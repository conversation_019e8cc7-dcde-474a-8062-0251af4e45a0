{"name": "vita", "private": true, "scripts": {"build": "turbo run build", "lint-staged": "lint-staged", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "prepare": "husky install", "clean": "rm -rf node_modules && rm -rf .turbo && pnpm recursive exec -- rm -rf node_modules && pnpm recursive exec -- rm -rf dist && pnpm recursive exec -- rm -rf .turbo", "mini:dev:h5": "pnpm --filter @baidu/vita-mini-app dev:h5", "mini:dev:swan": "pnpm --filter @baidu/vita-mini-app dev:swan", "mini:build:h5": "pnpm --filter @baidu/vita-mini-app build:h5", "mini:build:swan": "pnpm --filter @baidu/vita-mini-app build:swan", "mini:build:swan:pkg": "pnpm --filter @baidu/vita-mini-app build:swan:pkg"}, "resolutions": {"sass": "1.83.0", "sass-loader": "12.4.0"}, "dependencies": {"@babel/runtime": "^7.24.4", "@baidu/health-bos-uploader": "0.0.5", "@baidu/health-ubc": "1.0.14", "@baidu/health-utils": "1.1.3", "@baidu/wz-taro-tools-core": "^1.1.140", "@baidu/wz-taro-tools-icons": "^1.1.140", "@tarojs/components": "4.0.6", "@tarojs/helper": "4.0.6", "@tarojs/plugin-framework-react": "4.0.6", "@tarojs/plugin-platform-h5": "4.0.6", "@tarojs/plugin-platform-swan": "4.0.6", "@tarojs/plugin-platform-weapp": "4.0.6", "@tarojs/react": "4.0.6", "@tarojs/runtime": "4.0.6", "@tarojs/shared": "4.0.6", "@tarojs/taro": "4.0.6", "dayjs": "^1.11.6", "jotai": "^2.12.2", "lodash": "^4.17.21", "lottie-web": "^5.13.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/preset-react": "^7.26.3", "@baidu/health-ui": "1.0.40", "@baidu/health-ui-icon": "^1.0.39", "@baidu/health-version": "1.0.9", "@baidu/med-styles": "^1.0.33", "@baidu/mika-ubc": "1.1.61", "@baidu/vita-pages-common": "workspace:*", "@baidu/vita-pages-im": "workspace:*", "@baidu/vita-utils-shared": "workspace:*", "@baidu/vita-pages-portrait": "workspace:*", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "4.0.6", "@tarojs/plugin-indie": "^0.0.8", "@tarojs/taro-loader": "4.0.6", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "4.0.6", "@types/jest": "^29.3.1", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "babel-loader": "^10.0.0", "babel-plugin-import": "^1.13.8", "babel-plugin-lodash": "^3.3.4", "babel-preset-taro": "4.0.6", "classnames": "^2.5.1", "eslint": "^8.12.0", "eslint-config-taro": "4.0.6", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "husky": "^9.1.7", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "jotai-devtools": "0.12.0", "lint-staged": "^15.5.0", "postcss": "^8.4.18", "prettier": "^3.5.3", "react-refresh": "^0.11.0", "stylelint": "^16.17.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^37.0.0", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "turbo": "^2.4.4", "typescript": "5.8.2", "typescript-plugin-css-modules": "^5.1.0", "webpack": "5.91.0", "webpack-bundle-analyzer": "^4.10.2"}, "packageManager": "pnpm@9.6.0", "engines": {"node": ">=18", "pnpm": ">=9"}, "lint-staged": {"*.md": "prettier --write", "*.{ts,tsx,js,jsx,scss,less}": "prettier --write", "*.{ts,tsx,js,jsx}": ["eslint --fix"], "*.{scss,less,css}": ["stylelint --fix"]}}