import httpRequest from '@baidu/vita-pages-im/src/utils/basicAbility/comonRequest/common';
import {API_HOST} from '../../apis/host';
import type {SettingConfig, SettingRecommendConfParams} from './index.d';

/**
 *
 * @description 获取协议列表接口
 * @returns {Promise<SettingConfig>}
 */
export const getSettingConfig = () => {
    return httpRequest<SettingConfig>({
        url: `${API_HOST}/harbor/usercenter/useprotocol/list`,
        method: 'GET',
        data: {},
        isNeedLogin: false,
        isFirstScreen: true
    });
};

/**
 *
 * @description 设置个性化推荐配置
 * @returns {Promise<null>}
 */
export const setSettingRecommendConf = async (params: SettingRecommendConfParams) => {
    const [err, res] = await httpRequest<null>({
        url: `${API_HOST}/harbor/personalization/setconf/h5`,
        method: 'POST',
        data: params,
        isNeedLogin: true,
        isFirstScreen: false
    });

    if (err) {
        throw err;
    }

    return res?.data;
};
