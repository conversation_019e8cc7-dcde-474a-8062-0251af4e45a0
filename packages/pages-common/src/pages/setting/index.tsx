import CPageContainer from '@baidu/vita-pages-im/src/components/CPageContainer';
import {Switch, View} from '@tarojs/components';
import cx from 'classnames';
import {useEffect, useMemo, useRef, useState} from 'react';
import {HIcon} from '@baidu/health-ui';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';
import {getSettingConfig, setSettingRecommendConf} from '../../models/services/setting';
import type {SettingConfig} from '../../models/services/setting/index.d';
import {ubcCommonClkSend} from '../../../../pages-im/src/utils/generalFunction/ubc';
import styles from './index.module.less';
const pagePath = 'vita/pages/setting/index';

export default function Index() {
    const [settingConf, setSettingConf] = useState<SettingConfig | null>(null);
    const [switchChecked, setSwitchChecked] = useState<boolean>(false);

    const isRequesting = useRef<boolean>(false);

    const handleTapAgreements = () => {
        navigate({
            url: '/vita/pages/setting/terms',
            openType: 'navigate'
        });
        ubcCommonClkSend({
            value: 'terms',
            page: pagePath
        });
    };

    const switchChange = async e => {
        const isCheck = process.env.TARO_ENV === 'swan' ? e?.detail?.checked : e?.detail?.value;
        if (isRequesting.current) {
            return;
        }
        ubcCommonClkSend({
            value: 'personalization',
            ext: {
                product_info: {
                    status: isCheck
                }
            },
            page: pagePath
        });
        isRequesting.current = true;
        try {
            await setSettingRecommendConf({personalization: isCheck});
            setSwitchChecked(isCheck);
        } catch (error) {
            /* empty */
        } finally {
            isRequesting.current = false;
        }
    };

    useEffect(() => {
        const init = async () => {
            const [, res] = await getSettingConfig();
            res && setSettingConf(res.data);
            const switchChecked = res?.data?.setInfo.personalization ?? false;
            setSwitchChecked(switchChecked);
        };
        init();
    }, []);

    const renderSettingContent = useMemo(() => {
        if (!settingConf) {
            return null;
        }
        return (
            <>
                <View>
                    {(settingConf?.agreeList?.length ?? 0) > 0 && (
                        <View
                            className={cx('wz-flex wz-col-center wz-row-between', styles.recommend)}
                        >
                            <View>
                                <View className={cx('wz-fs-48', styles.recommendTextTitle)}>
                                    个性化推荐
                                </View>
                                <View
                                    className={cx(
                                        'wz-mt-30 wz-fs-42',
                                        styles.recommendTextSubTitle
                                    )}
                                >
                                    开启后，将根据个人兴趣偏好推荐内容
                                </View>
                            </View>
                            <View
                                className={`wz-flex wz-col-center wz-row-center ${styles.recommendSwitch}`}
                            >
                                <Switch
                                    color='#00C8C8'
                                    checked={switchChecked}
                                    onChange={switchChange}
                                />
                            </View>
                        </View>
                    )}
                </View>
                <View
                    className={cx(
                        'wz-mt-30 wz-flex wz-row-between wz-col-center',
                        styles.agreements
                    )}
                    onClick={handleTapAgreements}
                >
                    <View className={cx('wz-fs-48', styles.agreementsText)}>
                        {settingConf?.head.title}
                    </View>
                    <HIcon value='wise-right-arrow' color='#A7B4C5' size={48} />
                </View>
            </>
        );
    }, [settingConf, switchChecked]);

    return (
        <CPageContainer
            className={styles.container}
            topBarProps={{
                title: ' ',
                className: 'transparent',
                textColor: '#000311',
                blank: true,
                hideHome: true,
                titleLeftSlot: '设置'
            }}
        >
            <View className={styles.setting}>{renderSettingContent}</View>
        </CPageContainer>
    );
}
