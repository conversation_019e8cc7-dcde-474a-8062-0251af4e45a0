import CPageContainer from '@baidu/vita-pages-im/src/components/CPageContainer';
import {View} from '@tarojs/components';
import cx from 'classnames';
import {useEffect, useMemo, useState} from 'react';
import {HIcon} from '@baidu/health-ui';
import {navigate} from '@baidu/vita-pages-im/src/utils/basicAbility/commonNavigate';
import {getSettingConfig} from '../../../models/services/setting';
import type {SettingConfig} from '../../../models/services/setting/index.d';
import styles from './index.module.less';

export default function Terms() {
    const [settingConf, setSettingConf] = useState<SettingConfig | null>(null);

    useEffect(() => {
        const init = async () => {
            const [, res] = await getSettingConfig();
            res && setSettingConf(res.data);
        };
        init();
    }, []);

    const handleTapTerm = term => {
        let url = term.unifiUrl;
        if (url) {
            if (process.env.TARO_ENV === 'h5' || process.env.TARO_ENV === 'swan') {
                url = url.replace('pages/wz/agreement/index', 'wenzhen/pages/agreement/index');
            }
            if (process.env.TARO_ENV === 'weapp') {
                // 微信单独处理, 不需要登录态
                url = `${url}&notNeedLogin=1`;
            }
            navigate({
                url,
                openType: 'navigate'
            });
        }
    };

    const renderTermsContent = useMemo(() => {
        if (!settingConf) {
            return null;
        }
        return (
            <>
                {settingConf.agreeList?.map((terms, index) => (
                    <View key={index} className={styles.termList}>
                        {terms.item.map((term, idx) => (
                            <View
                                className={cx(
                                    'wz-flex wz-row-between wz-col-center',
                                    styles.termItem
                                )}
                                key={idx}
                                onClick={() => handleTapTerm(term)}
                            >
                                <View className={cx('wz-fs-48', styles.termItemText)}>
                                    {term.title}
                                </View>
                                <HIcon value='wise-right-arrow' color='#A7B4C5' size={48} />
                            </View>
                        ))}
                    </View>
                ))}
            </>
        );
    }, [settingConf]);

    return (
        <CPageContainer
            className={styles.container}
            topBarProps={{
                title: ' ',
                className: 'transparent',
                textColor: '#000311',
                blank: true,
                hideHome: true,
                titleLeftSlot: '使用协议'
            }}
        >
            <View className={styles.agreeList}>{renderTermsContent}</View>
        </CPageContainer>
    );
}
