/**
 * @file 长按menu hook
 * <AUTHOR>
 */
import {useState, useCallback} from 'react';
import {vibrateShort} from '@tarojs/taro';

interface IPositionProps {
    x?: number;
    y?: number;
}

export const useLongPressMenu = () => {
    const [menuOpenStatus, setMenuOpenStatus] = useState(false);
    const [menuPosition, setMenuPosition] = useState<IPositionProps>({x: 0, y: 0});

    /**
     * 展示菜单
     */
    const openMenu = useCallback((position?: IPositionProps, needVibrateShort?: boolean) => {
        position && setMenuPosition(position);
        setMenuOpenStatus(true);
        // 添加震动反馈
        if (needVibrateShort) {
            vibrateShort({
                type: 'medium'
            });
        }
    }, []);

    /**
     * 关闭菜单
     */
    const closeMenu = useCallback(() => {
        setMenuOpenStatus(false);
    }, []);

    return {
        menuOpenStatus,
        menuPosition,
        closeMenu,
        openMenu
    };
};
