import { getCurrentPages, showModal, setClipboardData } from '@tarojs/taro';

import { H5_HOST } from '../../../../models/apis/host';

import { parseURL } from '../../../../utils/generalFunction/urlRelated';
import { getQueryObject, decodeUntilFullyDecoded } from '../../../../utils/queryString';

import type { OpenType } from '../main';

// 重镜像地址 map
const redirectMap = {
    'pages/mall-sub/recipel-detail/index': 'pages/recipeldetail/index', // 处方详情
    'pages/wz/list/index': 'pages/order/list/index', // 订单列表
    'pages/brand/search/index': 'pages/doctor/list/index', // 医生列表
    'pages/wz/experthome/index': 'decision/pages/expert/newHome/index', // 兼容老路由映射医生主页
    'pages/wz/order/index': 'pages/order/detail/index', // 订单详情
    'pages/wz/triage/index': 'pages/triage/index', // 诊前
    'pages/patient/registerdetail/index': 'pages/patientRegistration/detail/index', // 患者报到审核
    'pages/patient/patientform/index': 'pages/patientRegistration/form/index', // 患者报到页
    'pages/guahao/orderdetail/index': 'pages/guahao/orderDetail/index', // 挂号详情
    'pages/wz-sub/feedback/index': 'pages/feedback/index', // 投诉反馈
    'pages/patient/mydoctor/index': 'pages/serviceRecord/list/index', // 我的医生列表
    'pages/patient/servicelist/index': 'pages/serviceRecord/detail/index' // 我的医生详情-服务记录
};

// wenzhen中已重构的页面，第三方再次重构页面
const wenzhenRedirectToOther = {
    'pages/doctor/detail/index': 'decision/pages/expert/newHome/index' // 医生详情
};

const needTransformWebViewPaths = [
    'pages/brand-sub/agreementlist/index',
    'pages/wz/followplan/index', // 随访计划
    'pages/speTopic/activity/sign-up/index', // 患者招募
    'wz/pages/wz-sub/teamhome/index',
    'pages/act/specialCenter/index', // 资质页
    'uha/ask/pages/im/index'
];

const webviewPath = {
    'pages/brand-sub/agreementlist/index': `${H5_HOST}/#/pages/brand-sub/agreementlist/index`,
    'pages/wz/followplan/index': `${H5_HOST}/#/pages/wz/followplan/index`,
    'pages/speTopic/activity/sign-up/index': `${H5_HOST}/hzzm/pages/speTopic/activity/sign-up/index`,
    'wz/pages/wz-sub/teamhome/index': `${H5_HOST}/#/pages/wz-sub/teamhome/index`,
    'pages/act/specialCenter/index': `${H5_HOST}/#/pages/act/specialCenter/index`,
    'uha/ask/pages/im/index': `${H5_HOST}/uha/ask/pages/im/index`
};

// 专科服务包
const servicePacketPath = {
    // 我的权益页
    'wwwhos/h5#/consumer/mySpecServiceDetail': 'vas/pages/mineEquity/index',
    // 服务包医生主页
    'wwwhos/h5#/consumer/doctorHome': 'vas/pages/packageDetail/index',
    // 订单列表
    'wwwhos/h5#/consumer/mySpecialistServices': 'vas/pages/orderList/index',
    // 服务包详情
    'wwwhos/h5#/consumer/servicePackageDetail': 'vas/pages/packageDetail/index'
};

// 专科服务包需要重镜像微信小程序的页面 map
const servicePacketPathToMini = {
    [`${H5_HOST}/vas/pages/packageDetail/index`]: 'vas/pages/packageDetail/index'
};

const webviewRouterPath = '/pages/common/webpage/index';
// webview跳转白名单
const webviewHostList = [
    'https://expert.baidu.com',
    'https://jiankang-dev.baidu.com',
    'https://jiankang-fenji.baidu.com',
    'https://jiankang-test.baidu.com',
    'https://jiankang.baidu.com',
    'https://mbd.baidu.com',
    'https://med-fe.cdn.bcebos.com',
    'https://muzhi.baidu.com',
    'https://muzhi-dev.baidu.com',
    'https://passport.baidu.com',
    'https://wappass.baidu.com',
    'https://mp.weixin.qq.com',
    'https://health.baidu.com',
    'https://live.baidu.com',
    'https://baijiahao.baidu.com'
];

/**
 *
 * @description 处理微信跳转相关 url
 * @param url
 * @returns
 */
const editUrl = (url: string) => {
    let resUrl = url;
    // 去掉旧页面中，接口返回/#
    if (resUrl.startsWith('/#')) {
        resUrl = resUrl.replace('/#', '');
    }

    // wenzhen中已重构的页面，第三方再次重构页面
    const isWenzhenPage = Object.keys(wenzhenRedirectToOther).find(i => {
        if (url.includes(i)) {
            // 后端返回可能带/wenzhen,wenzhen,不带wenzhen
            const resTmp = url.replace(/^\/?wenzhen/, '');
            resUrl = resTmp.replace(i, wenzhenRedirectToOther[i]);

            return resUrl;
        }
    });

    if (isWenzhenPage) {
        const resTmp = url.replace(/^\/?wenzhen/, '');

        return resTmp.replace(isWenzhenPage, wenzhenRedirectToOther[isWenzhenPage]);
    }

    const match: string[] = /\/?(wenzhen|guahao)\/pages([\w-/]+)/.exec(url) || [];
    if (match && match.length > 2) {
        // match = ['/wenzhen/pages/triage/index', 'wenzhen', '/triage/index']
        // eslint-disable-next-line prefer-destructuring
        const suffix = match[2];
        // 获取原始 URL 中的查询参数部分
        const queryIndex = url.indexOf('?');
        const queryPart: string = queryIndex !== -1 ? url.substring(queryIndex + 1) : '';

        // 构建最终的 URL，包括查询参数
        return queryPart ? `/pages${suffix}?${queryPart}` : `/pages${suffix}`;
    }

    // 对老页面进行重镜像
    Object.keys(redirectMap).some(i => {
        if (url.includes(i)) {
            resUrl = url.replace(i, redirectMap[i]);

            return true;
        }
    });

    // 对服务包老 H5 地址进行重镜像
    Object.keys(servicePacketPath).some(i => {
        if (url.includes(i)) {
            const h5Url
                = url.startsWith('http') || url.startsWith('https') ? url.substring(url.indexOf('wwwhos/h5#/')) : '';
            // 页面地址，用于匹配使用
            const h5Path = h5Url.includes('?') ? h5Url.split('?')[0] : h5Url;
            // 页面参数，用于拼接url中原带参数
            const h5Params = h5Url.includes('?') ? `?${h5Url.split('?')[1]}` : '';

            resUrl = `${h5Path.replace(i, servicePacketPath[i])}${h5Params}`;

            return true;
        }
    });

    // 对服务包 H5 地址进行重镜像
    Object.keys(servicePacketPathToMini).some(i => {
        if (url.includes(i)) {
            resUrl = `${url.replace(i, servicePacketPathToMini[i])}`;

            return true;
        }
    });

    // 补全 pages 前的 /
    if (!resUrl.startsWith('http') && !resUrl.startsWith('/')) {
        return `/${resUrl}`;
    }

    return resUrl;
};

/**
 *
 * @description 转换成 webview 需要访问的地址
 * @param param0
 */
const transformWebViewUrl = ({ url, key }: { url: string; key: string }) => {
    try {
        const repString = url?.startsWith('/') ? url : `/${url}`;
        let fullUrl = repString.replace(`/${key}`, webviewPath[key]);
        fullUrl = `${fullUrl}&scene=wx_wenzhen`;

        return `/pages/common/webpage/index?url=${encodeURIComponent(fullUrl)}`;
    } catch (err) {
        console.error('transformWebViewUrl 出错：', err);

        return url;
    }
};

export const commonRedirectUrlFn = ({ url, openType }): Promise<{ url: string; openType: OpenType }> => {
    return new Promise((resolve, reject) => {
        try {
            let reUrl = url;
            let resOpenType = openType;

            reUrl = editUrl(url);

            // 微信小程序内，识别到 http 全路径地址，则跳转 webview
            if (reUrl.startsWith('http') || reUrl.startsWith('https')) {
                const { pathname } = parseURL(reUrl);
                if (reUrl.includes(pathname)) {
                    reUrl = `${webviewRouterPath}?url=${encodeURIComponent(reUrl)}`;
                }
            }

            // webview need check white list
            if (reUrl.startsWith(webviewRouterPath)) {
                const query = getQueryObject(reUrl);
                if (query.url) {
                    const decodeUrl = decodeUntilFullyDecoded(query.url);
                    const isInWhiteList = webviewHostList.some(host => decodeUrl.startsWith(host));

                    if (!isInWhiteList) {
                        showModal({
                            title: '温馨提示',
                            content: '当前消息无法在微信小程序内浏览，您可复制链接前往浏览器打开',
                            confirmText: '复制链接',
                            confirmColor: '#00C8C8',
                            success: res => {
                                if (res.confirm) {
                                    setClipboardData({
                                        data: decodeUrl
                                    });
                                }
                            }
                        });
                        reject(new Error('webview 跳转地址不在白名单中'));
                    }
                }
            }

            needTransformWebViewPaths.some(i => {
                if (url?.includes(i)) {
                    reUrl = transformWebViewUrl({ url: reUrl, key: i });

                    return true;
                }
            });

            // 兜底机制
            if (getCurrentPages().length === 10) {
                resOpenType = 'relaunch';
            }
            resolve({
                url: reUrl,
                openType: resOpenType
            });
        } catch (err) {
            reject(err);
        }
    });
};
