import {getCurrentPage} from '@baidu/vita-utils-shared';
import {requirePlugin, navigateTo, setStorageSync, eventCenter, login} from '@tarojs/taro';
import {showToast} from '../../customShowToast';

import {WX_LOGIN_PATH} from '../../../constants/path';
import {APP_ID_MAP} from '../../../constants/common';
import {navigate} from '../../basicAbility/commonNavigate';
import {ubcCommonViewSend} from '../../generalFunction/ubc';

// import { reportWeAnalysisEvent } from '../../generalFunction/reportWeAnalysisEvent';
import {getWechatPassUserInfo} from '../../generalFunction/login';

import type {UserConfParams, WxPassPluginInstance, PassInitFnParams} from './index.d';

const {WX_APP_ENV} = process.env;
const loginSuccessfullyLandingPage = '/pages/wxHome/index';

// 微信小程序插件实例
let pluginInstance: WxPassPluginInstance | null = null;
const middlePageForPassPageLogin = '/pages/common/loginMiddlePage/index';

/**
 *
 * @description 初始化微信 pass 登录插件（已与 pass 确认，可重复 init 仅处理公共参数）
 * @returns
 */
export const initPlugin = (initParams?: PassInitFnParams): WxPassPluginInstance | null => {
    try {
        const plugin: WxPassPluginInstance = requirePlugin('pass-plugin');
        const {backUrl} = initParams || {};

        const middlePath = backUrl
            ? `${middlePageForPassPageLogin}?url=${encodeURIComponent(backUrl)}`
            : '';

        plugin?.initPass({
            // 产品线标识，若无tpl，可在此申请 http://dev.passport.baidu.com/authorize/index
            tpl: 'hcgweapp',
            // 小程序appid
            appid: APP_ID_MAP[WX_APP_ENV as string],
            // 回跳地址 示例：/pages/index/index  from兼容风控登录处理
            backu: middlePath || loginSuccessfullyLandingPage,
            // 是否支持游客登录
            supportGuestAccount: 0,
            // 短信登录是否支持三方登录
            authsite: 0,
            isAgree: 1
        });

        pluginInstance = plugin;

        return plugin;
    } catch (err) {
        return null;
    }
};

/**
 *
 * @description 处理微信登录逻辑
 * @param userConf
 */
const handleWxLogin = async (userConf: UserConfParams) => {
    return new Promise((resolve, reject) => {
        // eslint-disable-next-line no-console
        console.info('登录逻辑开始 2/3 调用 pass 插件获取 BDUSS 执行, 接收配置如下：', userConf);

        const {params = {} as UserConfParams['params'], successCallback, failCallback} = userConf;
        try {
            if (!params) {
                failCallback?.();
                reject();

                return false;
            }

            if (!params.detail?.encryptedData) {
                showToast({
                    title: '授权失败',
                    icon: 'none'
                });
                ubcCommonViewSend({
                    value: 'wx_login_refund'
                });
                failCallback?.();
                reject();

                return false;
            }

            if (params.wxCode && pluginInstance) {
                params.detail.appid = APP_ID_MAP[WX_APP_ENV as string];
                params.detail.wxCode = params.wxCode;

                pluginInstance?.mobileDirectLogin?.(
                    params.detail,
                    async () => {
                        // 成功回调
                        await bindWxWithPass();
                        // 登录成功
                        ubcCommonViewSend({
                            value: 'wx_login_success'
                        });
                        successCallback?.();
                        resolve(null);
                    },
                    data => {
                        if (data && data.jump === 1) {
                            const ignoreList = ['jump', 'page'];
                            let query = '';
                            // eslint-disable-next-line max-nested-callbacks
                            Object.keys(data).forEach(key => {
                                if (!ignoreList.includes[key]) {
                                    query += `${query.indexOf('?') > -1 ? '&' : '?'}${key}=${
                                        decodeURIComponent(data[key]) || ''
                                    }`;
                                }
                            });
                            failCallback?.();
                            reject();

                            navigateTo({
                                url: `plugin://pass-plugin/${data.page + query}`
                            });
                        }

                        // reportWeAnalysisEvent({
                        //     event: 'login_authorize_mobile',
                        //     properties: {
                        //         is_error: 1,
                        //         interaction_of_login: 'unknown',
                        //         login_error_query: '',
                        //         login_error_tips: `pass 插件授权手机号失败：${JSON.stringify(data)}`
                        //     }
                        // });
                    }
                );
            }
        } catch (err) {
            console.error('handleLogin 出错：', err);
            showToast({
                title: '登录失败',
                icon: 'none'
            });
            failCallback?.();
            reject();

            // reportWeAnalysisEvent({
            //     event: 'login_error',
            //     properties: {
            //         interaction_of_login: 'unknown',
            //         login_error_query: '',
            //         login_error_tips: `handleWxLogin 执行出错：${JSON.stringify(err)}`
            //     }
            // });
        } finally {
            // 允许授权
            ubcCommonViewSend({
                value: 'wx_login_sure'
            });
        }
    });
};

/**
 *
 * @description 绑定微信账号和手百账号
 */
export const bindWxWithPass = (arg?: {bduss: string}) => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
        try {
            // eslint-disable-next-line no-console
            console.info('登录逻辑开始 3/3 绑定 BDUSS；');

            const userInfo = arg ? JSON.stringify(arg) : pluginInstance?.getUserInfo?.();
            const userInfoValue = userInfo || '{}';
            // eslint-disable-next-line no-console
            console.info('登录逻辑开始 3/3 绑定 BDUSS：成功获取 userInfo', userInfo);
            setStorageSync('pass-plugin-userInfo', userInfoValue);

            if (WX_APP_ENV) {
                const plugin = requirePlugin('baidu-health-login-transfer');
                const bduss = JSON.parse(userInfo as string)?.bduss || '';

                // 校验微信账号是否绑定
                const res = await plugin?.checkWxAccountBindingStatus({
                    getBdussFunction: () => {
                        return {
                            bduss
                        };
                    }
                });

                // // 未绑定进行绑定逻辑
                if (String(res?.status) !== '0') {
                    let bindRes;
                    if (process.env.WX_APP_ENV === 'jiayi') {
                        const {code} = await login();

                        bindRes = await plugin?.bindAccountSysWithCode({
                            code,
                            getBdussFunction: () => {
                                return {
                                    bduss
                                };
                            }
                        });
                    } else {
                        bindRes = await plugin?.bindAccountSys({
                            getBdussFunction: () => {
                                return {
                                    bduss
                                };
                            }
                        });
                        // eslint-disable-next-line no-console
                        console.info('checkWxAccountBindingStatus bindRes ========', bindRes);
                    }

                    // eslint-disable-next-line no-console
                    console.info('登录逻辑完成 微信pass 绑定成功！！！');
                    resolve(null);

                    // reportWeAnalysisEvent({
                    //     event: 'login_bind_account',
                    //     properties: {
                    //         is_error: 0,
                    //         interaction_of_login: 'unknown',
                    //         login_error_query: '',
                    //         login_error_tips: `pass 登录绑定手机号成功：${JSON.stringify(bindRes)}`
                    //     }
                    // });

                    // 登录成功后通过广播通知状态变更
                    eventCenter.trigger('wxLoginStatusChange', {type: 'login'});
                } else {
                    // eslint-disable-next-line no-console
                    console.info('登录逻辑完成 微信pass已绑定无需重新绑定');
                    resolve(null);
                    // 登录成功后通过广播通知状态变更
                    eventCenter.trigger('wxLoginStatusChange', {type: 'login'});

                    // reportWeAnalysisEvent({
                    //     event: 'login_bind_account',
                    //     properties: {
                    //         is_error: 1,
                    //         interaction_of_login: 'unknown',
                    //         login_error_query: '',
                    //         login_error_tips: `pass 登录绑定手机号请求失败：${JSON.stringify(res)}`
                    //     }
                    // });
                }

                return;
            }
            reject(new Error('未找到微信环境'));
        } catch (err) {
            console.error('loginBind 出错：', err);
            // reportWeAnalysisEvent({
            //     event: 'login_error',
            //     properties: {
            //         interaction_of_login: 'unknown',
            //         login_error_query: '',
            //         login_error_tips: `loginBind 执行出错：${JSON.stringify(err)}`
            //     }
            // });

            // reportWeAnalysisEvent({
            //     event: 'login_bind_account',
            //     properties: {
            //         is_error: 1,
            //         interaction_of_login: 'unknown',
            //         login_error_query: '',
            //         login_error_tips: `pass 登录绑定手机号失败：${JSON.stringify(err)}`
            //     }
            // });

            reject(err);
        }
    });
};

/**
 * 微信小程序调起登录页面
 */
export const loginApi: (userConf?: UserConfParams) => Promise<null> = (userConf = {}) => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
        try {
            const {interactionType, backUrl} = userConf;
            initPlugin({
                backUrl: backUrl || ''
            });
            const curPage = getCurrentPage();

            // eslint-disable-next-line no-console
            console.info('登录逻辑开始 1/3 Api 执行, 接收配置如下：', userConf);

            if (
                interactionType === 'popup' ||
                (interactionType === 'page' && curPage.route === WX_LOGIN_PATH)
            ) {
                // 触发登录逻辑
                await handleWxLogin(userConf);

                // reportWeAnalysisEvent({
                //     event: 'login_trigger_loginapi',
                //     properties: {
                //         is_error: 0,
                //         interaction_of_login: 'unknown',
                //         login_error_query: '',
                //         login_error_tips: `触发登录流程：${JSON.stringify(userConf)}`
                //     }
                // });
            } else if (curPage.route !== WX_LOGIN_PATH) {
                // 兜底，如果 loginApi 在非登录页调用，则跳转至登录页
                let to = `/${curPage?.path}`;
                if (userConf?.href) {
                    to = userConf.href;
                }

                const msg = {
                    PageStatus: 1,
                    to,
                    sourcePage: curPage?.path
                };

                // reportWeAnalysisEvent({
                //     event: 'login_trigger_loginapi',
                //     properties: {
                //         is_error: 0,
                //         interaction_of_login: 'unknown',
                //         login_error_query: '',
                //         login_error_tips: `触发登录流程，自动跳转到登录页：${JSON.stringify(userConf)}`
                //     }
                // });

                navigate({
                    url: WX_LOGIN_PATH,
                    openType: 'navigate',
                    params: {
                        msg: encodeURIComponent(JSON.stringify(msg))
                    }
                });
            }
            resolve(null);
        } catch (err) {
            console.error('loginApi 出错：', err);
            reject(err);

            // reportWeAnalysisEvent({
            //     event: 'login_trigger_loginapi',
            //     properties: {
            //         is_error: 0,
            //         interaction_of_login: 'unknown',
            //         login_error_query: '',
            //         login_error_tips: `触发登录流程，自动跳转到登录页：${JSON.stringify(
            //             userConf
            //         )}，执行出错：${JSON.stringify(err)}`
            //     }
            // });
        }
    });
};

/**
 * 检查微信账号绑定状态
 *
 * @returns 返回一个Promise对象，用于处理异步操作
 */
export const checkWxAccountBindStatus = () => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
        try {
            const plugin = requirePlugin('baidu-health-login-transfer');
            const userInfo = await getWechatPassUserInfo();

            const r = await plugin?.checkWxAccountBindingStatus({
                autoBind: true,
                getBdussFunction: () => {
                    return {
                        bduss: userInfo?.bduss
                    };
                }
            });
            resolve(r);
        } catch (e) {
            reject(e);
        }
    });
};

/**
 * 当前百度账号是否已经关注了百度健康微信公众号
 * @returns 返回Promise对象，包含isBind字段表示是否绑定成功
 */
export const checkWxAccountBindStatusOfNetHospitalService = (): Promise<{isBind: boolean}> => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
        try {
            const plugin = requirePlugin('baidu-health-login-transfer');
            const userInfo = await getWechatPassUserInfo();

            const r = await plugin?.checkWxAccountBindingStatus({
                wxAppKey: 'netHospitalService',
                getBdussFunction: () => {
                    return {
                        bduss: userInfo?.bduss
                    };
                }
            });
            resolve({
                isBind: String(r.status) === '0'
            });
        } catch (e) {
            reject(e);
        }
    });
};

/**
 * 当前百度账号是否已经关注了百度健康微信公众号
 * @returns 返回Promise对象，包含isBind字段表示是否绑定成功
 */
export const checkWxAccountBindingStatusBeforePayment = (): Promise<null> => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
        try {
            const plugin = requirePlugin('baidu-health-login-transfer');
            const userInfo = await getWechatPassUserInfo();

            await plugin?.checkWxAccountBindingStatusBeforePayment({
                getBdussFunction: () => {
                    return {
                        bduss: userInfo?.bduss
                    };
                }
            });
            resolve(null);
        } catch (e) {
            reject(e);
        }
    });
};
