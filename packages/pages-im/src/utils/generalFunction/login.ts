import { getStorageSync, setStorageSync } from '@tarojs/taro';

import store from '../../globalDataStore/index';

import { loginApi } from './loginApi';
import { UserConfParams } from './loginApi/index.d';

/**
 * 跳转登录页
 *
 * @param {Object} userConf 用户配置，详见：http://wiki.baidu.com/pages/viewpage.action?pageId=537399885
 * @param {number} userConf.type        登录类型：1-账号密码；2-手机号验证码(H5使用)
 * @param {number} userConf.params      统计参数（H5使用）
 * @param {number} userConf.href        登录成功后跳转地址（通用）
 * @param {number} userConf.openType    登录成功后跳转页面方式（swan使用）
 * @param {number} userConf.isReplaceUrl 进入登录页是否replaceState（H5使用）
 * @param {boolean} userConf.loginStatus 登录成功状态，true：成功 / false： 失败
 */
export const toLoginWithApi: (userConf?: UserConfParams) => Promise<unknown> = (userConf = {}) => {
    return loginApi(userConf);
};

/**
 * 微信公众号中登录，跳转中台微信登录中间页
 *
 * @description 文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/-q-E-8qyAN/wiuYw5jtAtZBqT
 * @param {Object} userConf 用户登录配置参数，详见toLoginPage方法注释
 * @param {string} appid 微信公众号appid
 */
export const toWxLoginPage = (userConf: { [k in string]: string } = {}, appid = '') => {
    const { href } = userConf || {};
    const target = href
        ? encodeURIComponent(href?.indexOf('http') === 0 ? href : `${window.location.origin}${href as string}`)
        : encodeURIComponent(window.location.href);
    const from = 'wx_web';
    const redirectUri = `https://expert.baidu.com/wenzhen/pages/common/wxauth/index?appid=${appid}&target=${target}&from=${from}`;

    window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(
        redirectUri
    )}&response_type=code&scope=snsapi_base#wechat_redirect`;
};

/**
 * 设置登录状态
 */
export function setLoginStatus(status = -1) {
    const loginStatus = store.get('loginStatus');
    if (loginStatus) {
        loginStatus.status = status;
        store.set('loginStatus', loginStatus);
    }
}

/**
 * 获取微信小程序存储的Pass用户信息
 */
export function getWechatPassUserInfo() {
    try {
        const userInfo = JSON.parse(getStorageSync('pass-plugin-userInfo') || '{}');

        return userInfo;
    } catch (error) {
        console.error('getWechatPassUserInfo error', error);

        return getStorageSync('pass-plugin-userInfo');
    }
}

/**
 *
 * @description 清除微信小程序存储的Pass用户信息
 */
export function clearWechatPassUserInfo() {
    setStorageSync('pass-plugin-userInfo', JSON.stringify({}));
}
