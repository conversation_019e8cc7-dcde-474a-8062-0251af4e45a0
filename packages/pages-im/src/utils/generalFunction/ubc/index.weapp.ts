import Taro, {addInterceptor} from '@tarojs/taro';
import {isObject} from '@baidu/health-utils';
import {init, send, customizeTrack} from '@baidu/health-ubc/lib/index.weapp.esm';

import {getCurrentPage} from '@baidu/vita-utils-shared';
import {getCommonSysInfo, getSystemInfo} from '../../taro';

import globalData from '../../../globalDataStore/globalData';

import {SWAN_NAME_MAP} from '../../../constants/common';
import {getReduxStore} from '../../getReduxStoreData';
import {getWechatPassUserInfo} from '../../generalFunction/login';

import {getPageID} from './common';
import type {TrackCommonSendParams, UbcCommonSendParams} from './index.d';

const sys = getSystemInfo();
const {brand = '', model = '', platform = '', system = ''} = sys;

const isDev = process.env.NODE_ENV === 'development';
const taroRuntime = require('@tarojs/runtime');

// 获取自定义cookie
const setCustomCookie = () => {
    const bduss = getWechatPassUserInfo()?.bduss;
    if (bduss) {
        return {
            BDUSS: bduss
        };
    }

    return {};
};

const ubcInit = () => async baseInfo => {
    const sid = await getCommonSysInfo().sid;

    init('default', 12865, {
        sid,
        showLog: isDev,
        testMode: isDev, // 测试环境下数据不会落库
        // testMode: false, // 测试环境下数据会落库
        strictMode: true,
        appVersion: '2.0.0',
        scene: baseInfo?.scene || '',
        enableTracking: true,
        swan_name: SWAN_NAME_MAP[process.env.WX_APP_ENV || 'wenzhen'].weapp,
        pd: 'wenzhen-mini-app',
        trackingOptions: {
            trackClick: true,
            trackXHR: {
                type: 'taro',
                interceptor: addInterceptor
            },
            trackPageVisibility: true,
            trackCustomize: true
        },
        taro: taroRuntime,
        pageSourceKeys: ['loId'],
        getCurrentPages: () => {
            const pages = Taro.getCurrentPages();

            return (pages && pages.length > 0 && pages.map(i => ({route: i?.route}))) || [];
        },
        setCustomCookie
    });
};

const ubcSend = () => async option => {
    let page = option.page || globalData.get('page')?.pageId;
    if (!page) {
        const {route} = getCurrentPage();
        if (route) {
            page = route;
            // let urlPath = TARO_UBC_PATH_MAP[route];
            // urlPath && urlPath.startsWith('/') && (page = urlPath.slice(1));
        }
    }

    isObject(option) &&
        send(
            {
                ...option,
                page,
                setCustomCookie
            },
            'default'
        );
};

// 通用的 ext 埋点值
const commonExtVal = {
    page_version: '2.0.0'
};

/**
 *
 * @description ubc 通用 view 事件埋点方法
 * @param params
 */
export const ubcCommonViewSend = (params: UbcCommonSendParams) => {
    if (!params?.value) {
        console.error('ubcCommonViewSend 发送出错：传参错误', params);

        return;
    }
    const storeData = getReduxStore();

    const page = params?.page || globalData.get('page')?.pageId || getCurrentPage()?.route;
    const pageIDs = getPageID(storeData, page);
    const product_info = params?.ext?.product_info || {};

    const ext = {
        ...pageIDs,
        ...params?.ext,
        ...commonExtVal,
        product_info: {
            brand,
            model,
            platform,
            system,
            ...storeData,
            ...product_info
        }
    };

    send(
        {
            ...params,
            ext,
            page,
            type: 'view',
            setCustomCookie
        },
        'default'
    );
};

/**
 *
 * @description ubc 通用 clk 事件埋点方法
 * @param params
 */
export const ubcCommonClkSend = (params: UbcCommonSendParams) => {
    if (!params?.value) {
        console.error('ubcCommonClkSend 发送出错：传参错误', params);

        return;
    }
    const storeData = getReduxStore();
    const page = params?.page || globalData.get('page')?.pageId || getCurrentPage()?.route;
    const pageIDs = getPageID(storeData, page);
    const product_info = params?.ext?.product_info || {};

    const ext = {
        ...pageIDs,
        ...params?.ext,
        ...commonExtVal,
        product_info: {
            brand,
            model,
            platform,
            system,
            ...storeData,
            ...product_info
        }
    };

    send(
        {
            ...params,
            ext,
            page,
            setCustomCookie,
            type: 'clk'
        },
        'default'
    );
};

/**
 *
 * @description ubc 通用 timing 事件埋点方法
 * @param params
 */
export const ubcCommonTimingSend = (params: UbcCommonSendParams) => {
    if (!params?.value) {
        console.error('ubcCommonClkSend 发送出错：传参错误', params);

        return;
    }
    const storeData = getReduxStore();
    const page = params?.page || globalData.get('page')?.pageId || getCurrentPage()?.route;
    const pageIDs = getPageID(storeData, page);
    const product_info = params?.ext?.product_info || {};

    const ext = {
        ...pageIDs,
        ...params?.ext,
        ...commonExtVal,
        product_info: {
            brand,
            model,
            platform,
            system,
            ...storeData,
            ...product_info
        }
    };

    send(
        {
            ...params,
            ext,
            page,
            setCustomCookie,
            type: 'timing'
        },
        'default'
    );
};

/**
 *
 * @description track 自定义事件埋点方法
 * @param params
 */
export const trackCustomizeSend = (params: TrackCommonSendParams) => {
    customizeTrack(params);
};

export const ubcFn = {
    init: ubcInit(),
    send: ubcSend()
};

export const ubc = {
    ubcFn
};
