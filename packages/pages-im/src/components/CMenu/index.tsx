/**
 * @file 右上角菜单
 * <AUTHOR>
 */
import {View} from '@tarojs/components';
import {type FC, memo, useState, useCallback, useEffect} from 'react';
import cx from 'classnames';
import {debounce} from 'lodash-es';
import {pxTransform, eventCenter} from '@tarojs/taro';
import {WiseUser, WiseRemark, WiseEdit} from '@baidu/wz-taro-tools-icons';
import {WImage} from '@baidu/wz-taro-tools-core';
import {CLoginButton} from '@baidu/vita-ui-cards-common';

import {navigate} from '../../utils/basicAbility/commonNavigate';
import {ubcCommonClkSend} from '../../../src/utils/generalFunction/ubc';
import {isEmpty} from '../../utils';
import HistoryRecordPopup from '../HistoryRecordPopup';
import {useCreateSessionhook} from '../../hooks/triageStream/session';

import styles from './index.module.less';

import {ICMenuProps, menuItem} from './index.d';

const DEBOUNCETIME = 1000;
const initLoginStatus = {
    isLogin: false,
    status: 'init'
};

const renderIcon = size => {
    const historyIcon = 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiHistoryIcon.png';
    const myOrderIcon = 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiMyOrderIcon.png';
    const createIcon = 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiCreateIcon.png';

    return {
        user: <WiseUser size={size} />,
        question: <WiseRemark size={size} />,
        edit: <WiseEdit size={size} />,
        create: (
            <WImage
                src={createIcon}
                style={{width: pxTransform(size), height: pxTransform(size)}}
            />
        ),
        history: (
            <WImage
                src={historyIcon}
                style={{width: pxTransform(size), height: pxTransform(size)}}
            />
        ),
        myOrder: (
            <WImage
                src={myOrderIcon}
                style={{width: pxTransform(size), height: pxTransform(size)}}
            />
        )
    };
};

const CMenu: FC<ICMenuProps> = props => {
    console.info('zzzz', props);
    const {menu, menuButtonWidth = 0, isLogin} = props;

    const [type, setType] = useState('');
    const [open, setOpen] = useState(false);
    const {createSession} = useCreateSessionhook();

    

    const handleLogin = useCallback(async (onLoginCallback?: menuItem['onLoginCallback']) => {
        if (initLoginStatus?.status === 'updateed' && !initLoginStatus?.isLogin) {
            await onLoginCallback?.();
            initLoginStatus.isLogin = true;
        }
    }, []);

    const handleTypeEvent = useCallback(
        item => {
            const {type} = item;
            const typeMap = {
                history: () => setOpen(true),
                navigate: () => handerClick(item),
                createSession: () => createSession()
            };
            typeMap[type] && typeMap[type]();
        },
        [createSession]
    );

    /**
     * 处理操作按钮点击事件
     * @param {Object} actionInfo - 动作信息对象
     */
    const handleClickBtn = useCallback(
        async (item: menuItem, onLoginCallback?: menuItem['onLoginCallback']) => {
            const {type, logValue, isNeedLogin = false} = item;

            type && setType(type);
            // 若在语音播报 则关闭
            eventCenter.trigger('stopTTS');

            if (isNeedLogin && !isLogin) {
                await Promise.resolve(handleLogin(onLoginCallback));
            }
            handleTypeEvent(item);

            logValue &&
                ubcCommonClkSend({
                    value: logValue
                });
        },
        []
    );

    const handerClick = item => {
        navigate({
            url: item.url,
            openType: 'navigate'
        });
    };

    useEffect(() => {
        if (initLoginStatus?.status === 'init') {
            initLoginStatus.isLogin = isLogin;
            initLoginStatus.status = 'updateed';
        }
    }, []);

    const renderSingleMenu = item => {
        const {isNeedLogin = false, icon = ''} = item || {};
        return isNeedLogin ? (
            <CLoginButton
                isLogin={isLogin}
                closeShowNewUserTag={true}
                useH5CodeLogin={true}
                onLoginFail={error => {
                    console.error('error', error);
                }}
                onLoginSuccess={debounce(
                    () => {
                        handleClickBtn(item, item.onLoginCallback);
                    },
                    DEBOUNCETIME,
                    {leading: true, trailing: false}
                )}
            >
                <View
                    className={cx(
                        styles.menuContent,
                        'wz-flex wz-col-center wz-row-center wz-mtb-12 wz-ml-24'
                    )}
                >
                    {icon && renderIcon(60)[icon]}
                </View>
            </CLoginButton>
        ) : (
            <View className='wz-flex' onClick={() => handleClickBtn(item)}>
                <View
                    className={cx(
                        styles.menuContent,
                        'wz-flex wz-col-center wz-row-center wz-mtb-12 wz-ml-24'
                    )}
                >
                    {icon && renderIcon(60)[icon]}
                </View>
            </View>
        );
    };

    return isEmpty(menu) ? null : (
        <View style={{marginRight: menuButtonWidth + 'px'}}>
            <View className='wz-flex'>
                {menu &&
                    menu.map((item, index) => {
                        return <View key={index}>{renderSingleMenu(item)}</View>;
                    })}
            </View>
            {type === 'history' && <HistoryRecordPopup open={open} setOpen={setOpen} />}
        </View>
    );
};

export default memo(CMenu);
