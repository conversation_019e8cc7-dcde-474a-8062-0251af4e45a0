import {createCanvasContext, createSelectorQuery} from '@tarojs/taro';

/**
 * H5版本：通过DOM API计算文本行数
 * @param props 计算参数
 * @param props.text 文本内容
 * @param props.maxLine 最大行数
 * @param props.selector 选择器
 * @param props.delta 误差值，默认8(半个字宽)，避免输入一个逗号后换行了但是高度没变
 * @returns 计算出的行数
 */
export const calculateTextLinesH5 = (props: {
    text: string;
    maxLine: number;
    selector: string;
    delta?: number;
}): number => {
    const {text, maxLine, selector, delta = 8} = props;

    if (!text || !selector) {
        return 1;
    }

    try {
        const textareaElement = document.querySelector(selector) as HTMLTextAreaElement;
        // 创建一个临时的span元素来测量文本宽度
        const tempSpan = document.createElement('span');

        // 复制textarea的样式
        const textareaStyle = window.getComputedStyle(textareaElement);
        tempSpan.style.fontSize = textareaStyle.fontSize;
        tempSpan.style.fontFamily = textareaStyle.fontFamily;
        tempSpan.style.fontWeight = textareaStyle.fontWeight;
        tempSpan.style.fontStyle = textareaStyle.fontStyle;
        tempSpan.style.letterSpacing = textareaStyle.letterSpacing;
        tempSpan.style.whiteSpace = 'nowrap';
        tempSpan.style.position = 'absolute';
        tempSpan.style.visibility = 'hidden';
        tempSpan.style.pointerEvents = 'none';

        // 设置文本内容
        tempSpan.textContent = text;

        // 添加到DOM中进行测量
        document.body.appendChild(tempSpan);

        // 获取文本宽度
        const textWidth = tempSpan.offsetWidth;

        // 获取textarea的可用宽度（减去padding和border）
        const textareaRect = textareaElement.getBoundingClientRect();
        const textareaStyleComputed = window.getComputedStyle(textareaElement);
        const paddingLeft = parseFloat(textareaStyleComputed.paddingLeft);
        const paddingRight = parseFloat(textareaStyleComputed.paddingRight);
        const borderLeft = parseFloat(textareaStyleComputed.borderLeftWidth);
        const borderRight = parseFloat(textareaStyleComputed.borderRightWidth);

        const textareaWidth =
            textareaRect.width - paddingLeft - paddingRight - borderLeft - borderRight - delta;

        // 清理临时元素
        document.body.removeChild(tempSpan);

        // 计算行数
        const newLines = Math.ceil(textWidth / textareaWidth);
        return Math.max(1, Math.min(newLines, maxLine));
    } catch (error) {
        console.error('H5版本计算文本行数失败:', error);
        return 1;
    }
};

/**
 * Swan版本：通过Canvas API计算文本行数
 * @param props 计算参数
 * @param props.text 文本内容
 * @param props.maxLine 最大行数
 * @param props.selector 选择器
 * @param props.delta 误差值，默认8(半个字宽)，避免输入一个逗号后换行了但是高度没变
 * @returns Promise<number> 计算出的行数
 */
export const calculateTextLinesSwan = (props: {
    text: string;
    maxLine: number;
    selector: string;
    delta?: number;
}): Promise<number> => {
    // delta=8(半个字宽)是误差值，避免输入一个逗号后换行了但是高度没变
    const {text, maxLine, selector, delta = 8} = props;

    return new Promise(resolve => {
        if (!text || !selector) {
            resolve(1);
            return;
        }

        try {
            const textareaSelectQuery = createSelectorQuery();
            textareaSelectQuery.select(selector).fields({
                rect: true,
                computedStyle: ['fontSize', 'fontFamily']
            });
            textareaSelectQuery.exec(fields => {
                if (fields && fields[0]) {
                    const {left, right, fontSize, fontFamily} = fields[0];
                    const textareaWidth = right - left - delta;
                    // 使用缓存的Canvas上下文测量文本宽度
                    const canvasContext = createCanvasContext('myCanvas');
                    canvasContext.font = `${fontSize} ${fontFamily}`;
                    const {width: textWidth = 0} = canvasContext.measureText(text);

                    // 计算行数
                    const newLines = Math.ceil(textWidth / textareaWidth);
                    resolve(Math.max(1, Math.min(newLines, maxLine)));
                } else {
                    resolve(1);
                }
            });
        } catch (error) {
            console.error('Swan版本计算文本行数失败:', error);
            resolve(1);
        }
    });
};
