/**
 * @file msgItem bubble
 * <AUTHOR>
 */
import cx from 'classnames';
import {View} from '@tarojs/components';
import {FC, memo, ReactNode, useCallback, useState} from 'react';

import LongPressMenu from '../LongPressMenu';
import {useLongPressMenu} from '../../hooks/useLongPressMenu';
import {ExperienceConfig} from '../../models/services/triageStream/index.d';

import styles from './index.module.less';

interface MsgBubbleShellProps {
    originText?: string;
    isPrivate?: boolean;
    isFirstItem?: boolean;
    children?: ReactNode;
    className?: string;
    experienceConfig?: ExperienceConfig;
}

const MsgBubbleShell: FC<MsgBubbleShellProps> = props => {
    const {originText, isPrivate, children, className, experienceConfig} = props;
    const {menuOpenStatus, menuPosition, openMenu, closeMenu} = useLongPressMenu();
    const [h5Position, setH5Position] = useState({x: 0, y: 0});

    // 气泡长按事件
    const handleLongPress = useCallback(
        e => {
            if (!experienceConfig) {
                return;
            }

            // 阻止默认的右键菜单
            e?.preventDefault();
            e?.stopPropagation();

            let position = e?.detail;
            if (process.env.TARO_ENV === 'h5') {
                position = h5Position;
            }
            openMenu(position, true);
        },
        [experienceConfig, h5Position, openMenu]
    );

    const handleTouchStart = useCallback(e => {
        // e.preventDefault();
        const position = {
            x: e?.touches[0].clientX,
            y: e?.touches[0].clientY
        };
        setH5Position(position);
    }, []);

    return (
        <>
            <View
                className={cx(
                    isPrivate ? styles.bubbleWrapper : styles.bubbleWrapperServicer,
                    'wz-plr-45 wz-ptb-42 wz-flex-col wz-col-top wz-fs-51',
                    className
                )}
                onLongPress={e => handleLongPress(e)}
                onLongTap={e => handleLongPress(e)}
                onTouchStart={e => handleTouchStart(e)}
            >
                {children}
            </View>
            <LongPressMenu
                open={menuOpenStatus}
                position={menuPosition}
                originText={originText}
                isPrivate={isPrivate}
                experienceConfig={experienceConfig}
                onClose={() => closeMenu()}
            />
        </>
    );
};

export default memo(MsgBubbleShell);
