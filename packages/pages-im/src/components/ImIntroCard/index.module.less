.imIntroModule {
    width: 100%;
    padding-top: 204px;

    .inner {
        position: relative;
        width: 100%;
        padding: 72px 45px 60px;
        box-sizing: border-box;
        background: url('https://med-fe.cdn.bcebos.com/vita/introBg.png') no-repeat;
        background-size: cover;
        z-index: 2;

        .topImg {
            position: absolute;
            left: 50%;
            top: -204px;
            transform: translateX(-50%);
            width: 288px;
            height: 288px;
            background: url('https://med-fe.cdn.bcebos.com/vita/introCircle.png') no-repeat;
            background-size: cover;
            z-index: 1;

            .bigWhite {
                width: 210px;
                height: 210px;
            }
        }

        .intro {
            font-family: PingFang SC;
            font-weight: 400;
            color: #000311;
            font-size: 51px;
            line-height: 87px;
        }

        .split {
            width: 100%;
            padding-top: 21px;
            margin: 27px 0 48px;
            background: url('https://med-fe.cdn.bcebos.com/vita/introSplit.png') no-repeat;
            background-size: cover;
        }

        .checkCon {
            .checkItem {
                .checkIcon {
                    width: 42px;
                    height: 42px;
                }

                .checkText {
                    padding-left: 9px;
                    font-size: 42px;
                    line-height: 1;
                    color: #103a63;
                    font-family: PingFang SC;
                    font-weight: 400;
                }
            }
        }
    }
}
