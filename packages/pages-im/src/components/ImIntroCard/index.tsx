import {type FC, memo, useEffect} from 'react';
import cx from 'classnames';
import {View, Image, Text} from '@tarojs/components';
import {ubcCommonViewSend} from '../../utils/generalFunction/ubc';
import {IntroProp} from './index.d';
import styles from './index.module.less';

const ImIntroCard: FC<IntroProp> = props => {
    const {data} = props;
    const {content} = data || {};
    const {docImg, title, subTitle} = content || {};

    useEffect(() => {
        ubcCommonViewSend({
            value: 'ImIntroCard'
        });
    }, []);

    return (
        <View className={styles.imIntroModule}>
            <View className={styles.inner}>
                {docImg ? (
                    <View className={cx(styles.topImg, 'wz-flex wz-row-center wz-col-center')}>
                        <Image className={styles.bigWhite} src={docImg} />
                    </View>
                ) : null}
                {title ? <Text className={styles.intro}>{title}</Text> : null}
                <View className={styles.split}></View>
                {subTitle?.length ? (
                    <View className={cx(styles.checkCon, 'wz-flex wz-row-between')}>
                        {subTitle.map((item, index) => (
                            <View
                                className={cx(styles.checkItem, 'wz-flex')}
                                key={`${item.text}_${index}`}
                            >
                                <Image src={item.icon} className={styles.checkIcon} />
                                <Text className={styles.checkText}>{item.text}</Text>
                            </View>
                        ))}
                    </View>
                ) : null}
            </View>
        </View>
    );
};

export default memo(ImIntroCard);
