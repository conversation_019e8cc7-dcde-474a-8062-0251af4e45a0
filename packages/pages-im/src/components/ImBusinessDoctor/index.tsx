import cx from 'classnames';
import {View} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import {type FC, memo, useCallback, useEffect} from 'react';

import {ubcCommonViewSend} from '../../utils/generalFunction/ubc';

import {imgUrlMap} from '../../constants/resourcesOnBos';
import {getDataForUbcAtom} from '../../store/docImAtom/index';

import type {InteractionType} from '../../typings';

import Avatar from './components/Avatar';
import BusinessDoctorInfo from './components/DoctorInfo';

import type {ImBusinessDoctorContent, ImBusinessDoctorProps} from './index.d';

import styles from './index.module.less';

const ImBusinessDoctor: FC<ImBusinessDoctorProps> = ({data, onThrowEvent}) => {
    const {content} = data || {};
    const {interaction, interactionInfo} = data?.actionInfo || {};

    // 整卡点击事件
    const clickFocusDoctor = useCallback(() => {
        onThrowEvent &&
            onThrowEvent({
                interaction: interaction as InteractionType,
                interactionInfo: interactionInfo!
            });
    }, [interaction, interactionInfo, onThrowEvent]);

    useEffect(() => {
        ubcCommonViewSend({
            value: 'ImBusinessDoctor',
            ext: {
                product_info: {
                    ...getDataForUbcAtom()?.product_info
                }
            }
        });
    }, []);

    return (
        <View className={cx(styles.expCardWrapper, 'wz-flex-col')}>
            <View
                className={cx(styles.expFocusDoctorWrapper, 'wz-fs-42')}
                onClick={clickFocusDoctor}
            >
                {/* 医生头像 */}
                {content?.expertPic && (
                    <View className={cx(styles.avatarWrapper)} onClick={clickFocusDoctor}>
                        <Avatar src={content?.expertPic} />
                    </View>
                )}
                {/* 医生信息 */}
                <BusinessDoctorInfo doctorInfo={content as ImBusinessDoctorContent} />
            </View>
            <View className={cx(styles.recWrapper)}>
                {/* 医生简介 */}
                {content?.introduction && (
                    <View className={cx(styles.introduction, 'c-line-clamp2')}>
                        {content?.introduction}
                    </View>
                )}

                {/* 医生擅长领域 */}
                {content?.goodAt && (
                    <View className={cx(styles.goodAt)}>
                        <WImage
                            src={imgUrlMap?.doctorGoodAt}
                            style={{
                                width: 30,
                                height: 15
                            }}
                            className={cx(styles.goodAtIcon)}
                        />
                        <View className={cx(styles.goodAtContent, 'c-line-clamp2')}>
                            {content?.goodAt}
                        </View>
                    </View>
                )}
            </View>
        </View>
    );
};

ImBusinessDoctor.displayName = 'ImBusinessDoctor';
export default memo(ImBusinessDoctor);
