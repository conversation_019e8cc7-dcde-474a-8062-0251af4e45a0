.expCardWrapper {
    background: #fff;
    border: rgb(255 255 255) 3px solid;
    border-radius: 63px;

    .expFocusDoctorWrapper {
        display: flex;
        padding-left: 45px;
        padding-top: 45px;
        position: relative;
        width: 100%;
        box-sizing: border-box;
        z-index: 3;

        .avatar<PERSON>rapper {
            margin-left: 24px;
            position: absolute;
            right: 45px;
            top: 15px;
        }
    }

    .recWrapper {
        display: flex;
        flex-direction: column;
        margin-top: 45px;
        padding: 45px 45px 36px;
        box-sizing: border-box;
        width: 100%;
        border-radius: 63px;
        background: rgb(255 255 255);
        z-index: 3;
        backdrop-filter: blur(60px);
        box-shadow: 0 -12px 36px 0 rgb(0 0 0 / 3.1%);

        .introduction {
            font-size: 42px;
            line-height: 69px;
        }

        .goodAt {
            position: relative;
            margin-top: 18px;
            max-height: 153px;

            .goodAtIcon {
                position: absolute;
                transform: translateY(7px);
                top: 0;
                left: 0;
            }

            .goodAtContent {
                text-indent: 126px;
            }
        }
    }
}
