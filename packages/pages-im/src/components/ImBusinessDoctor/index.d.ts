/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON> zhang<PERSON><EMAIL>
 * @Date: 2025-08-20 15:59:08
 * @LastEditors: zhang<PERSON>jie <EMAIL>
 * @LastEditTime: 2025-08-20 16:58:50
 * @Description:
 */
import {DispatchEventCallbackParams} from '../../typings/msg.type';
import type {InteractionInfo} from '../../typings';

export interface ImBusinessDoctorProps {
    data: ImBusinessDoctorData;
    // 卡片事件统一处理
    onThrowEvent?: (args: DispatchEventCallbackParams) => void;
}

export interface ImBusinessDoctorData {
    cardStyle?: {
        // 本期新增renderType为2
        renderType?: number;
    };
    content?: ImBusinessDoctorContent;
    actionInfo?: ActionInfo;
}

export interface ImBusinessDoctorContent {
    expertName?: string;
    expertHome?: string;
    expertPic?: string;
    expertDepartment?: string;
    expertLevel?: string;
    expertHospital?: string;
    source?: string;
    hospitalFiling?: string;
    hospitalTags?: HospitalTagsData[];
    goodAt?: string;
    isMedicalCosmetology?: boolean; //百分医生
    officialVerification?: boolean; //平台验证
    wjwVerification?: boolean; //卫健委验证
    introduction?: string; //医生简介
    yearsOfExperience?: string; //从业时长
    enableGuahao?: number;
    type?: string;
    license?: string;
    isCertified?: boolean;
    isOnline?: boolean;
    receiptCount?: RateData;
    consultCount?: RateData;
    replySpeed?: RateData;
    commentScore?: RateData;
    goodCommentRate?: RateData;
    serviceIndicators?: RateData;
    assistants?: AssistantsData;
}

export interface RateData {
    text?: string;
    value?: string;
    highLightColor?: string;
}

export interface HospitalTagsData {
    key?: string;
    text?: string;
}

export interface ActionInfo {
    interaction: string;
    interactionInfo: InteractionInfo;
}

export interface AssistantsData {
    title?: string;
    list?: AssistantsItem[];
}

export interface AssistantsItem {
    name?: string;
    level?: string;
}
