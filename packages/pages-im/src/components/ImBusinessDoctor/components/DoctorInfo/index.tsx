/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON> zhang<PERSON><EMAIL>
 * @Date: 2025-08-14 20:39:09
 * @LastEditors: zhang<PERSON>jie <EMAIL>
 * @LastEditTime: 2025-08-22 19:54:00
 * @Description:
 */
import {View, Text} from '@tarojs/components';
import cx from 'classnames';
import {FC} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';
import {imgUrlMap} from '../../../../constants/resourcesOnBos';
import {HospitalTag} from '../HospitalTag';
import styles from './index.module.less';
import {IBusinessDoctorInfoProps} from './index.d';

const BusinessDoctorInfo: FC<IBusinessDoctorInfoProps> = props => {
    const {doctorInfo} = props || {};
    const {
        expertDepartment,
        expertName,
        expertLevel,
        expertHospital,
        isMedicalCosmetology,
        officialVerification,
        wjwVerification
    } = doctorInfo || {};

    return (
        <View className={cx(styles.infoWrapper)}>
            {/* 名称、职称、科室 */}
            <View className={cx(styles.titleWrapper, 'wz-flex-col')}>
                {/* 名称 */}
                <View className={cx(styles.expertName)}>{expertName}</View>

                <View className={cx(styles.expertInfo, 'wz-flex')}>
                    {/* 科室 */}
                    <Text className='wz-fw-400 wz-mr-27'>{expertDepartment}</Text>
                    {/* 职称 */}
                    <Text className='wz-fw-400 wz-mr-27'>{expertLevel}</Text>
                </View>
            </View>

            {/* 医院 */}
            <View className={cx(styles.expertHosWrapper, 'wz-flex, wz-mt-30')}>
                <View className={cx(styles.expertHosName, 'wz-taro-ellipsis wz-mr-18')}>
                    {expertHospital}
                </View>
                <HospitalTag tagType='hospitalLevel' />
            </View>
            {/* 百分医生 */}
            {isMedicalCosmetology && (
                <View className={styles.medicalCosmetology}>
                    <WImage src={imgUrlMap.goodDoctor} className={styles.excellentDoctor} />
                    <HospitalTag
                        tagType='medicalCosmetology'
                        className={styles.medicalCosmetologyTag}
                    />
                </View>
            )}
            {/* 已入驻、验证、卫健委可查 */}
            <View className={styles.doctorTagWrapper}>
                <HospitalTag tagType='settlement' />
                {officialVerification && <HospitalTag tagType='officialVerification' />}
                {wjwVerification && <HospitalTag tagType='wjwVerification' />}
            </View>
        </View>
    );
};

BusinessDoctorInfo.displayName = 'BusinessDoctorInfo';
export default BusinessDoctorInfo;
