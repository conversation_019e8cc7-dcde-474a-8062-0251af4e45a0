.infoWrapper {
    box-sizing: border-box;
    z-index: 2;
    max-width: 756px;
    display: flex;
    flex-direction: column;

    .titleWrapper {
        align-items: start;
        flex-wrap: wrap;
    }

    .expertName {
        font-size: 63px;
        line-height: 63px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
    }

    .expertInfo {
        align-items: end;
        flex-wrap: wrap;
        margin-top: 27px;
        font-size: 42px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
    }

    .expertHosWrapper {
        width: 100%;

        .expertHosName {
            min-width: 0;
            font-family: PingFangSC-Regular;
            font-weight: 400;
        }
    }

    .doctorTagWrapper {
        display: flex;
        flex-wrap: wrap;
        margin-top: 24px;
        gap: 24px 0;
    }

    .medicalCosmetology {
        display: flex;
        align-items: center;
        margin-top: 27px;

        .excellentDoctor {
            border-radius: 12px 0 0 12px;
            width: 180px;
            height: 48px;
            flex-shrink: 0;
        }

        .medicalCosmetologyTag {
            padding-left: 0;
        }
    }
}
