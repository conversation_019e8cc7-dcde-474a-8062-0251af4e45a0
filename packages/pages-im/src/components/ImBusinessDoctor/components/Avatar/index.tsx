import {WImage} from '@baidu/wz-taro-tools-core';
import {FC} from 'react';

import {AvatarProps} from './index.d';

const Avatar: FC<AvatarProps> = ({src}) => {
    return (
        <WImage
            src={src}
            shape='square'
            style={{
                width: 150,
                height: 150
            }}
            mode='aspectFit'
        />
    );
};

Avatar.displayName = 'Avatar';
export default Avatar;
