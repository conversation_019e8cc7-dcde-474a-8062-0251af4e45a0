import type {ExperienceConfig} from '../../models/services/triageStream/index.d';
import type {FeatureProps} from '../../typings/msg.type';

export interface IMenuProps {
    open?: boolean;
    isPrivate?: boolean;
    originText?: string;
    position?: {
        x?: number;
        y?: number;
    };
    msgId?: string;
    sessionId?: string;
    feature?: FeatureProps;
    experienceConfig?: ExperienceConfig;
    onClose?: () => void;
}

export interface IMenuContentProps {
    playStatus?: boolean;
    likeStatus?: string;
    experienceConfig?: ExperienceConfig;
    position?: {
        x?: number;
        y?: number;
    };
    isPrivate?: boolean;
    originText?: string;
    msgId?: string;
    sessionId?: string;
    onClose?: () => void;
}
