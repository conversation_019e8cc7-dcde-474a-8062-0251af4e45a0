/**
 * @file 长按展示的menu组件
 * <AUTHOR>
 */
import {View, Image} from '@tarojs/components';
import {FC, memo, useCallback, useEffect, useState} from 'react';
import cx from 'classnames';
import {eventCenter, getSystemInfoSync, pxTransform, createSelectorQuery} from '@tarojs/taro';

import {copyTextFn} from '../../utils/basicAbility/copy';
import {ubcCommonClkSend} from '../../utils/generalFunction/ubc';
import {
    imCopy,
    imEdit,
    disLikeIcon,
    disLikeSolid,
    voiceIcon,
    voicePlayingIcon,
    likeIconSolid,
    likeIcon
} from '../../constants/resourcesOnBos';

import styles from './index.module.less';
import {IMenuContentProps} from './index.d';

const MenuContent: FC<IMenuContentProps> = props => {
    const {
        isPrivate,
        originText,
        position,
        onClose,
        msgId = '',
        sessionId = '',
        experienceConfig = {},
        likeStatus,
        playStatus
    } = props;
    const {userLongPress, botLongPress} = experienceConfig || {};
    const [menuPositionX, setMenuPositionX] = useState(0);
    const [menuPositionY, setMenuPositionY] = useState(0);
    const [menuShow, setMenuShow] = useState(false);

    // 获取菜单的宽
    const getMenuWidth = useCallback(callback => {
        const menuWidth = 300;
        createSelectorQuery()
            .select('#menu')
            .boundingClientRect(res => {
                if (res) {
                    callback?.(res);
                }
            })
            .exec();
        return menuWidth;
    }, []);

    // 计算菜单弹出位置
    useEffect(() => {
        if (position) {
            const {x = 0, y = 0} = position;
            const {screenWidth, screenHeight} = getSystemInfoSync();

            if (x || y) {
                getMenuWidth(({width, height}) => {
                    // 菜单横坐标，距离左右两边安全距离为24，其余情况相对点击位置居中
                    if (x < screenWidth / 2) {
                        // 菜单位于屏幕左侧，左侧安全区域为24
                        setMenuPositionX(x - width / 2 < 24 ? 24 : x - width / 2);
                    } else {
                        // 菜单位于屏幕右侧，右侧安全区域为24
                        setMenuPositionX(
                            screenWidth - (x + width / 2) < 24
                                ? screenWidth - 24 - width
                                : x - width / 2
                        );
                    }

                    // 菜单纵坐标
                    // 位于内容区上1/3位置，气泡位于点击区域下方12px；位于内容区域下方2/3位置，气泡位于点击区域上方12px
                    if (y < screenHeight / 3) {
                        setMenuPositionY(y + 12);
                    } else {
                        setMenuPositionY(y - height - 12);
                    }
                    // 等位置计算完再展示，不然会有位置偏移
                    setMenuShow(true);
                });
            }
        }
    }, [getMenuWidth, position]);

    // 点击菜单项
    const handleMenuClick = useCallback(
        menuItem => {
            switch (menuItem.type) {
                case 'edit':
                    // 输入框重新编辑
                    eventCenter.trigger('inputReEdit', {
                        text: originText
                    });
                    break;
                case 'broadCastTts':
                    // 先暂停再播放tts，防止有其他tts在播放中
                    eventCenter.trigger('stopTTS');
                    !playStatus && eventCenter.trigger(`startTTS_${sessionId}_${msgId}`);
                    break;
                case 'like':
                    eventCenter.trigger(
                        `onLikeClick_${msgId}_${sessionId}`,
                        likeStatus === 'like' ? 'WiseLikeSolid' : 'WiseLike'
                    );
                    break;
                case 'disLike':
                    eventCenter.trigger(
                        `onLikeClick_${msgId}_${sessionId}`,
                        likeStatus === 'disLike' ? 'WiseDislikeSolid' : 'WiseDislike'
                    );
                    break;
                case 'copy':
                    originText && copyTextFn(originText, isPrivate ? '已复制' : '回答内容已复制');
                    break;
                default:
                    break;
            }
            onClose?.();
            // 区分是否是播放状态
            const ubcType =
                menuItem.type === 'broadCastTts'
                    ? playStatus
                        ? 'stopTts'
                        : 'startTts'
                    : menuItem.type;
            ubcCommonClkSend({
                value: `menu_${isPrivate ? 'private' : 'public'}_${ubcType}`,
                ext: {
                    msgId,
                    sessionId
                }
            });
        },
        [isPrivate, likeStatus, msgId, onClose, originText, playStatus, sessionId]
    );

    // Icon映射
    const getIcon = useCallback(
        (iconName?: string) => {
            const iconMap = {
                edit: <Image className={styles.iconImg} src={imEdit} />,
                volume: playStatus ? (
                    <Image className={styles.iconImg} src={voicePlayingIcon} />
                ) : (
                    <Image className={styles.iconImg} src={voiceIcon} />
                ),
                wiseLike:
                    likeStatus === 'like' ? (
                        <Image className={styles.iconImg} src={likeIconSolid} />
                    ) : (
                        <Image className={styles.iconImg} src={likeIcon} />
                    ),
                wiseDisLike:
                    likeStatus === 'disLike' ? (
                        <Image className={styles.iconImg} src={disLikeSolid} />
                    ) : (
                        <Image className={styles.iconImg} src={disLikeIcon} />
                    ),
                copy: <Image className={styles.iconImg} src={imCopy} />
            };

            return iconMap[iconName || ''] || null;
        },
        [likeStatus, playStatus]
    );

    // 菜单项
    const getMenuItem = useCallback(
        menuItem => {
            if (!menuItem) {
                return null;
            }

            return (
                <View
                    className={cx(styles.menuItem, 'wz-plr-36 wz-text-center')}
                    onClick={() => handleMenuClick(menuItem)}
                >
                    <View className={'wz-mb-12'}>{getIcon(menuItem.icon)}</View>
                    <View
                        className={cx(
                            styles.menuItemText,
                            menuItem?.type === 'broadCastTts' &&
                                playStatus &&
                                styles.menuItemTextSelected,
                            menuItem?.type === 'like' &&
                                likeStatus === 'like' &&
                                styles.menuItemTextSelected,
                            menuItem?.type === 'disLike' &&
                                likeStatus === 'disLike' &&
                                styles.menuItemTextSelected,
                            'wz-fs-42'
                        )}
                    >
                        {menuItem?.type === 'broadCastTts'
                            ? playStatus
                                ? menuItem.selectedText
                                : menuItem.text
                            : menuItem.text}
                    </View>
                </View>
            );
        },
        [getIcon, handleMenuClick, likeStatus, playStatus]
    );

    // 获取组件map
    const getComponentsMap = useCallback(() => {
        const menuConfig = isPrivate ? userLongPress : botLongPress;
        if (!menuConfig?.length) {
            return null;
        }

        return (
            <>
                {menuConfig?.map((menuItem, index) => {
                    return <View key={index}>{getMenuItem(menuItem)}</View>;
                })}
            </>
        );
    }, [botLongPress, getMenuItem, isPrivate, userLongPress]);

    return (
        <View
            className={cx(
                styles.menuWrap,
                menuShow && styles.menuWrapShow,
                'wz-br-36 wz-plr-18 wz-flex'
            )}
            id='menu'
            style={{
                top: pxTransform((menuPositionY || position?.y || 0) * 3),
                left: pxTransform((menuPositionX || position?.x || 0) * 3)
            }}
        >
            {getComponentsMap()}
        </View>
    );
};

export default memo(MenuContent);
