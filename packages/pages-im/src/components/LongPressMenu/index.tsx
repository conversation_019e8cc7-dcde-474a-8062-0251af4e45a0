/**
 * @file 长按展示的menu组件
 * <AUTHOR>
 */

import {FC, memo, useEffect, useState} from 'react';
import {eventCenter} from '@tarojs/taro';
import {Backdrop} from '@baidu/wz-taro-tools-core';

import {ubcCommonViewSend} from '../../utils/generalFunction/ubc';

import {Portal} from '../../../../ui-cards-common';
import MenuContent from './MenuContent';

import styles from './index.module.less';
import {IMenuProps} from './index.d';

const LongPressMenu: FC<IMenuProps> = props => {
    const {
        open = false,
        isPrivate,
        originText,
        position,
        onClose,
        msgId = '',
        sessionId = '',
        experienceConfig = {},
        feature
    } = props;

    const LikestatusMap = {
        0: 'unselect',
        1: 'like',
        2: 'disLike'
    };

    const [likeStatus, setLikeStatus] = useState(LikestatusMap[feature?.attributeStatus || 0]);
    const [playStatus, setPlayStatus] = useState(false);

    // 展现打点
    useEffect(() => {
        if (open) {
            ubcCommonViewSend({
                value: `longPressMenu_${isPrivate ? 'private' : 'public'}`,
                ext: {
                    msgId,
                    sessionId
                }
            });
        }
    }, [isPrivate, msgId, open, sessionId]);

    useEffect(() => {
        eventCenter.on(`updateLikeStatus_${msgId}_${sessionId}`, (status: string) => {
            setLikeStatus(status);
        });
        eventCenter.on(`updateTTSStatus_${sessionId}_${msgId}`, (status: boolean) => {
            setPlayStatus(status);
        });
        return () => {
            eventCenter.off(`updateLikeStatus_${msgId}_${sessionId}`, (status: string) => {
                setLikeStatus(status);
            });
        };
    }, [msgId, sessionId]);

    return (
        <Portal>
            <Backdrop
                open={open}
                closeable
                className={styles.backDrop}
                onClose={() => onClose?.()}
                onLongPress={() => onClose?.()}
                onLongTap={() => onClose?.()}
            >
                {/* 长按菜单 */}
                {open && (
                    <MenuContent
                        playStatus={playStatus}
                        likeStatus={likeStatus}
                        experienceConfig={experienceConfig}
                        position={position}
                        isPrivate={isPrivate}
                        originText={originText}
                        onClose={onClose}
                        msgId={msgId}
                        sessionId={sessionId}
                    />
                )}
            </Backdrop>
        </Portal>
    );
};

export default memo(LongPressMenu);
