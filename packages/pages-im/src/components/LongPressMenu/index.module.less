.backDrop {
    background-color: transparent;
}

.menuWrap {
    background-color: #fff;
    min-height: 210px;
    box-sizing: border-box;
    position: absolute;
    box-shadow: 0 6px 21px 0 #1e1f2433;
    visibility: hidden;
}

.menuWrapShow {
    visibility: visible;
}

.menuItem {
    background-color: #fff;

    &Text {
        color: #272933;
        white-space: nowrap;
    }

    &TextSelected {
        color: #00c8c8;
    }

    .iconImg {
        width: 66px;
        height: 66px;
    }
}
