import {View} from '@tarojs/components';
import {useLoad} from '@tarojs/taro';
import {useState, useEffect} from 'react';
import cx from 'classnames';
import {HIcon, HPageContainer} from '@baidu/health-ui';
import {Button} from '@baidu/wz-taro-tools-core';
import {ImDemo} from '@baidu/vita-ui-cards-wz';
import {
    ImFlow,
    ImSystem,
    ImFormCard,
    CLoginButton,
    ImDoctorCard,
    Portal
} from '@baidu/vita-ui-cards-common';

import ImCollectedAndNoNDirectSkuTypewriter from '../../components/ImCollectedAndNoNDirectSkuTypewriter';
import ImTopNavBar from '../../components/ImTopNavBar';
import StreamFocusService from '../../components/StreamFocusService';
import ImCollectedAndNoNDirectSku from '../../components/ImCollectedAndNoNDirectSku';
import HistoryRecordPopup from '../../components/HistoryRecordPopup';
import ImCollectedAndFocusSku from '../../components/ImCollectedAndReExpert';
import ImInput from '../../components/ImInput';
import CapsuleTools from '../../components/ImInput/capsuleTools';
import ImUploadGuide from '../../components/ImUploadGuide';

import {
    imFlowData,
    imSystemData,
    imFormCardData,
    ImCollectedAndNoNDirectSkuTypewriterData
} from './mockData';
import {Skudata} from './doctorCardMockData';
import {streamFocusServiceData} from './streamFocusServiceMockData';
import {ImCollectedAndNoNDirectSkuData} from './ImCollectedAndNoNDirectSkuMockData';
import {ImCollectedAndReExpertMockData} from './ImCollectedAndReExpertMockData';
import {ImUploadGuideData} from './ImUploadGuideMockData';
import ImImageDemo from './ImImageDemo';
import {navBarData} from './navBarMockData';
import {ongoingToast} from './guideMock';
import styles from './index.module.less';

export default function Index() {
    const [network, setNetwork] = useState<'init' | 'loading' | 'success' | 'error'>('loading');
    const [open, setOpen] = useState(false);

    useLoad(() => {
        // eslint-disable-next-line no-console
        console.log('Page loaded.');
    });

    // const init = (state: any) => {
    //     return {
    //         ...state,
    //         statusBarHeight: getSystemInfo().statusBarHeight, // 状态栏高度
    //         navigationBarHeight: getSystemInfo().navigationBarHeight // 导航栏高度
    //     };
    // };

    // const clickBtn = () => {
    //     navigateTo({url: '/pages/index/index'});
    // };

    useEffect(() => {
        setTimeout(() => {
            setNetwork('success');
        }, 1000);
    }, []);

    return (
        <HPageContainer
            loadingConfig={{
                show: true
            }}
            pageNetworkStatus={network}
            background='#fff'
        >
            {/* <HTopBar
                title='百度健康问医生'
                barBg='linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 100%)'
                initialize={init}
                navigateBack={clickBtn}
                navigateHome={clickBtn}
            /> */}
            <Portal.provider>
                <View className={styles.container}>
                    {/*  */}

                    <ImFlow msgId={imFlowData.msgId} data={imFlowData.data} />
                    <StreamFocusService
                        msgId={streamFocusServiceData.msgId}
                        data={streamFocusServiceData.data}
                    />
                    <ImSystem data={imSystemData.data} />
                    <ImFlow msgId={imFlowData.msgId} data={imFlowData.data} />
                    <StreamFocusService
                        msgId={streamFocusServiceData.msgId}
                        data={streamFocusServiceData.data}
                    />
                    <ImSystem data={imSystemData.data} />

                    <ImDoctorCard data={Skudata.list[0]} />
                    <ImCollectedAndNoNDirectSku
                        msgId={ImCollectedAndNoNDirectSkuData.msgId}
                        data={ImCollectedAndNoNDirectSkuData.data}
                    />
                    <ImImageDemo />

                    <CapsuleTools />
                    <ImInput />
                    <Button
                        style={{paddingTop: '300'}}
                        color='primary'
                        onClick={() => {
                            setOpen(true);
                        }}
                    >
                        打开历史列表
                    </Button>
                    <HistoryRecordPopup {...{open, setOpen}}></HistoryRecordPopup>

                    <ImFormCard
                        msgId={ImCollectedAndNoNDirectSkuData.msgId}
                        data={imFormCardData.data}
                    />

                    <ImCollectedAndNoNDirectSkuTypewriter
                        cardId={ImCollectedAndNoNDirectSkuTypewriterData.data.content.cardId.toString()}
                        cardName={ImCollectedAndNoNDirectSkuTypewriterData.data.content.cardName}
                        version={1}
                        data={ImCollectedAndNoNDirectSkuTypewriterData.data.content.data}
                        msgId={ImCollectedAndNoNDirectSkuTypewriterData.meta.msgId}
                    ></ImCollectedAndNoNDirectSkuTypewriter>
                </View>
                <Portal.slot />
            </Portal.provider>
        </HPageContainer>
    );
}
