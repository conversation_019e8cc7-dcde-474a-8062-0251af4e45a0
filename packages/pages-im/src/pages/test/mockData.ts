/* eslint-disable max-len */
export const imFlowData = {
    data: {
        action: 'end',
        content: {
            cardId: 11412,
            cardName: 'ImFlow',
            data: {
                cardStyle: {
                    renderType: 1
                },
                ubc: {
                    value: 'imFlowIntent',
                    productionInfo: {
                        talkId: '123456',
                        sessionId: 'xxxx',
                        intention: '医生精准'
                    }
                },
                content: {
                    list: [
                        {
                            sectionId: 1,
                            type: 'markdownList',
                            isFinish: false,
                            sectionShowMore: {
                                content: '查看更多医院',
                                interaction: 'openLink',
                                interactionInfo: {
                                    url: '/guahao/pages/guahao/appointslist/index?doc_id=u9pio7r3qog4t36rrgi0\u0026illdesc=%E6%84%9F%E5%86%92%EF%BC%8C%E8%AF%A2%E9%97%AE%E6%84%9F%E5%86%92%E6%9C%9F%E9%97%B4%E8%83%BD%E5%90%A6%E8%BF%90%E5%8A%A8%E3%80%81%E5%96%9D%E7%89%9B%E5%A5%B6%E3%80%81%E5%90%83%E9%B1%BC%EF%BC%8C%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E6%8C%82%E5%8F%B7%E3%80%82\u0026vita_session_id=\u0026vita_talk_id=70ea9ffa9cc3a840781ff8ea248a7c4e_67a2426d30009603'
                                }
                            },
                            // media: {
                            //     // 图片
                            //     images: [
                            //         {
                            //             icon: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                            //             small: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                            //             origin: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                            //             hasMask: true,
                            //             maskInfo: {
                            //                 text: '图片可能会引起您的不适'
                            //             }
                            //         },
                            //         {
                            //             icon: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg',
                            //             small: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg',
                            //             origin: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg'
                            //         }
                            //     ],
                            //     videos: [
                            //         {
                            //             origin: 'https://vd8.bdstatic.com//mda-rdfz91c17xdpufta/ernie/540p/h264_cae/1744846137771181886/mda-rdfz91c17xdpufta.mp4?authorization=bce-auth-v1%2F40f207e648424f47b2e3dfbb1014b1a5%2F2025-06-12T08%3A37%3A51Z%2F-1%2Fhost%2F5752ced039a5bf53d84f157730afbb6adda5d0641028e194ad687e759e2c90dd',
                            //             thumb: 'http://t15.baidu.com/it/u=2254298556,2232916923\u0026fm=225\u0026app=113\u0026f=JPEG?w=1080\u0026h=1920\u0026s=3FA2870A90C6C4E9D2A8296D0300F062',
                            //             width: 1080,
                            //             height: 1920
                            //         }
                            //     ],
                            //     sourceLinks: [
                            //         {
                            //             title: '嗓子异物感有哪些原因', // 标题
                            //             avatars: [
                            //                 'https://med-fe.cdn.bcebos.com/wz-mini/ImWelcomeModule/docNewIpAvatar.png',
                            //                 'https://med-fe.cdn.bcebos.com/wz-mini/ImWelcomeModule/docNewIpAvatar.png',
                            //                 'https://med-fe.cdn.bcebos.com/wz-mini/ImWelcomeModule/docNewIpAvatar.png'
                            //             ], // 头像组
                            //             label: '多专家审阅', // 描述文案
                            //             actionInfo: {
                            //                 interaction: 'openLink',
                            //                 interactionInfo: {
                            //                     url: '/guahao/pages/guahao/appointslist/index?  doc_id=u9pio7r3qog4t36rrgi0\u0026illdesc=%E6%84%9F%E5%86%92%EF%BC%8C%E8%AF%A2%E9%97%AE%E6%84%9F%E5%86%92%E6%9C%9F%E9%97%B4%E8%83%BD%E5%90%A6%E8%BF%90%E5%8A%A8%E3%80%81%E5%96%9D%E7%89%9B%E5%A5%B6%E3%80%81%E5%90%83%E9%B1%BC%EF%BC%8C%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E6%8C%82%E5%8F%B7%E3%80%82\u0026vita_session_id=\u0026vita_talk_id=70ea9ffa9cc3a840781ff8ea248a7c4e_67a2426d30009603'
                            //                 }
                            //             }
                            //         },
                            //         {
                            //             title: '嗓子异物感有哪些原因', // 标题
                            //             avatars: [
                            //                 'https://med-fe.cdn.bcebos.com/wz-mini/ImWelcomeModule/docNewIpAvatar.png',
                            //                 'https://med-fe.cdn.bcebos.com/wz-mini/ImWelcomeModule/docNewIpAvatar.png',
                            //                 'https://med-fe.cdn.bcebos.com/wz-mini/ImWelcomeModule/docNewIpAvatar.png'
                            //             ], // 头像组
                            //             label: '多专家审阅', // 描述文案
                            //             actionInfo: {
                            //                 interaction: 'openLink',
                            //                 interactionInfo: {
                            //                     url: '/guahao/pages/guahao/appointslist/index?  doc_id=u9pio7r3qog4t36rrgi0\u0026illdesc=%E6%84%9F%E5%86%92%EF%BC%8C%E8%AF%A2%E9%97%AE%E6%84%9F%E5%86%92%E6%9C%9F%E9%97%B4%E8%83%BD%E5%90%A6%E8%BF%90%E5%8A%A8%E3%80%81%E5%96%9D%E7%89%9B%E5%A5%B6%E3%80%81%E5%90%83%E9%B1%BC%EF%BC%8C%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E6%8C%82%E5%8F%B7%E3%80%82\u0026vita_session_id=\u0026vita_talk_id=70ea9ffa9cc3a840781ff8ea248a7c4e_67a2426d30009603'
                            //                 }
                            //             }
                            //         },
                            //         {
                            //             title: '嗓子异物感有哪些原因', // 标题
                            //             avatars: [
                            //                 'https://med-fe.cdn.bcebos.com/wz-mini/ImWelcomeModule/docNewIpAvatar.png',
                            //                 'https://med-fe.cdn.bcebos.com/wz-mini/ImWelcomeModule/docNewIpAvatar.png',
                            //                 'https://med-fe.cdn.bcebos.com/wz-mini/ImWelcomeModule/docNewIpAvatar.png'
                            //             ], // 头像组
                            //             label: '多专家审阅', // 描述文案
                            //             actionInfo: {
                            //                 interaction: 'openLink',
                            //                 interactionInfo: {
                            //                     url: '/guahao/pages/guahao/appointslist/index?  doc_id=u9pio7r3qog4t36rrgi0\u0026illdesc=%E6%84%9F%E5%86%92%EF%BC%8C%E8%AF%A2%E9%97%AE%E6%84%9F%E5%86%92%E6%9C%9F%E9%97%B4%E8%83%BD%E5%90%A6%E8%BF%90%E5%8A%A8%E3%80%81%E5%96%9D%E7%89%9B%E5%A5%B6%E3%80%81%E5%90%83%E9%B1%BC%EF%BC%8C%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E6%8C%82%E5%8F%B7%E3%80%82\u0026vita_session_id=\u0026vita_talk_id=70ea9ffa9cc3a840781ff8ea248a7c4e_67a2426d30009603'
                            //                 }
                            //             }
                            //         }
                            //     ]
                            // },
                            contentList: {
                                // ids: ['1', '2', '3', '4', '5', '7'],
                                ids: ['1',],
                                data: {
                                    '1': {
                                        component: 'markdown',
                                        contentAdd:
                                            `中日友好医院（简称“中日医院”）位于北京市
朝阳区樱花园东街2号，是国家卫健委直属大
型三甲医院，1984年开院。作为国家呼吸医学
中心、国家中西医结合医学中心，其呼吸与危
重症医学科、内分泌科等19个专科入选国家临
床重点专科。医院由王辰院士等领衔，承担中
央保健及国家紧急救援任务，首创糖尿病预防“
大庆研究”等里程碑成果。实行365天无假日门
诊，官网www.zryhyy.com.cn,电话010-842
05566。`,
                                        isFinish: false
                                    },
                                    '2': {
                                        contentReplace: {
                                            hospitailLogo:
                                                'https://med-fe.cdn.bcebos.com/hospital/logo/101640.png',
                                            hospitailName: '首都医科大学附属北京儿童医院',
                                            hospitailScore: '322分',
                                            hospitailRemark: '“多项国家级临床实验中心”',
                                            fudanHospitalRank: '全国医院综合排行A++++',

                                            attributeTag: [
                                                {
                                                    text: '三甲综合',
                                                    key: 'hospitalLevel',
                                                    color: '#00C8C8',
                                                    borderColor: '#00C8C8'
                                                },
                                                {
                                                    text: '医保定点',
                                                    key: 'local',
                                                    color: '#FF6600',
                                                    borderColor: 'rgba(255,102,0,0.50)'
                                                }
                                            ],
                                            actionInfo: {
                                                interaction: 'openLink',
                                                interactionInfo: {
                                                    url: '/guahao/pages/guahao/appointslist/index?doc_id=u9pio7r3qog4t36rrgi0\u0026illdesc=%E6%84%9F%E5%86%92%EF%BC%8C%E8%AF%A2%E9%97%AE%E6%84%9F%E5%86%92%E6%9C%9F%E9%97%B4%E8%83%BD%E5%90%A6%E8%BF%90%E5%8A%A8%E3%80%81%E5%96%9D%E7%89%9B%E5%A5%B6%E3%80%81%E5%90%83%E9%B1%BC%EF%BC%8C%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E6%8C%82%E5%8F%B7%E3%80%82\u0026vita_session_id=\u0026vita_talk_id=70ea9ffa9cc3a840781ff8ea248a7c4e_67a2426d30009603'
                                                }
                                            },
                                            btnInfo: [
                                                {
                                                    value: '预约挂号',
                                                    disabled: false,
                                                    interaction: 'openLink',
                                                    interactionInfo: {
                                                        url: '/guahao/pages/guahao/appointslist/index?doc_id=u9pio7r3qog4t36rrgi0\u0026illdesc=%E6%84%9F%E5%86%92%EF%BC%8C%E8%AF%A2%E9%97%AE%E6%84%9F%E5%86%92%E6%9C%9F%E9%97%B4%E8%83%BD%E5%90%A6%E8%BF%90%E5%8A%A8%E3%80%81%E5%96%9D%E7%89%9B%E5%A5%B6%E3%80%81%E5%90%83%E9%B1%BC%EF%BC%8C%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E6%8C%82%E5%8F%B7%E3%80%82\u0026vita_session_id=\u0026vita_talk_id=70ea9ffa9cc3a840781ff8ea248a7c4e_67a2426d30009603'
                                                    }
                                                },
                                                {
                                                    value: '医生主页',
                                                    disabled: false,
                                                    interaction: 'openLink',
                                                    interactionInfo: {
                                                        url: '/guahao/pages/guahao/appointslist/index?doc_id=u9pio7r3qog4t36rrgi0\u0026illdesc=%E6%84%9F%E5%86%92%EF%BC%8C%E8%AF%A2%E9%97%AE%E6%84%9F%E5%86%92%E6%9C%9F%E9%97%B4%E8%83%BD%E5%90%A6%E8%BF%90%E5%8A%A8%E3%80%81%E5%96%9D%E7%89%9B%E5%A5%B6%E3%80%81%E5%90%83%E9%B1%BC%EF%BC%8C%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E6%8C%82%E5%8F%B7%E3%80%82\u0026vita_session_id=\u0026vita_talk_id=70ea9ffa9cc3a840781ff8ea248a7c4e_67a2426d30009603'
                                                    }
                                                }
                                            ]
                                        },
                                        component: 'medicalCard'
                                    },
                                    // '3': {
                                    //     component: 'markdown',
                                    //     contentAdd: '侧睡下ID吃饭i参加法会i长的时间吃多少吃侧睡下ID吃饭i参加法会i长的时间吃多少吃侧睡下ID吃饭i参加法会i长的时间吃多少吃侧睡下ID吃饭i参加法会i长的时间吃多少吃侧睡下ID吃饭i参加法会i长的时间吃多少吃侧睡下ID吃饭i参加法会i长的时间吃多少吃侧睡下ID吃饭i参加法会i长的时间吃多少吃侧睡下ID吃饭i参加法会i长的时间吃多少吃侧睡下ID吃饭i参加法会i长的时间吃多少吃',
                                    //     isFinish: true
                                    // },
                                    '4': {
                                        contentReplace: {
                                            hospitailLogo:
                                                'https://med-fe.cdn.bcebos.com/hospital/logo/101640.png',
                                            hospitailName: '首都医科大学附属北京儿童医院',
                                            hospitailScore: '322分',
                                            hospitailRemark: '“多项国家级临床实验中心”',
                                            fudanHospitalRank: '全国医院综合排行A++++',

                                            attributeTag: [
                                                {
                                                    text: '三甲综合',
                                                    key: 'hospitalLevel',
                                                    color: '#00C8C8',
                                                    borderColor: '#00C8C8'
                                                },
                                                {
                                                    text: '医保定点',
                                                    key: 'local',
                                                    color: '#FF6600',
                                                    borderColor: 'rgba(255,102,0,0.50)'
                                                }
                                            ],
                                            actionInfo: {
                                                interaction: 'openLink',
                                                interactionInfo: {
                                                    url: '/guahao/pages/guahao/appointslist/index?doc_id=u9pio7r3qog4t36rrgi0\u0026illdesc=%E6%84%9F%E5%86%92%EF%BC%8C%E8%AF%A2%E9%97%AE%E6%84%9F%E5%86%92%E6%9C%9F%E9%97%B4%E8%83%BD%E5%90%A6%E8%BF%90%E5%8A%A8%E3%80%81%E5%96%9D%E7%89%9B%E5%A5%B6%E3%80%81%E5%90%83%E9%B1%BC%EF%BC%8C%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E6%8C%82%E5%8F%B7%E3%80%82\u0026vita_session_id=\u0026vita_talk_id=70ea9ffa9cc3a840781ff8ea248a7c4e_67a2426d30009603'
                                                }
                                            },
                                            btnInfo: [
                                                {
                                                    value: '预约挂号',
                                                    disabled: false,
                                                    interaction: 'openLink',
                                                    interactionInfo: {
                                                        url: '/guahao/pages/guahao/appointslist/index?doc_id=u9pio7r3qog4t36rrgi0\u0026illdesc=%E6%84%9F%E5%86%92%EF%BC%8C%E8%AF%A2%E9%97%AE%E6%84%9F%E5%86%92%E6%9C%9F%E9%97%B4%E8%83%BD%E5%90%A6%E8%BF%90%E5%8A%A8%E3%80%81%E5%96%9D%E7%89%9B%E5%A5%B6%E3%80%81%E5%90%83%E9%B1%BC%EF%BC%8C%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E6%8C%82%E5%8F%B7%E3%80%82\u0026vita_session_id=\u0026vita_talk_id=70ea9ffa9cc3a840781ff8ea248a7c4e_67a2426d30009603'
                                                    }
                                                },
                                                {
                                                    value: '医生主页',
                                                    disabled: false,
                                                    interaction: 'openLink',
                                                    interactionInfo: {
                                                        url: 'https://www.baidu.com'
                                                    }
                                                }
                                            ]
                                        },
                                        component: 'medicalCard'
                                    },
                                    '5': {
                                        component: 'doctorCard', //前端对应渲染组件，可能存在同一个数据结构，渲染不同前端组件，通过此字段控制
                                        isFinish: false,
                                        contentReplace: {
                                            expertID: '2296689',
                                            coreID: '30392',
                                            docID: 'bhiimqr3qog4t31gen20',
                                            expertName: '边大鹏',
                                            expertPic:
                                                'https://muzhi-public-pic.cdn.bcebos.com/100110379969',
                                            expertLevel: '副主任医师',
                                            expertHospital: '北京大学第一医院',
                                            expertDepartment: '内镜中心',
                                            expertGoodAt: [
                                                {
                                                    type: 'text',
                                                    value: '擅长: 十二指肠良性肿瘤、十二指肠息肉、取石、急性胆囊炎、慢性胆囊炎、胆管结石、肝内胆管结石、胆总管结石、胆管癌、急性胰腺炎、慢性胰腺炎、肝外胆管结石、肝门部胆管癌、胆管肿瘤、胆源性胰腺炎、胆道肿瘤、胆管炎、胆结石、胆管扩张症、胆道镜碎石、胆管狭窄球囊扩张、胆管肿瘤射频消融、壶腹良性肿瘤内镜下切除'
                                                }
                                            ],
                                            price: '9900',
                                            isOnline: 1,
                                            attributeTag: [
                                                {
                                                    text: '三甲',
                                                    key: 'hospitalLevel',
                                                    color: '#00C8C8',
                                                    borderColor: '#00C8C8'
                                                }
                                            ],
                                            indicatorList: [
                                                {
                                                    text: '年接诊量',
                                                    value: '266',
                                                    highLightColor: '#00C8C8'
                                                },
                                                {
                                                    text: '平均响应',
                                                    value: '7小时内',
                                                    highLightColor: '#00C8C8'
                                                }
                                            ],
                                            actionInfo: {
                                                interaction: 'openLink',
                                                interactionInfo: {
                                                    url: '/wenzhen/pages/triage/index?doc_id=bhiimqr3qog4t31gen20\u0026expert_id=2296689\u0026isDirected=1\u0026preRecordBizId=1395697586669571\u0026preRecordBizType=preQid'
                                                }
                                            },
                                            btnInfo: [
                                                {
                                                    value: '去咨询',
                                                    subDesc: '￥0.45起',
                                                    disabled: false,
                                                    interaction: 'openLink',
                                                    interactionInfo: {
                                                        url: '/wenzhen/pages/triage/index?doc_id=bhiimqr3qog4t31gen20\u0026expert_id=2296689\u0026isDirected=1\u0026preRecordBizId=1395697586669571\u0026preRecordBizType=preQid'
                                                    }
                                                },
                                                {
                                                    value: '预约挂号',
                                                    disabled: false,
                                                    interaction: 'openLink',
                                                    interactionInfo: {
                                                        url: '/wenzhen/pages/triage/index?doc_id=bhiimqr3qog4t31gen20\u0026expert_id=2296689\u0026isDirected=1\u0026preRecordBizId=1395697586669571\u0026preRecordBizType=preQid'
                                                    }
                                                }
                                            ],
                                            fuDanInfo: [
                                                {
                                                    text: '医院全国A++++',
                                                    type: 'complex_honor_reason',
                                                    name: 'fudanRank'
                                                }
                                            ]
                                        }
                                    },
                                    7: {
                                        component: 'medicalCard',
                                        isFinish: true,
                                        contentReplace: {
                                            hospitalLogo:
                                                'https://med-fe.cdn.bcebos.com/hospital/logo/106321.png',
                                            hospitalName: '北京积水潭医院(创伤骨科)',
                                            hospitalScore: '33.93333',
                                            hospitalRemark: '已收到1647位患友好评',
                                            attributeTag: [
                                                {
                                                    text: '三甲综合',
                                                    key: 'hospitalLevel',
                                                    color: '',
                                                    borderColor: ''
                                                },
                                                {
                                                    text: '医保定点',
                                                    key: 'local',
                                                    color: '',
                                                    borderColor: ''
                                                },
                                                {
                                                    text: '西城区',
                                                    key: 'area',
                                                    color: '',
                                                    borderColor: ''
                                                }
                                            ],
                                            btnInfo: [
                                                {
                                                    value: '去挂号',
                                                    disabled: false,
                                                    interaction: 'openLink',
                                                    interactionInfo: {
                                                        url: 'pages/guahao/expertlist/index?title=%E5%8C%97%E4%BA%AC%E7%A7%AF%E6%B0%B4%E6%BD%AD%E5%8C%BB%E9%99%A2&hospital=%E5%8C%97%E4%BA%AC%E7%A7%AF%E6%B0%B4%E6%BD%AD%E5%8C%BB%E9%99%A2&hospital_id=1006321&department=%E5%88%9B%E4%BC%A4%E9%AA%A8%E7%A7%91'
                                                    }
                                                },
                                                {
                                                    value: '更多服务',
                                                    disabled: false,
                                                    interaction: 'openLink',
                                                    interactionInfo: {
                                                        url: 'pages/department/home/<USER>'
                                                    }
                                                }
                                            ],
                                            actionInfo: {
                                                interaction: 'openLink',
                                                interactionInfo: {
                                                    url: 'pages/department/home/<USER>'
                                                }
                                            }
                                        }
                                    }
                                    // '5': {
                                    //     contentReplace: {
                                    //         title: '查看更多医院',
                                    //         actionInfo: {
                                    //             interaction: 'openLink',
                                    //             interactionInfo: {
                                    //                 url: '/guahao/pages/guahao/appointslist/index?doc_id=u9pio7r3qog4t36rrgi0\u0026illdesc=%E6%84%9F%E5%86%92%EF%BC%8C%E8%AF%A2%E9%97%AE%E6%84%9F%E5%86%92%E6%9C%9F%E9%97%B4%E8%83%BD%E5%90%A6%E8%BF%90%E5%8A%A8%E3%80%81%E5%96%9D%E7%89%9B%E5%A5%B6%E3%80%81%E5%90%83%E9%B1%BC%EF%BC%8C%E4%BB%A5%E5%8F%8A%E5%A6%82%E4%BD%95%E6%8C%82%E5%8F%B7%E3%80%82\u0026vita_session_id=\u0026vita_talk_id=70ea9ffa9cc3a840781ff8ea248a7c4e_67a2426d30009603'
                                    //             }
                                    //         }
                                    //     },
                                    //     component: 'viewMoreCard'
                                    // }
                                }
                            }
                        },
                        {
                            sectionId: 2,
                            content: `# 胆结石科普\n\n胆结石是常见的消化系统疾病，让我为您详细说明：\n\n## 基本概念
                            \n胆结石是在胆囊或胆管内形成的固体结晶物质，主要由胆固醇、胆色素或混合成分构成。\n\n## 主要类型
                            \n1. **胆固醇结石**：最常见(约80%)，呈黄色或绿色，与高胆固醇饮食相关
                            \n2. **胆色素结石**：较小呈黑色或棕色，与溶血性疾病或肝硬化有关\n3. **混合型结石**：同时含有胆固醇和胆色素\
                            n\n## 危险因素\n- 女性(尤其是多次妊娠)\n- 40岁以上人群\n- 肥胖或快速减肥\n- 高脂肪、高胆固醇饮食
                            \n- 糖尿病或肝硬化患者\n- 某些降胆固醇药物使用\n\n## 常见症状\n- 右上腹剧烈疼痛(胆绞痛)\n- 饭后腹胀不适
                            \n- 恶心呕吐\n- 黄疸(结石阻塞胆管时)\n- 发热寒战(合并感染时)\n\n## 诊断方法\n- 腹部超声(首选检查)
                            \n- CT或MRI检查\n- 血液检查(肝功能、炎症指标)\n\n## 预防建议\n1. 保持规律饮食，避免长时间空腹
                            \n2. 控制体重，避免快速减肥\n3. 减少高脂肪、高胆固醇食物摄入\n4. 增加膳食纤维和水分摄入\n5. 规律运动\n\n您近期是否有右上腹不适或其他相关症状需要咨询？`,
                            type: 'markdown',
                            isFinish: false,
                            markdownBtn: {
                                title: '预约挂号',
                                description: '根据病情推荐对症医生在线解答',
                                icon: 'https://muzhi-public-pic.cdn.bcebos.com/100115090069',
                                actionInfo: {
                                    value: '问医生',
                                    interaction: 'sendImg',
                                    interactionInfo: {
                                        params: {
                                            payload: [
                                                {
                                                    content: 'abcabc',
                                                    contentType: 1 // 不填为默认，默认为1，图文；2 照片
                                                }
                                            ]
                                        }
                                    }
                                },
                                instruction: {
                                    title: '发图小贴士',
                                    content: [
                                        {
                                            type: 'title',
                                            data: {
                                                value: '报告单解读',
                                                list: []
                                            }
                                        },
                                        {
                                            type: 'text',
                                            data: {
                                                value: '您可上传10MB以内医院就诊记录；血液、尿液等检验报告；B超、X光片、CT、核磁等检查报告，我将马上为您进行深度解读~',
                                                list: []
                                            }
                                        },
                                        {
                                            type: 'img',
                                            data: {
                                                value: '',
                                                list: [
                                                    {
                                                        small: 'https://med-fe.cdn.bcebos.com/wz-mini/vitaResource/medicalRecords.png',
                                                        desc: '门诊病历'
                                                    },
                                                    {
                                                        small: 'https://med-fe.cdn.bcebos.com/wz-mini/vitaResource/diagnosticReport.png',
                                                        desc: '诊断报告'
                                                    },
                                                    {
                                                        small: 'https://med-fe.cdn.bcebos.com/wz-mini/vitaResource/inspectionReport.png',
                                                        desc: '检验报告'
                                                    }
                                                ]
                                            }
                                        }
                                    ]
                                }
                            },
                            media: {
                                // 图片
                                images: [
                                    {
                                        icon: 'https://images.pexels.com/photos/32440654/pexels-photo-32440654.jpeg',
                                        small: 'https://images.pexels.com/photos/32440654/pexels-photo-32440654.jpeg',
                                        origin: 'https://images.pexels.com/photos/32440654/pexels-photo-32440654.jpeg'
                                    },
                                    {
                                        icon: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                                        small: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                                        origin: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                                        hasMask: true,
                                        maskInfo: {
                                            text: '图片可能会引起您的不适'
                                        }
                                    },
                                    {
                                        icon: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg',
                                        small: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg',
                                        origin: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg'
                                    },
                                    {
                                        icon: 'https://images.pexels.com/photos/32412580/pexels-photo-32412580.jpeg',
                                        small: 'https://images.pexels.com/photos/32412580/pexels-photo-32412580.jpeg',
                                        origin: 'https://images.pexels.com/photos/32412580/pexels-photo-32412580.jpeg'
                                    }
                                ],
                                // 视频
                                videos: [
                                    {
                                        origin: 'http://vd3.bdstatic.com/mda-qf30pu9wtyua4w8b/hd/cae_h264/1717461062155899885/mda-qf30pu9wtyua4w8b.mp4',
                                        thumb: 'http://t13.baidu.com/it/u=3116046577,3608829820&fm=225&app=113&f=JPEG?w=1280&h=720&s=9D34EC1219124DCA4E54E9D00000C0B3',
                                        width: 1280,
                                        height: 720
                                    }
                                ]
                            },
                            tabList: {
                                header: [
                                    {
                                        title: '症状解读',
                                        content: '体癣是一种常见的真菌感染性皮肤病'
                                    },
                                    {
                                        title: '评估结果',
                                        content: '有 1种 可能得皮肤病供您参考'
                                    }
                                ],
                                body: [
                                    {
                                        title: '体癣',
                                        list: [
                                            {
                                                title: '疾病概要',
                                                content:
                                                    '体癣是一种由真菌感染引起的皮肤病变，常发生 在暴露部位，如躯干、手臂。真菌在适宜的环境 下繁殖，引起皮肤炎症反应，导致体癣的发生。'
                                            },
                                            {
                                                title: '如何辨别',
                                                content:
                                                    '辨别体癣的主要依据是皮肤出现环状红斑，中心 消退，边缘隆起，伴有瘴痒、脱屑等症状。在严 重时，体癣可能扩散至全身，形成广泛的皮肤损 害。若疑似患有体癣，应及时就医确诊。'
                                            },
                                            {
                                                title: '注意事项',
                                                content:
                                                    '治疗体癣时，需遵循医生建议，采用抗真菌药物 进行治疗。同时，保持皮肤清洁干燥，避免潮湿 环境，减少真菌繁殖的机会。在治疗期间，患者 应注意个人卫生，勤换衣物，避免与他人共用毛 巾等物品，以防传染。如症状持续加重，需及时 复诊，调整治疗方案。'
                                            }
                                        ],
                                        media: {
                                            // 图片
                                            images: [
                                                {
                                                    icon: 'https://images.pexels.com/photos/32440654/pexels-photo-32440654.jpeg',
                                                    small: 'https://images.pexels.com/photos/32440654/pexels-photo-32440654.jpeg',
                                                    origin: 'https://images.pexels.com/photos/32440654/pexels-photo-32440654.jpeg'
                                                },
                                                {
                                                    icon: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                                                    small: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                                                    origin: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                                                    hasMask: true,
                                                    maskInfo: {
                                                        text: '图片可能会引起您的不适'
                                                    }
                                                },
                                                {
                                                    icon: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg',
                                                    small: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg',
                                                    origin: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg'
                                                },
                                                {
                                                    icon: 'https://images.pexels.com/photos/32412580/pexels-photo-32412580.jpeg',
                                                    small: 'https://images.pexels.com/photos/32412580/pexels-photo-32412580.jpeg',
                                                    origin: 'https://images.pexels.com/photos/32412580/pexels-photo-32412580.jpeg'
                                                }
                                            ],
                                            // 视频
                                            videos: [
                                                {
                                                    origin: 'http://vd3.bdstatic.com/mda-qf30pu9wtyua4w8b/hd/cae_h264/1717461062155899885/mda-qf30pu9wtyua4w8b.mp4',
                                                    thumb: 'http://t13.baidu.com/it/u=3116046577,3608829820&fm=225&app=113&f=JPEG?w=1280&h=720&s=9D34EC1219124DCA4E54E9D00000C0B3',
                                                    width: 1280,
                                                    height: 720
                                                }
                                            ]
                                        }
                                    },
                                    {
                                        title: '湿疹',
                                        list: [
                                            {
                                                title: '疾病概要',
                                                content:
                                                    '湿疹是一种由多种因素引起的皮肤炎症，常表现为皮肤出现红色丘疹、水泡、渗液等。 湿疹的发病原因复杂，可能与遗传、环境、免疫等因素有关。'
                                            },
                                            {
                                                title: '如何辨别',
                                                content:
                                                    '辨别湿疹的主要依据是皮肤出现红色丘疹、水泡、渗液等。在严重情况下，湿疹可能扩散至全身，形成广泛的皮肤损害。若疑似患有湿疹，应及时就医确诊。'
                                            },
                                            {
                                                title: '注意事项',
                                                content:
                                                    '治疗湿疹时，需遵循医生建议，采用抗过敏药物 进行治疗。同时，保持皮肤清洁干燥，避免潮湿 环境，减少过敏原的刺激。在治疗期间，患者应 注意个人卫生，勤换衣物，避免与他人共用毛 巾等物品，以防传染。'
                                            }
                                        ],
                                        media: {
                                            // 视频
                                            videos: [
                                                {
                                                    origin: 'http://vd3.bdstatic.com/mda-qf30pu9wtyua4w8b/hd/cae_h264/1717461062155899885/mda-qf30pu9wtyua4w8b.mp4',
                                                    thumb: 'http://t13.baidu.com/it/u=3116046577,3608829820&fm=225&app=113&f=JPEG?w=1280&h=720&s=9D34EC1219124DCA4E54E9D00000C0B3',
                                                    width: 1280,
                                                    height: 720
                                                }
                                            ]
                                        }
                                    },
                                    {
                                        title: '紫癫',
                                        list: [
                                            {
                                                title: '疾病概要',
                                                content:
                                                    '紫癫是一种由多种因素引起的皮肤炎症，常表现为皮肤出现紫红色丘疹、水泡、渗液等。 紫癫的发病原因复杂，可能与遗传、环境、免疫等因素有关。'
                                            },
                                            {
                                                title: '如何辨别',
                                                content:
                                                    '辨别紫癫的主要依据是皮肤出现紫红色丘疹、水泡、渗液等。在严重情况下，紫癫可能扩散至全身，形成广泛的皮肤损害。若疑似患有紫癫，应及时就医确诊。'
                                            },
                                            {
                                                title: '注意事项',
                                                content:
                                                    '治疗紫癫时，需遵循医生建议，采用抗过敏药物 进行治疗。同时，保持皮肤清洁干燥，避免潮湿 环境，减少过敏原的刺激。在治疗期间，患者应 注意个人卫生，勤换衣物，避免与他人共用毛 巾等物品，以防传染'
                                            }
                                        ],
                                        media: {
                                            // 图片
                                            images: [
                                                {
                                                    icon: 'https://images.pexels.com/photos/32440654/pexels-photo-32440654.jpeg',
                                                    small: 'https://images.pexels.com/photos/32440654/pexels-photo-32440654.jpeg',
                                                    origin: 'https://images.pexels.com/photos/32440654/pexels-photo-32440654.jpeg'
                                                },
                                                {
                                                    icon: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                                                    small: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                                                    origin: 'https://pic5.40017.cn/02/001/b4/aa/rBANDFrfAWqAcnHiAAL0CGb8FEY756_167x167_00.jpg',
                                                    hasMask: true,
                                                    maskInfo: {
                                                        text: '图片可能会引起您的不适'
                                                    }
                                                },
                                                {
                                                    icon: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg',
                                                    small: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg',
                                                    origin: 'https://images.pexels.com/photos/8597551/pexels-photo-8597551.jpeg'
                                                },
                                                {
                                                    icon: 'https://images.pexels.com/photos/32412580/pexels-photo-32412580.jpeg',
                                                    small: 'https://images.pexels.com/photos/32412580/pexels-photo-32412580.jpeg',
                                                    origin: 'https://images.pexels.com/photos/32412580/pexels-photo-32412580.jpeg'
                                                }
                                            ]
                                        }
                                    }
                                ]
                            },
                            plan: [
                                {
                                    id: 'task_001',
                                    name: '第一步：病情分析',
                                    description: `综合咱们之前的对话内容与您的就诊病情信息，
                                    您说到您高血脂并伴有头晕和版本身体麻痹`,
                                    status: 'completed'
                                },
                                {
                                    id: 'task_002',
                                    name: '第二步：全网匹配',
                                    description: `搜索全网120000+医生，对比医生地区、擅长、服务响应速度、
                                    服务评价，综合匹配`,
                                    status: 'doing'
                                },
                                {
                                    id: 'task_002',
                                    name: '第三步：医生筛选',
                                    description: '为您挑选出最符合您当前疾病的两位本地医生',
                                    status: 'doing'
                                }
                            ]
                        }
                    ],
                    quickReply: [
                        {
                            content: '原发性中枢神经系统淋巴瘤是什么，症状有哪些？'
                        },
                        {
                            content:
                                '我之前咨询过周医生，但问题没有解决，这次希望他能更深入地帮我解答。'
                        },
                        {
                            content: '我当前主要的问题是神经性头痛，这该怎么办？'
                        }
                    ]
                }
            }
        },
        searchReferences: {
            title: '思考中', // 开始思考中，完事文案 已深度思考（用时32秒）
            content:
                '用户提供的搜索结果有8条，其中1提到三高啥子嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻，现成误饿死东方闪电范德水电费水电费水电费的说法萨发递四方速递',
            isFinish: false // 是否完成思考
        },
        feature: {
            attributeStatus: 1,
            like: {
                actionInfo: {
                    interaction: 'request',
                    interactionInfo: {
                        url: '/wzcui/uiservice/zhenqian/zhusu/useraction?action=attitude',
                        method: 'post'
                    }
                }
            },
            dislike: {
                actionInfo: {
                    interaction: 'popup',
                    interactionInfo: {
                        url: '/wzcui/uiservice/zhenqian/zhusu/useraction?action=attitude',
                        method: 'post',
                        popupInfo: {
                            type: 'dislikeFeedback',
                            content: {
                                input: {
                                    value: '',
                                    placeholder: '期待你的反馈和意见让我持续成长'
                                },
                                title: '😊 期待你的反馈',
                                content: [
                                    {
                                        subTitle: '',
                                        items: [
                                            {
                                                value: '答非所问'
                                            },
                                            {
                                                value: '回答有误'
                                            },
                                            {
                                                value: '信息过时'
                                            },
                                            {
                                                value: '没有帮助'
                                            },
                                            {
                                                value: '存在偏见'
                                            },
                                            {
                                                value: '内容违法'
                                            },
                                            {
                                                value: '链接不相关'
                                            },
                                            {
                                                value: '链接异常'
                                            }
                                        ]
                                    }
                                ],
                                submitBtn: {
                                    interaction: 'request',
                                    interactionInfo: {
                                        url: '/wzcui/uiservice/zhenqian/zhusu/useraction?action=attitude',
                                        method: 'post'
                                    },
                                    value: '确认'
                                }
                            }
                        }
                    }
                }
            },
            copyInfo: {
                // 新增复制功能，后端返回复制后的数据；
                enable: true
            },
            tts: {
                // 语音播报相关数据
                speaker: ''
            }
        }
    },
    msgId: '1909159960708444160'
};

export const imSystemData = {
    data: {
        cardStyle: {
            renderType: 0
        },
        content: {
            list: [
                [
                    {
                        type: 'text',
                        value: '继续沟通代表您已阅读并同意'
                    },
                    {
                        type: 'underLineText',
                        value: '《服务声明》',
                        interaction: 'openLink',
                        interactionInfo: {
                            url: '/wenzhen/pages/common/服务声明'
                        }
                    },
                    {
                        type: 'text',
                        value: '，AI将基于您的个性化信息进行答复(仅供参考)，点此'
                    },
                    {
                        type: 'underLineText',
                        value: '更改设置',
                        interaction: 'openLink',
                        interactionInfo: {
                            url: '/wenzhen/pages/common/更改设置'
                        }
                    }
                ]
            ]
        }
    }
};

export const imFormCardData = {
    data: {
        cardStyle: {
            renderType: 1
        },
        content: {
            userSubmitMsg: '开始计算经期时间吧～',
            disabled: false,
            title: '经期时间计算器',
            subtitle: '请填写您的经期信息',
            fields: [
                {
                    key: 'firstDayOfLastPeriod',
                    label: '末次月经',
                    element: 'picker',
                    value: '',
                    disabled: false,
                    placeholder: '请选择（第一天）',
                    description: '用户上次月经的第一天',
                    popupTitle: '选择末次月经时间',
                    config: {
                        type: 'date',
                        dateFormat: 'YYYY-MM-DD',
                        minDate: '2025-01-01',
                        maxDate: ''
                    },
                    rules: [
                        {
                            type: 'required',
                            message: '请输入完整数据'
                        }
                    ],
                    unit: {
                        type: 'icon',
                        value: 'wise-fold-down'
                    }
                },
                {
                    key: 'periodDays',
                    label: '月经周期',
                    element: 'input',
                    value: '22',
                    disabled: false,
                    placeholder: '请输入（一般为21～35天）',
                    description: '用户通常的月经周期长度',
                    config: {
                        type: 'number'
                    },
                    rules: [
                        {
                            type: 'required',
                            message: '请输入完整数据'
                        },
                        {
                            type: 'min',
                            value: '15',
                            message: '最小值为15天'
                        },
                        {
                            type: 'max',
                            value: '45',
                            message: '最大值为45天'
                        }
                    ],
                    unit: {
                        type: 'string',
                        value: '天'
                    }
                }
            ],
            footer: {
                buttons: [
                    {
                        type: 'button',
                        value: '立即计算',
                        interaction: 'request',
                        interactionInfo: {
                            url: '/vtui/conversation/msg',
                            method: 'post'
                        },
                        disabled: false
                    }
                ],
                description: {
                    icon: 'wise-tip',
                    text: '温馨提示：固定周期预测法只适用于月经周期规律的女性。若月经周期不规律可使用平均周期预测法，或使用近3-6个月的月经周期平均天数作为周期进行预测。'
                }
            }
        },
        ext: {
            scene: 'imWelcomeModule_wenzhen',
            talkId: 'd5448c538cddbcd7498ee5ccd3f9ec9e_96d6d83b76edc931'
        }
    }
};
export const ImCollectedAndNoNDirectSkuTypewriterData = {
    type: 'static',
    meta: {
        sessionId: '55cfd1a58ef65327386aa6878c6b7fa5_g_eb85a95243849193',
        msgId: '1947578452004093952',
        ownerType: 1,
        showPosterRole: 1,
        createTime: '1753173896',
        sendTime: '0',
        posterInfo: {
            name: '',
            avatar: '',
            title: '',
            disableClickOut: false
        },
        rounds: 0
    },
    data: {
        action: 'end',
        content: {
            cardId: 11602,
            cardName: 'ImAIRecommendUnDirect',
            data: {
                cardStyle: {
                    renderType: 4
                },
                content: {
                    isExpired: false,
                    lineText: '',
                    collectedInfo: {
                        curPatient: {
                            contactId: '341061753173857473',
                            name: '',
                            age: '25',
                            gender: '女',
                            isCertified: 0
                        },
                        patientList: [
                            {
                                contactId: '341061753173857473',
                                name: '',
                                age: '25',
                                gender: '女',
                                isCertified: 0,
                                mouth: 33
                            },
                            {
                                contactId: '341061723034402775',
                                name: '王先生',
                                age: '20',
                                gender: '男',
                                isCertified: 0,
                                mouth: 33
                            },
                            {
                                contactId: '341061722952733767',
                                name: '王先生',
                                age: '125',
                                gender: '男',
                                isCertified: 2,
                                mouth: 33
                            }
                        ],
                        qid: '2087415345261708',
                        clinicalDesc: '25岁女性，近期出现胃胀气、打嗝症状。',
                        statusText: {
                            collectionComplete: {
                                text: '根据病情为您推荐相关医生'
                            },
                            patientCollecting: {
                                text: '正在整理您的就诊人信息...'
                            },
                            patientFail: {
                                text: '未识别您的就诊人信息...'
                            },
                            questionCollecting: {
                                text: '正在整理您的病情信息...'
                            },
                            questionFail: {
                                text: '未识别您的病情信息...'
                            }
                        }
                    },
                    skuData: {
                        titleImg: '极速问医生·60s响应',
                        leftIcon: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/rectangle.png',
                        subText: '',
                        topArea: {
                            titleImg:
                                'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/national_health_commission.png',
                            renderType: 0,
                            doctorDisplayArea: {
                                avatars: [
                                    'https://med-fe.cdn.bcebos.com/wz-mini/docAvatar/98bac45fa571d7e68d2032971f52f2db.png'
                                ],
                                words: '全网36万医生·已服务本市875万患者',
                                isCarousel: true,
                                tag: [
                                    {
                                        icon: '',
                                        title: '国家卫健委认证'
                                    }
                                ]
                            },
                            dialog: {
                                sloganUrl: '',
                                serviceIcon: '',
                                tips: ''
                            }
                        },
                        btnInfo: null,
                        tipText: '',
                        toastTip: '您当前免费咨询次数已用完，可购买其他服务咨询医生',
                        serviceTip: '',
                        list: [
                            {
                                card: '6',
                                detailInfo: {
                                    skuId: '2107079850461184',
                                    type: 'cardDetail',
                                    title: '服务详情',
                                    doctorTitle: '免费咨询',
                                    explainTitle: '服务说明',
                                    oldPrice: '3',
                                    finalPrice: '0',
                                    discount: '3',
                                    couponReductionPrice: '3',
                                    btn: '立即咨询'
                                },
                                skuId: '2107079850461184',
                                skuCode: '',
                                title: '免费咨询',
                                priceText: '￥0',
                                sub: '真人医生，15分钟一对一服务',
                                couponInfo: {},
                                btn: {
                                    value: '去咨询',
                                    interaction: 'request',
                                    interactionInfo: {
                                        url: '/wzcui/uiservice/order/makeorderwithcheck?atn=&coupon_select=56665749014310&entrance=newbot_fdxsku&from=new_bot&qid=2087415345261708&sku_id=2107079850461184&version=v3'
                                    }
                                },
                                salePrice: 300,
                                promotionPrice: 0,
                                titleIcon: [
                                    {
                                        url: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/exclusive_subsidy.png',
                                        type: 'img'
                                    }
                                ]
                            },
                            {
                                card: '6',
                                detailInfo: {
                                    skuId: '2060350606280704',
                                    type: 'cardDetail',
                                    title: '服务详情',
                                    doctorTitle: '三甲医生',
                                    explainTitle: '服务说明',
                                    oldPrice: '0.03',
                                    finalPrice: '0.03',
                                    btn: '立即咨询'
                                },
                                skuId: '2060350606280704',
                                skuCode: '',
                                title: '三甲医生',
                                priceText: '￥0.03',
                                sub: '公立三甲医生 平均1分钟响应',
                                couponInfo: {},
                                btn: {
                                    value: '去咨询',
                                    interaction: 'request',
                                    interactionInfo: {
                                        url: '/wzcui/uiservice/order/makeorderwithcheck?atn=&entrance=newbot_fdxsku&from=new_bot&qid=2087415345261708&sku_id=2060350606280704&version=v3'
                                    }
                                },
                                salePrice: 3,
                                promotionPrice: 3
                            }
                        ],
                        buttonPrice: null,
                        tagArr: null
                    },
                    maxShowLen: 3,
                    expertData: {
                        title: '权威专家·24小时沟通',
                        leftIcon: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/rectangle.png',
                        list: [
                            {
                                expertID: '2229751',
                                coreID: '21086',
                                docID: 'jlsgudj3qog4t37b6dkg',
                                expertName: '窦晓坛',
                                expertPic: 'https://muzhi-public-pic.cdn.bcebos.com/100082606579',
                                expertLevel: '副主任医师',
                                expertHospital: '南京鼓楼医院',
                                expertDepartment: '消化科',
                                expertGoodAt: [
                                    {
                                        type: 'text',
                                        value: '擅长: 胃炎、功能性消化不良、消化性溃疡、胃肠功能紊乱、胃十二指肠疾病、消化道出血、肠道息肉、胃食管反流病、贲门失弛缓症、食管炎、胃溃疡、十二指肠溃疡、胃息肉、溃疡性结肠炎、克罗恩病、胃良性肿瘤、胃肠道间质瘤、小肠肿瘤、食管良性肿瘤、炎症性肠病、肠道疾病、食管疾病、消化道肿瘤、消化道早癌ESD治疗、炎症性肠病、小肠镜'
                                    }
                                ],
                                price: '12500',
                                isOnline: 1,
                                attributeTag: [
                                    {
                                        text: '三甲',
                                        key: 'hospitalLevel',
                                        color: '#00C8C8',
                                        borderColor: '#00C8C8'
                                    }
                                ],
                                indicatorList: [
                                    {
                                        text: '年接诊量',
                                        value: '43',
                                        highLightColor: '#00C8C8'
                                    },
                                    {
                                        text: '平均响应',
                                        value: '5小时内',
                                        highLightColor: '#00C8C8'
                                    }
                                ],
                                showPortraitTag: [
                                    {
                                        value: '曾经问过',
                                        type: '',
                                        name: '',
                                        icon: ''
                                    }
                                ],
                                actionInfo: {
                                    interaction: 'openLink',
                                    interactionInfo: {
                                        url: '/wenzhen/pages/triageStream/index?doc_id=jlsgudj3qog4t37b6dkg&expert_id=2229751&from=new_bot&isDirected=1&preRecordBizId=2087415345261708&preRecordBizType=preQid'
                                    }
                                },
                                btnInfo: {
                                    value: '去咨询',
                                    disabled: false,
                                    interaction: 'openLink',
                                    interactionInfo: {
                                        url: '/wenzhen/pages/triageStream/index?doc_id=jlsgudj3qog4t37b6dkg&expert_id=2229751&from=new_bot&isDirected=1&preRecordBizId=2087415345261708&preRecordBizType=preQid'
                                    }
                                },
                                fuDanInfo: [
                                    {
                                        text: '医院全国A+++',
                                        type: 'complex_honor_reason',
                                        name: 'fudanRank'
                                    }
                                ]
                            },
                            {
                                expertID: '2322326',
                                coreID: '21084',
                                docID: 'tot4u233qog4t333h2f0',
                                expertName: '沈永华',
                                expertPic: 'https://muzhi-public-pic.cdn.bcebos.com/100111401020',
                                expertLevel: '副主任医师',
                                expertHospital: '南京鼓楼医院',
                                expertDepartment: '消化内科',
                                expertGoodAt: [
                                    {
                                        type: 'text',
                                        value: '擅长: 胃炎、急性胰腺炎、慢性胰腺炎、胰腺炎、经内镜逆行性胰胆管造影术/超声内镜/内镜黏膜切除术、胆管结石、黄疸、胆管癌、胰腺癌、结肠息肉'
                                    }
                                ],
                                price: '5900',
                                isOnline: 0,
                                attributeTag: [
                                    {
                                        text: '三甲',
                                        key: 'hospitalLevel',
                                        color: '#00C8C8',
                                        borderColor: '#00C8C8'
                                    }
                                ],
                                indicatorList: [
                                    {
                                        text: '年接诊量',
                                        value: '45',
                                        highLightColor: '#00C8C8'
                                    },
                                    {
                                        text: '平均响应',
                                        value: '4小时内',
                                        highLightColor: '#00C8C8'
                                    }
                                ],
                                actionInfo: {
                                    interaction: 'openLink',
                                    interactionInfo: {
                                        url: '/wenzhen/pages/triage/index?doc_id=tot4u233qog4t333h2f0&expert_id=2322326&isDirected=1&preRecordBizId=2087415345261708&preRecordBizType=preQid'
                                    }
                                },
                                btnInfo: {
                                    value: '去咨询',
                                    disabled: false,
                                    interaction: 'openLink',
                                    interactionInfo: {
                                        url: '/wenzhen/pages/triage/index?doc_id=tot4u233qog4t333h2f0&expert_id=2322326&isDirected=1&preRecordBizId=2087415345261708&preRecordBizType=preQid'
                                    }
                                },
                                fuDanInfo: [
                                    {
                                        text: '医院全国A+++',
                                        type: 'complex_honor_reason',
                                        name: 'fudanRank'
                                    }
                                ]
                            },
                            {
                                expertID: '2582067',
                                coreID: '21416',
                                docID: 'deqgt1j3qog4t36gqdsg',
                                expertName: '夏金荣',
                                expertPic: 'https://muzhi-public-pic.cdn.bcebos.com/100115034058',
                                expertLevel: '主任医师',
                                expertHospital: '东南大学附属中大医院',
                                expertDepartment: '消化科',
                                expertGoodAt: [
                                    {
                                        type: 'text',
                                        value: '擅长: 胃炎、慢性胃炎、慢性浅表性胃炎、萎缩性胃炎、糜烂性胃炎、慢性非萎缩性胃炎、胆汁反流性胃炎、急性单纯性胃炎、慢性肠胃炎、消化性溃疡、胃肠良、胃癌、胃溃疡、十二指肠溃疡、脂肪肝、胰腺炎、急性胰腺炎、慢性胰腺炎、溃疡性结肠炎、胃肠炎、十二指肠球部溃疡、小儿消化性溃疡、消化道出血、上消化道出血、下消化道出血、胃肿瘤、胃息肉、胃底腺息肉、急性胃肠炎、肝硬化、炎症性肠病、肝硬化、慢性胆囊炎、重症急性胰腺炎的诊疗、早期食道癌、早期胃癌、早期结肠癌及癌前期病变的内镜下粘膜切除术（EMR）、粘膜下剥离术（ESD）、内镜粘膜下挖除术（ESE）、内镜全层切除术（EFR）、经口内镜下环形肌切开术(POEM)、粘膜下隧道内镜切除术(STER)、非静脉曲张出血内镜粘膜下注射、止血夹及APC电凝止血治疗、食道静脉曲张硬化剂注射治疗和圈套结扎治疗术、胃底静脉曲张组织胶注射治疗术、内镜下胃造瘘及鼻空肠管置入术、食道、恶性狭窄探条扩张及支架置放术、B超引导下肝囊肿穿刺无水酒精硬化术、上消化道及大肠隆起或凹陷性疾病及肝胆胰疾病超声内镜检查术、超声内镜引导下食管下段固有肌层注射肉毒杆菌毒素治疗贲门失弛缓症等'
                                    }
                                ],
                                price: '8900',
                                isOnline: 0,
                                attributeTag: [
                                    {
                                        text: '三甲',
                                        key: 'hospitalLevel',
                                        color: '#00C8C8',
                                        borderColor: '#00C8C8'
                                    }
                                ],
                                indicatorList: [
                                    {
                                        text: '年接诊量',
                                        value: '2',
                                        highLightColor: '#00C8C8'
                                    },
                                    {
                                        text: '平均响应',
                                        value: '2小时内',
                                        highLightColor: '#00C8C8'
                                    }
                                ],
                                actionInfo: {
                                    interaction: 'openLink',
                                    interactionInfo: {
                                        url: '/wenzhen/pages/triageStream/index?doc_id=deqgt1j3qog4t36gqdsg&expert_id=2582067&from=new_bot&isDirected=1&preRecordBizId=2087415345261708&preRecordBizType=preQid'
                                    }
                                },
                                btnInfo: {
                                    value: '去咨询',
                                    disabled: false,
                                    interaction: 'openLink',
                                    interactionInfo: {
                                        url: '/wenzhen/pages/triageStream/index?doc_id=deqgt1j3qog4t36gqdsg&expert_id=2582067&from=new_bot&isDirected=1&preRecordBizId=2087415345261708&preRecordBizType=preQid'
                                    }
                                },
                                fuDanInfo: [
                                    {
                                        text: '医院全国A++',
                                        type: 'complex_honor_reason',
                                        name: 'fudanRank'
                                    }
                                ]
                            }
                        ]
                    }
                },
                ext: {
                    scene: 'imFlowOptions',
                    talkId: '55cfd1a58ef65327386aa6878c6b7fa5_7c0502badf33fefd'
                }
            }
        },
        searchReferences: {
            isFinish: false
        },
        feature: {}
    }
};
