import cx from 'classnames';
import {useAtomValue} from 'jotai';
import {View} from '@tarojs/components';
import {WiseTipSolid} from '@baidu/wz-taro-tools-icons';
import {
    memo,
    useRef,
    useMemo,
    useEffect,
    useCallback,
    type FC,
    type CSSProperties,
    type ElementRef,
    type ReactNode
} from 'react';

import {MSG_CARDID_ENUM, MSG_CARDID_ENUM_STREAM_TYPE} from '../../../../constants/msg';
import {ErrorBoundary} from '../../../../components/ErrorBoundary/ErrorBoundaryClass';

import {useMsgDataGetController} from '../../../../hooks/triageStream/dataController';
import {useConversationDataController} from '../../../../hooks/triageStream/useConversationDataController';
import {useScrollControl} from '../../../../hooks/common/useScrollControl';

import {triageSessionResendMsgArgAtom} from '../../../../store/triageStreamAtom/msg';

import type {MsgId} from '../../../../typings';
import {OwnerTypeEnum, type OwnerTypeValue} from '../../../../typings/msg.type';
import {HideLoadingMsg} from '../../../../store/triageStreamAtom/index.type';

import MsgAvatar from '../MsgAvatar';
import CardComponents from '../CardComponents';

import styles from './index.module.less';

interface IProp {
    msgId: MsgId;
    lastMsgId: MsgId;
    isLatest: boolean;
}

const msgDirectionStyle: Record<OwnerTypeValue, CSSProperties> = {
    [OwnerTypeEnum['需求方']]: {
        flexDirection: 'row-reverse'
    },
    [OwnerTypeEnum['服务方']]: {},
    [OwnerTypeEnum['系统']]: {
        width: '100%',
        justifyContent: 'center'
    }
};

/**
 * 会话消息项组件
 *
 * @param props 组件属性
 * @returns 返回 JSX.Element
 */
const SessionMsgItem: FC<IProp> = props => {
    const {msgId, isLatest, lastMsgId} = props;
    const elementRef = useRef<ElementRef<'div'>>(null);

    const {data} = useMsgDataGetController({msgId});
    const {createConversation} = useConversationDataController();
    const resendMsgArg = useAtomValue(triageSessionResendMsgArgAtom(msgId));
    const {scrollToBottom} = useScrollControl();

    const needAvatar = useMemo(() => {
        return false;
    }, []);

    const needHideLoading = useMemo(() => {
        if (HideLoadingMsg[data?.data?.content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE]) {
            return true;
        }

        return false;
    }, [data?.data?.content?.cardId]);

    // 消息是图文混排类型时，loading和 error icon位置对齐文本
    const isImRichMsg = useMemo(() => {
        if (data?.data?.content?.cardId === MSG_CARDID_ENUM['ImRichMsg']) {
            return true;
        }

        return false;
    }, [data?.data?.content?.cardId]);

    const handleClickErrorIcon = useCallback(() => {
        resendMsgArg && createConversation(resendMsgArg);
    }, [createConversation, resendMsgArg]);

    const localMsgStatusDom = useMemo((): ReactNode => {
        if (
            data?.meta?.ownerType === OwnerTypeEnum['需求方'] &&
            data?.meta?.localMsgStatus &&
            ['pending', 'rejected'].includes(data?.meta?.localMsgStatus)
        ) {
            const dMap = {
                rejected: (
                    <WiseTipSolid
                        color='#FD503E'
                        size={60}
                        className={cx(styles.errorIcon, {[styles.richMsgIcon]: isImRichMsg})}
                        onClick={handleClickErrorIcon}
                    />
                ),
                pending: needHideLoading ? null : (
                    <View className={cx(styles.loadingIcon, {[styles.richMsgIcon]: isImRichMsg})} />
                ),
                success: null,
                aborted: null
            };

            return dMap[data?.meta?.localMsgStatus] || null;
        }

        return null;
    }, [
        data?.meta?.ownerType,
        data?.meta?.localMsgStatus,
        isImRichMsg,
        handleClickErrorIcon,
        needHideLoading
    ]);

    // 是否需要滚动到最新消息标识由数据层处理；
    const needScrollToBottom = useMemo(() => {
        return data?.meta?.localExt?.needScrollToBottom;
    }, [data?.meta]);

    useEffect(() => {
        if (needScrollToBottom) {
            // 滚动到指定消息
            scrollToBottom('msg-item-scrollToBottom');
        }
    }, [scrollToBottom, needScrollToBottom]);

    return data ? (
        <View
            data-id={msgId}
            data-card-name={data?.data?.content?.cardName || 'unknown'}
            id={`msg-item-${msgId}`}
            className={cx(styles.sessionMsgItem, 'wz-flex wz-flex-row')}
            style={
                data?.meta?.ownerType === OwnerTypeEnum['需求方']
                    ? {flexDirection: 'row-reverse'}
                    : {}
            }
        >
            <View
                ref={elementRef}
                className={cx(
                    data?.meta?.ownerType === OwnerTypeEnum['需求方']
                        ? styles.sessionUserMsgItemWrapper
                        : styles.sessionMsgItemWrapper,
                    'wz-flex wz-flex-row'
                )}
                style={msgDirectionStyle[data?.meta?.ownerType] || {}}
            >
                <ErrorBoundary key={msgId}>
                    {needAvatar && (
                        <MsgAvatar
                            role={data?.meta?.ownerType}
                            data={data?.meta?.posterInfo}
                            posterRole={data?.meta?.showPosterRole}
                        />
                    )}

                    <CardComponents
                        data={data}
                        msgId={msgId}
                        isLatest={isLatest}
                        lastMsgId={lastMsgId}
                    />
                </ErrorBoundary>
                {/* 用于历史消息加载后，滚动到指定消息 */}
                <View
                    id={`msg-item-${msgId}-scrollAnchor`}
                    className={styles.msgItemScrollAnchor}
                />
            </View>
            {localMsgStatusDom}
        </View>
    ) : null;
};

export default memo(SessionMsgItem);
