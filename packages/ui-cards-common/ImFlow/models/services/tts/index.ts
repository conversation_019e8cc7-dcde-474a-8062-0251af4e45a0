import {API_HOST} from '../../../models/apis/host';
import httpRequest from '../../../../../pages-im/src/utils/basicAbility/comonRequest/common';
import {AudioData, Text2audioParams} from './index.d';

/**
 *
 * @description 文字转语音
 * @returns {Promise<GetRecomListResponse>}
 */
export const text2audio = (params: Text2audioParams) => {
    return httpRequest<AudioData>({
        url: `${API_HOST}/vtui/conversation/text2audio`,
        method: 'POST',
        data: params,
        isNeedLogin: false,
        isFirstScreen: true
    });
};
