export interface Text2audioParams {
    data: {
        // 待转换文本
        tex: string;
        // 待转换文本列表
        texList?: string[];
        // 待转换文本所在消息内容
        msgCnt: string;
        // 待转换文本所在消息内容md5,由后端返回消息体中返回
        msgCntSign: string;
        // 音频比特率, 16、32、64、128、160、320 kbps，默认64
        rate?: number;
        // 语速，取值0-15，默认为5
        spd?: number;
        // 音调，取值0-15，默认为5
        pit?: number;
        // 音量，取值0-15，默认为5
        vol?: number;
        // 发音人选择，百度智能云度厂版申请接入的发音人ID，比如4100
        per?: number;
        // 3为mp3格式(默认)；6-Wave格式；2-opus SDK使用编码格式
        aue?: number;
        // 请求唯一标识，关联上下游服务，不填写服务自动生成
        sn?: string;
        // 用于打开SSML的开关，默认为0关闭；设置为1打开
        xml?: string;
        // 当前talkId
        sessionId: string;
        // 消息id
        msgId: string;
        // 当前待tts文本所在消息 msgKey
        msgKey: string;
    };
}

export interface AudioData {
    // 音频链接
    link: string;
    // 转换文本
    tex?: string;
    // 请求唯一标识
    sn?: string;
}
