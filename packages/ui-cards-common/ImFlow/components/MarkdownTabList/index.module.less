.tabListContainer {
    font-family: PingFang SC;
    width: 100%;
    box-sizing: border-box;
    min-height: 240px;
    position: relative;

    .tabs {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: -24px;

        .tab {
            flex: 1 1 0;
            min-width: 0;
            background-color: #f5f6fa;
            height: 117px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 30px;
            margin-right: 24px;
            padding: 0 30px;

            &Text {
                white-space: nowrap;
                overflow-x: hidden;
                text-overflow: ellipsis;
                width: 100%;
                display: block;
                text-align: center;
                color: #000311;
                font-size: 45px;
                letter-spacing: 0;
                font-family: PingFang SC;
            }

            &.active {
                background: linear-gradient(90deg, #00cfa3 0%, #00d3ea 100%);
                position: relative;

                .tabText {
                    color: #fff;
                    font-weight: 700;
                    font-family: PingFang SC;
                    letter-spacing: 0;
                    font-size: 45px;
                }

                &::after {
                    content: '';
                    position: absolute;
                    width: 30px;
                    height: 30px;
                    border-radius: 0 0 6px;
                    background: linear-gradient(45deg, #00d1c3 0%, #00d1c9 100%);
                    transform: rotate(45deg) translateX(-21px);
                    transform-origin: center;
                    left: 50%;
                    bottom: -29px;
                    z-index: 0;
                }
            }
        }
    }

    .contents {
        line-height: 1;

        &.collapse {
            max-height: 450px;
            overflow: hidden;
            position: relative;
        }

        &.single {
            &.collapse {
                max-height: 591px;
            }
        }

        .content {
            display: none;

            &.active {
                display: block;
            }
        }

        .expandMore {
            opacity: 0;
            display: none;
            transition: opacity 0.3s ease 0.1s;
            position: absolute;
            z-index: 1;
            left: 0;
            bottom: -3px;
            transform: translateZ(0);
            height: 105px;
            width: 100%;
            background-color: #fff;
            justify-content: center;

            &.visible {
                display: flex;
                opacity: 1;
            }

            &View {
                background-color: #f5f6fa;
                border-radius: 90px;
                padding: 18px 36px;
                height: 75px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: center;

                &:active {
                    background-color: #ecedf3;
                }
            }

            &Text {
                font-family: PingFang SC;
                color: #848691;
                font-size: 39px;
            }

            &::before {
                content: '';
                position: absolute;
                left: 0;
                bottom: 105px;
                height: 180px;
                width: 100%;
                background: linear-gradient(to top, #fff, rgb(255 255 255 / 0%));
            }
        }
    }
    /* stylelint-disable selector-pseudo-class-no-unknown */
    :global {
        .markdownContainer {
            .markdown {
                padding: 18px 0 27px;

                &:first-child {
                    padding-top: 15px;

                    .inline-code {
                        font-style: normal;
                        font-weight: 500;
                        color: #00c8c8;
                        display: inline;
                        background-color: transparent;
                        padding: 0 12px;
                        font-family: PingFang SC;
                    }
                }

                .bold {
                    font-weight: 500;
                }

                .p {
                    line-height: 87px;
                    margin-bottom: 18px;
                }

                .h1 {
                    color: #000311;
                    font-size: 72px;
                    font-weight: 600;
                    line-height: 72px;
                    padding: 15px 0 36px;
                    border-bottom: 1px solid #e0e0e0;
                    margin-bottom: 18px;
                }

                .h2 {
                    font-size: 57px;
                    font-weight: 600;
                    line-height: 57px;
                    position: relative;
                    margin-bottom: 0;
                    padding: 45px 0 45px 36px;

                    &::before {
                        content: '';
                        display: block;
                        position: absolute;
                        left: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 12px;
                        height: 57px;
                        background-color: #00c8c8;
                        border-radius: 18px;
                    }
                }

                .h3 {
                    font-size: 51px;
                    font-weight: 600;
                    line-height: 51px;
                    position: relative;
                    margin-bottom: 0;
                    padding: 45px 0;
                }
            }
        }
    }
}
