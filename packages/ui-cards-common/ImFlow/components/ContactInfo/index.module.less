.contactInfoWrapper {
    width: 100%;
    background: rgb(255 255 255 / 100%);
    box-sizing: border-box;
    padding: 18px 0 60px;

    .infoBox {
        display: flex;

        .infoText {
            flex: 1;
            min-width: 0;
        }

        .avatars {
            flex-shrink: 0;
        }

        .goodAt {
            color: #50525c;
        }
    }

    .getNumber {
        position: relative;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        height: 135px;
        width: 100%;
        border-radius: 45px;
        margin-top: 42px;
        padding: 45px 54px;
        background: rgb(245 246 250 / 100%);

        .getNumberInput {
            width: 100%;
            margin-right: 60px;
        }

        .closeIcon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 45px;
        }
    }

    .toReserve {
        margin-top: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 135px;
        border-radius: 300px;
        background: linear-gradient(231.45deg, #4d69f0 0%, #a767f5 98.01%);
        font-size: 54px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        color: rgb(255 255 255 / 100%);
    }

    .bottomTips {
        margin-top: 45px;
        position: relative;
        color: #848691;
        line-height: 60px;

        .tipsIcon {
            position: absolute;
            top: 12px;
        }

        .topsText {
            margin-left: 57px;
            margin-bottom: 0;
        }
    }
}
