import {type FC, ReactNode} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';
import {Text, View} from '@tarojs/components';
import cx from 'classnames';
import {imgUrlMap} from '../../../../../pages-im/src/constants/resourcesOnBos';
import {IHospitalTagProps, TagConfig, TagType, Tags} from './index.type';
import styles from './index.module.less';

const TAG_CONFIGS: Record<TagType, TagConfig> = {
    [Tags.settlement]: {
        text: '已入驻',
        className: 'Settlement',
        leftIcon: 'vIcon'
    },
    [Tags.officialVerification]: {
        text: '平台已验证',
        className: 'officialVerification',
        leftIcon: 'certificationIcon'
    },
    [Tags.wjwVerification]: {
        text: '卫健委可查',
        className: 'wjwVerification',
        leftIcon: 'preferIcon'
    },
    [Tags.hospitalLevel]: {
        text: '社会办医',
        className: 'hospitalLevel'
    },
    [Tags.medicalCosmetology]: {
        text: '多维严评·甄选医美好医生',
        className: 'medicalCosmetology'
    }
};

export const HospitalTag: FC<IHospitalTagProps> = ({tagType, customConfig, className, style}) => {
    const config = TAG_CONFIGS[tagType];

    if (!config) {
        return null;
    }

    // 合并config
    const finalConfig = {...config, ...customConfig};

    const renderIcon = (iconName: string | undefined, isRightIcon = false): ReactNode => {
        if (!iconName || !imgUrlMap?.[iconName]) return null;
        return (
            <WImage
                src={imgUrlMap[iconName]}
                className={cx(styles.icon, isRightIcon && styles.rightIcon)}
            />
        );
    };

    return (
        <View
            className={cx(styles.tagWrapper, styles[finalConfig?.className], className)}
            style={style}
        >
            {finalConfig?.leftIcon && renderIcon(finalConfig?.leftIcon)}
            <Text className={styles.text}>{finalConfig?.text}</Text>
            {finalConfig?.rightIcon && renderIcon(finalConfig?.rightIcon, true)}
        </View>
    );
};
