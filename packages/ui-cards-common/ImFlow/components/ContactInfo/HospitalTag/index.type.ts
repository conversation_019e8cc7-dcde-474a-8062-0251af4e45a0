/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON> zhang<PERSON><EMAIL>
 * @Date: 2025-08-14 11:27:07
 * @LastEditors: zhang<PERSON>ji<PERSON> <EMAIL>
 * @LastEditTime: 2025-08-16 21:07:38
 * @Description:
 */
import type {CSSProperties} from 'react';

// 定义标签类型
export const Tags = {
    settlement: 'settlement',
    officialVerification: 'officialVerification',
    wjwVerification: 'wjwVerification',
    hospitalLevel: 'hospitalLevel',
    medicalCosmetology: 'medicalCosmetology'
} as const;

export type TagType = (typeof Tags)[keyof typeof Tags];

// 定义图标配置接口
export interface IconConfig {
    src: string;
    width?: number;
    height?: number;
    style?: CSSProperties;
}

// 定义标签配置接口
export interface TagConfig {
    text: string;
    className: string;
    leftIcon?: string;
    rightIcon?: string;
}
export interface IHospitalTagProps {
    tagType: TagType;
    // 支持自定义配置覆盖
    customConfig?: Partial<TagConfig>;
    // 支持自定义样式
    className?: string;
    style?: CSSProperties;
}
