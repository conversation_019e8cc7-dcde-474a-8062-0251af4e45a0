import cx from 'classnames';
import {memo, useState, type FC, useEffect, useCallback} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';
import {View, Text, Input} from '@tarojs/components';
import {WiseCloseSolid, WiseTip} from '@baidu/wz-taro-tools-icons';

import {useScrollControl} from '../../../../pages-im/src/hooks/common/useScrollControl';
import {phoneNumberReg} from '../../../../pages-im/src/constants/reg';
import {showToast} from '../../../../pages-im/src/utils/customShowToast';
import {getFreeConsultDoctor} from '../../../../pages-im/src/models/services/docIm';
import {
    ubcCommonViewSend,
    ubcCommonClkSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';

import {HospitalTag} from './HospitalTag';

import type {ContactInfoProps} from './index.d';

import styles from './index.module.less';

const ContactInfo: FC<ContactInfoProps> = props => {
    const {docContactInfo, msgId, ext} = props || {};
    //缺少医生擅长字段
    const {
        expertName,
        expertPic,
        expertLevel,
        expertDepartment,
        expertHospital,
        authorizeDesc,
        phoneNumber,
        actionInfo,
        officialVerification,
        wjwVerification,
        goodAt
    } = docContactInfo || {};
    const {url, params} = actionInfo?.interactionInfo || {};

    const [focus, setFocus] = useState<boolean>(false);
    const [phoneNum, setPhoneNum] = useState<string>(phoneNumber || '');

    const {scrollToBottom} = useScrollControl();

    // 处理输入框聚焦和失焦
    const handleFocusBlur = useCallback(() => {
        scrollToBottom('textarea-send-focus');
    }, [scrollToBottom]);

    useEffect(() => {
        ubcCommonViewSend({
            value: 'telcard_2',
            ext: {
                product_info: {
                    msgId,
                    ...ext
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleToReserve = async () => {
        ubcCommonClkSend({
            value: 'telcardbtn_2',
            ext: {
                product_info: {
                    msgId,
                    ...ext
                }
            }
        });

        if (!phoneNum || (phoneNum !== phoneNumber && !phoneNumberReg.test(phoneNum))) {
            showToast({
                title: '请输入正确的手机号',
                icon: 'none'
            });
            return;
        }

        if (!url || !params) {
            console.error('缺少必要参数');
            return;
        }

        try {
            if (phoneNum !== phoneNumber) {
                params['phone'] = phoneNum;
            }
            const res = await getFreeConsultDoctor(url, params);
            if (!res) {
                console.error('请求错误');
                return;
            }
            showToast({
                title: '稍后有机构医生助理联系您',
                icon: 'none'
            });
        } catch (error) {
            console.error(error);
        }
    };

    const handleClear = () => {
        setPhoneNum('');
    };

    return (
        <>
            <View className={styles.contactInfoWrapper}>
                {/* 医生信息 */}
                <View className={styles.infoBox}>
                    {/* 头像 */}
                    {expertPic && (
                        <WImage
                            src={expertPic || ''}
                            round
                            className={styles.avatars}
                            style={{
                                width: 40,
                                height: 40
                            }}
                            mode='aspectFill'
                        />
                    )}
                    <View className={cx(styles.infoText, 'wz-ml-27')}>
                        <View>
                            {/* 名称 */}
                            <Text className='wz-fs-54 wz-fw-500 wz-mr-18'>{expertName || ''}</Text>
                            {/* 职称 */}
                            <Text className='wz-fw-400 wz-mr-18'>{expertLevel || ''}</Text>
                            {/* 科室 */}
                            <Text className='wz-fw-400'>{expertDepartment || ''}</Text>
                        </View>
                        {/* 医院 */}
                        <View className={'wz-flex wz-mt-12'}>
                            <View className={'c-line-clamp1 wz-fw-500 wz-mr-18'}>
                                {expertHospital || ''}
                            </View>
                            <HospitalTag tagType='hospitalLevel' />
                        </View>
                        {/* 已入驻、验证、卫健委可查 */}
                        <View className={'wz-mt-12'}>
                            {/* 已入驻默认显示 */}
                            <HospitalTag tagType='settlement' />
                            {officialVerification && <HospitalTag tagType='officialVerification' />}
                            {wjwVerification && <HospitalTag tagType='wjwVerification' />}
                        </View>
                        <View className={cx(styles.goodAt, 'c-line-clamp1')}>{goodAt || ''}</View>
                    </View>
                </View>
                {/* 输入模块 */}
                <View className={styles.getNumber}>
                    <Input
                        focus={focus}
                        type='text'
                        maxlength={11}
                        value={phoneNum}
                        placeholder='请输入您的手机号'
                        cursorSpacing={60}
                        //添加margin-right，留出删除按钮的点击区域
                        className={styles.getNumberInput}
                        onBlur={() => {
                            setFocus(false);
                            handleFocusBlur();
                        }}
                        onInput={e => setPhoneNum(e.detail.value)}
                    />
                    {phoneNum && phoneNum !== '' && (
                        <WiseCloseSolid
                            size={54}
                            color='rgba(183, 185, 193, 1)'
                            className={styles.closeIcon}
                            onClick={handleClear}
                        />
                    )}
                </View>

                {/* 预约按钮 */}
                <View className={styles.toReserve} onClick={handleToReserve}>
                    {actionInfo?.value || '去预约'}
                </View>
                {/* 底部提示 */}
                {authorizeDesc && (
                    <View className={cx(styles.bottomTips, 'wz-fs-39 wz-flex')}>
                        <WiseTip className={styles.tipsIcon} color='#848691' size={39} />
                        <View className={cx(styles.topsText, 'c-line-clamp2')}>
                            {authorizeDesc}
                        </View>
                    </View>
                )}
            </View>
        </>
    );
};
ContactInfo.displayName = 'ContactInfo';
export default memo(ContactInfo);
