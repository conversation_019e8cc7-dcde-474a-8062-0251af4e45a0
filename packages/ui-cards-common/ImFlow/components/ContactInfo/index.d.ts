/*
 * @Author: zhang<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-16 19:31:00
 * @LastEditors: zhang<PERSON>jie <EMAIL>
 * @LastEditTime: 2025-08-19 15:21:52
 * @Description:
 */
import {CapsulesToolsType} from '@baidu/vita-pages-im/src/store/triageStreamAtom/index.type';

export interface ContactInfoProps {
    docContactInfo: IDocContactInfo;
    msgId: string;
    ext: {
        [key: string]: string | number;
    };
}

interface IDocContactInfo {
    expertName: string;
    expertPic: string;
    expertDepartment: string;
    expertLevel: string;
    expertHospital: string;
    source: string;
    hospitalFiling: string;
    type: string;
    license: string;
    isCertified: boolean;
    phoneNumber: string;
    authorizeDesc: string;
    actionInfo: {
        value: string;
        interaction: string;
        interactionInfo: {
            url: string;
            params: Record<string, any>;
        };
    };
    tags?: string[];
    platformVerified?: boolean;
    healthCommissionVerified?: boolean;
    officialVerification?: boolean;
    wjwVerification?: boolean;
    instruction?: CapsulesToolsType['instruction'];
    goodAt: string;
}
