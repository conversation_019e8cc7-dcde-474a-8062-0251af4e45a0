import React, {useMemo} from 'react';
import {View} from '@tarojs/components';
import {
    type ContentList,
    type ImFlowProps,
    type Ext,
    type CommonUbcInteraction
} from '../../index.d';

import MarkDown, {type MarkDdownProps} from '../MarkDown';
import DoctorCard, {type DoctorProps} from '../DoctorCard';
import MedicalCard, {type medicalCardProps} from '../MedicalCard';
import viewMoreCard, {type ViewShowMoreProps} from '../ViewShowMore';
import MediaCardSlot, {type MediaCardSlotProps} from '../MediaCardSlot';

interface MarkDownListProps {
    contentList: ContentList;
    className?: string;
    // 全局透传属性
    globalProps?: Partial<ImFlowProps> & {
        msgEnd?: boolean;
        interrupted?: boolean;
        dataSource?: string;
        msgId?: string;
        ext?: Ext;
        forcedShowLoading?: boolean;
        contentList?: ContentList;
        customStyles?: ImFlowProps['customStyles'];
        commonUbcInteraction?: CommonUbcInteraction;
    };
}

export type ComponentProps =
    | MarkDdownProps
    | medicalCardProps
    | ViewShowMoreProps
    | DoctorProps
    | MediaCardSlotProps;
// 组件适配器
interface ComponentAdapter<T = ComponentProps> {
    component: React.ComponentType<T>;
    propsTransformer?: (
        item: ContentList['data'][string],
        globalProps?: MarkDownListProps['globalProps'],
        index?: number
    ) => T;
    validator?: (item: ContentList['data'][string]) => boolean;
}

const componentRegistry: Record<string, ComponentAdapter<ComponentProps>> = {
    markdown: {
        component: MarkDown,
        propsTransformer: (
            item: ContentList['data'][string],
            globalProps?: MarkDownListProps['globalProps']
        ): MarkDdownProps => ({
            content: item.contentAdd || '',
            isFinish: globalProps?.msgEnd || item.isFinish,
            msgEnd: globalProps?.msgEnd || false,
            isTypewriter: true,
            forcedShowLoading: globalProps?.forcedShowLoading,
            customStyles: globalProps?.customStyles?.markDown
        })
    },
    medicalCard: {
        component: MedicalCard,
        propsTransformer: (
            item: ContentList['data'][string],
            globalProps?: MarkDownListProps['globalProps'],
            index?: number
        ): medicalCardProps => ({
            content: item.contentReplace as medicalCardProps['content'],
            curIds: item.curIds as string,
            msgEnd: globalProps?.msgEnd,
            isLastCard: index === (globalProps?.contentList?.ids.length || 0) - 1,
            contentList: item.contentList as ContentList
        })
    },
    doctorCard: {
        component: DoctorCard,
        propsTransformer: (
            item: ContentList['data'][string],
            globalProps?: MarkDownListProps['globalProps'],
            index?: number
        ): DoctorProps => {
            return {
                content: item.contentReplace as DoctorProps['content'],
                isLastCard: index === (globalProps?.contentList?.ids.length || 0) - 1,
                msgEnd: globalProps?.msgEnd || false,
                commonUbcInteraction: globalProps?.commonUbcInteraction
            };
        }
    },
    media: {
        component: MediaCardSlot,
        propsTransformer: (
            item: ContentList['data'][string],
            globalProps?: MarkDownListProps['globalProps']
        ): MediaCardSlotProps => ({
            content: item.contentReplace as MediaCardSlotProps['content'],
            commonUbcInteraction: globalProps?.commonUbcInteraction
        })
    },
    viewMoreCard: {
        component: viewMoreCard,
        propsTransformer: (
            item: ContentList['data'][string],
            globalProps?: MarkDownListProps['globalProps']
        ): ViewShowMoreProps => ({
            content: item.contentReplace as ViewShowMoreProps['content'],
            commonUbcInteraction: globalProps?.commonUbcInteraction
        })
    }
} as const;

type ComponentType = keyof typeof componentRegistry;

const MarkDownList: React.FC<MarkDownListProps> = ({contentList, className, globalProps = {}}) => {
    const {ids, data} = contentList;

    // 渲染单个组件项
    const renderComponent = useMemo(() => {
        return (id: string, index: number) => {
            const item = data[id];
            if (!item?.component) return null;

            const adapter = componentRegistry[item.component as ComponentType];
            if (!adapter) {
                // eslint-disable-next-line no-console
                console.warn(`Unknown component type: ${item.component}`);
                return null;
            }

            const {component: Component, propsTransformer} = adapter;

            try {
                // 组件属性转换
                const componentProps = propsTransformer
                    ? propsTransformer(
                        {
                            ...item,
                            curIds: id,
                            contentList
                        },
                        globalProps,
                        index
                    )
                    : {
                        content: item.contentAdd || '',
                        isFinish: item.isFinish ?? false
                    };

                return <Component key={id} {...componentProps} />;
            } catch (error) {
                const err = error as Error;
                // eslint-disable-next-line no-console
                console.error(`Error rendering component ${item.component}:`, err);
                return null;
            }
        };
    }, [contentList, data, globalProps]);

    // 过滤有效的组件ID
    const validIds = useMemo(
        () =>
            ids.filter(id => {
                const item = data[id];
                if (!item?.component) return false;

                const adapter = componentRegistry[item.component as ComponentType];
                if (!adapter) return false;

                return adapter?.validator ? adapter.validator(item) : true;
            }),
        [ids, data]
    );

    if (!validIds.length) return null;

    return <View className={className}>{validIds.map(renderComponent)}</View>;
};

export default MarkDownList;
