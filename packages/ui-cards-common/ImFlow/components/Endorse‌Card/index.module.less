.popup {
    background: url('https://med-fe.cdn.bcebos.com/vita/vita_endorse_popup-bg.png') no-repeat;
    background-size: cover;

    .popupContentWrap {
        height: 80vh;
        flex-direction: column;
    }

    .headerWrap {
        position: relative;
        top: 63px;
        height: 327px;
    }

    .bigtitle {
        position: relative;
        left: 57px;
        height: 72px;
        width: 597px;
    }

    .tipTitle {
        position: relative;
        left: 57px;
        color: #9a6628;
        max-width: 759px;
        flex-wrap: wrap;
        line-height: 65px;
    }

    .container {
        position: relative;
        width: 100%;
        background-color: #fff;
        border-top-left-radius: 36px;
        border-top-right-radius: 36px;
        overflow-y: scroll;
        flex: 1;

        .docItem {
            border-color: #f5f6fa;
        }
    }

    .avatar {
        flex: none;
        width: 123px;
        height: 123px;
        border: 3px solid #f5f6fa;
    }

    .content {
        flex-direction: column;

        .name {
            color: #1e1f24;
        }

        .level,
        .department,
        .hospitalName {
            color: #000311;
        }

        .count {
            color: #ff471a;
        }

        .goodAt {
            max-width: 972px;
            color: #3d404d;
        }

        .fudanWrap {
            background-color: #fff6d9;
            color: #aa6508;
            height: 54px;
            line-height: 54px;
            border-radius: 12px;

            .fudanIcon {
                width: 144px;
                height: 54px;
            }

            .fudanGap {
                position: relative;

                &::before {
                    position: absolute;
                    left: -12px;
                    top: 6px;
                    content: '';
                    width: 3px;
                    height: 30px;
                    background-color: #d56200;
                    opacity: 0.3;
                }
            }
        }
    }
}

.pageContainer {
    .endorse‌Header {
        position: relative;
        height: 144px;
        margin-bottom: 60px;
        box-sizing: border-box;
        border-radius: 150px;
        background-color: #fff8eb;

        &Desc {
            flex: 1;
            color: #aa6508;
            margin-left: 32px;
            max-width: 780px;
        }

        .content {
            flex: 1;
        }

        .arrow {
            position: relative;
            right: -8px;
            flex: none;
            width: 42px;
            height: 42px;
        }
    }

    .doctorAcatorGroup {
        height: 63px;

        .rotation {
            position: absolute;
            left: 57px;
            top: 50%;
            transform: translateY(-50%);
            height: 63px;
            width: 140px;
        }

        .rotationContainer {
            display: flex;
            align-items: center;

            .expertAvatar {
                width: 66px;
                height: 66px;
                border-radius: 32px;
                box-sizing: border-box;

                .expertAvatarIcon {
                    width: 63px;
                    height: 63px;
                    border: 3px solid #fff;
                }
            }
        }
    }
}
