/**
 * @file 医生卡组件
 * @author: <EMAIL>
 */

import {View, Text} from '@tarojs/components';
import {memo, useEffect, useMemo, type FC} from 'react';
import cx from 'classnames';
import {navigate} from '@baidu/vita-pages-im/src/utils/basicAbility/commonNavigate';
import ImDoctorCard from '../../../ImDoctorCard';
import styles from './index.module.less';

import type {DoctorProps} from './index.d';

// 将price中的数字分割转换成数组
// const priceHandle = (price: string) => {
//     try {
//         return (
//             price
//                 ?.match(/(\D*)(\d+(?:\.\d+)?)(.*)/)
//                 ?.slice(1)
//                 .map((part, index) => {
//                     return {
//                         value: index === 1 ? Number(part) : part,
//                         type: index === 1 ? 'number' : 'text'
//                     };
//                 })
//                 .filter(item => item.value) || []
//         );
//     } catch (err) {
//         console.error('priceHandle 执行出错:', err);
//         return [
//             {
//                 type: 'text',
//                 value: price
//             }
//         ];
//     }
// };

const DoctorCard: FC<DoctorProps> = props => {
    const {content, isLastCard, commonUbcInteraction, msgEnd} = props || {};
    const {btnInfo} = content || {};
    const {expertId, docId} = content || {};

    const ext = useMemo(() => {
        return {
            product_info: {
                expertId,
                docId
            }
        };
    }, [expertId, docId]);

    // 医生卡跳转
    const handleJump = data => {
        const {interaction, interactionInfo} = data || {};
        const {url, params} = interactionInfo || {};

        if (interaction === 'openLink') {
            if (!url) return;

            navigate({
                url,
                openType: 'navigate',
                params
            });
        }
    };

    useEffect(() => {
        if (msgEnd) {
            commonUbcInteraction?.({type: 'view', value: 'doctorCard', ext});
        }
    }, [commonUbcInteraction, ext, msgEnd]);

    const memoBtnChildRen = useMemo(() => {
        if (!btnInfo?.length) return null;
        return (
            <View className={cx(styles.btnInfo, 'wz-flex')}>
                {btnInfo?.map((item, idx) => {
                    // const priceArray = priceHandle(item?.subDesc || '');

                    return (
                        <View
                            key={idx}
                            onClick={() => {
                                handleJump(item);
                                commonUbcInteraction?.({
                                    type: 'clk',
                                    value: 'doctorBtn',
                                    ext: {
                                        product_info: {
                                            btnValue: item?.value
                                        }
                                    }
                                });
                            }}
                            className={cx(styles.btnItem, 'wz-flex')}
                        >
                            <Text className={cx(styles.btntitle, 'wz-fs-45 wz-fw-500')}>
                                {item?.value}
                            </Text>
                            <View className={cx(styles.btnSubTitle, 'wz-fs-45 wz-fw-500')}>
                                {item?.subDesc}
                                {/* {priceArray?.map((item, idx) => {
                                    return (
                                        <Text
                                            key={idx}
                                            className={cx(
                                                'wz-fs-36 wz-fw-500',
                                                item?.type === 'number'
                                                    ? styles.btnSubTitleNumber
                                                    : ''
                                            )}
                                        >
                                            {item?.value}
                                        </Text>
                                    );
                                })} */}
                            </View>
                        </View>
                    );
                })}
            </View>
        );
    }, [btnInfo, commonUbcInteraction]);

    return (
        <View
            className={cx(
                styles.doctorCardWrapper,
                'wz-mt-30',
                'wz-mb-45 wz-pb-63',
                isLastCard ? styles.lastCard : ''
            )}
        >
            <ImDoctorCard
                data={content}
                mode='combination'
                hidePriceBtn
                className={styles.imDoctorCard}
                btnChildren={memoBtnChildRen}
                // onCardClick={() => {
                //     handleJump(content?.actionInfo);
                //     commonUbcInteraction?.({type: 'clk', value: 'doctorCard', ext});
                // }}
                onAvatarClick={() => {
                    handleJump(content?.actionInfo);
                    commonUbcInteraction?.({type: 'clk', value: 'doctorAvatar', ext});
                }}
            />
        </View>
    );
};

export default memo(DoctorCard);

export type {DoctorProps};
