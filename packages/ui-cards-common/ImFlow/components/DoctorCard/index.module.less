.doctorCardWrapper {
    background: #fff;
    border-bottom: 3px solid #ededf0;
    line-height: 1;

    .imDoctorCard {
        /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
        :global {
            [class*='docInfoFlexWrapper'] {
                margin-bottom: 24px;
            }

            [class*='avatarComb'] {
                width: 156px;
                height: 156px;
                box-sizing: border-box;
            }

            [class*='onlineComb'] {
                left: 32px;
                display: none;
            }

            [class*='doctorCardInfoWrapper'] {
                margin-left: 45px;
            }

            [class*='name'] {
                font-size: 54px;
            }

            [class*='line'] {
                color: #000311;
            }

            [class*='department'] {
                color: #000311;
            }

            [class*='hosName'] {
                font-weight: 700;
            }

            [class*='docNameWrapper'] {
                font-size: 42px;
            }

            [class*='tips'] {
                font-size: 42px;
            }
        }
    }

    .btnInfo {
        width: 100%;
        margin-top: 57px;
        box-sizing: border-box;
        justify-content: flex-start;
        gap: 27px;

        .btnItem {
            flex: 1;
            justify-content: center;
            background: #e3f9f9;
            border-radius: 120px;
            height: 120px;
            box-sizing: border-box;

            .btntitle {
                color: #000311;
                font-family: PingFang SC;
            }

            .btnSubTitle {
                color: #fd503e;

                &Number {
                    font-size: 45px;
                    font-family: BaiduNumberPlus;
                }
            }
        }
    }
}

.lastCard {
    border-bottom: none;
}
