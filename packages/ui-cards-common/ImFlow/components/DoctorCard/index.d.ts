import {type ExpertInfo, type ButtonInfo as BtnInfoType} from '../../../ImDoctorCard/index.d';
import {type Ubc, type CommonUbcInteraction} from '../../index.d';

export interface BtnInfo extends BtnInfoType {
    subDesc?: string;
}
export interface ExpertInfoNew extends Omit<ExpertInfo, 'btnInfo'> {
    btnInfo?: BtnInfo[];
}

export interface DoctorProps {
    content: ExpertInfoNew;
    isLastCard?: boolean;
    ubc?: Ubc;
    msgEnd?: boolean;
    commonUbcInteraction?: CommonUbcInteraction;
}
