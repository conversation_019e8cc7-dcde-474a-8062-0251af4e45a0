import {memo, useRef, useCallback} from 'react';
import {View, Text} from '@tarojs/components';
import cx from 'classnames';
import {WImage} from '@baidu/wz-taro-tools-core';
import {imgUrlMap} from '../../../../pages-im/src/constants/resourcesOnBos';
import {
    ubcCommonViewSend,
    ubcCommonClkSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';
import SanjiaTag from '../../../../ui-cards-common/ImDoctorCard/components/SanjiaTag';
import {isEmpty} from '../../../../pages-im/src/utils';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';
import type {ContentList} from '../../index.d';
import {useComponentUbcControl} from '../../hooks/useComponentUbcControl';
import type {medicalCardProps} from './index.d';

import styles from './index.module.less';

const MedicalCard = memo((props: medicalCardProps) => {
    const {msgEnd = false, curIds = '', isLastCard, contentList = {}} = props;
    // 记录需要上报打点的组件ids
    const componentUbcIds = useRef<string[]>([]);
    // 当前渲染组件所在位置
    const componentPos = useRef<number>(0);

    // 消息结束后对卡进行打点处理
    useComponentUbcControl({
        type: 'medicalCard',
        msgEnd,
        curIds,
        dataSource: contentList as ContentList,
        getComponentUbcIds: ({componentIdsList, pos}) => {
            componentUbcIds.current = componentIdsList;
            componentPos.current = pos;
        },
        onMsgEndComponentRender: ({componentIdsList, pos}) => {
            const ubcEvents =
                componentIdsList?.length === 1
                    ? ['doctorcard', 'doctorcard_yygh', 'doctorcard_ckjs']
                    : ['hospitallist', `hospitallist_qck_${pos + 1}`, `doctorcard_qgh_${pos + 1}`];

            ubcEvents.forEach(event => ubcCommonViewSend({value: event}));
        }
    });

    const {
        hospitalLogo = '',
        hospitalName = '',
        hospitalScore = '',
        hospitalRemark = '',
        fudanHospitalRank = '',
        fudanDepartmentRank = '',
        attributeTag = [],
        btnInfo = [],
        actionInfo = {}
    } = props?.content || {};

    // 医院卡跳转
    const handleJump = useCallback((data, idx = 0, type = '') => {
        const {interaction, interactionInfo} = data;
        const needUbcList = componentUbcIds?.current;
        const curComponentPos = componentPos?.current;

        if (interaction === 'openLink') {
            const url = interactionInfo?.url;
            if (!url) return;

            const isOuterUrl = url.startsWith('http') || url.startsWith('https');
            const openType =
                isOuterUrl && process.env.TARO_ENV === 'swan' ? 'easybrowse' : 'navigate';

            navigate({
                url,
                openType
            });

            if (type) {
                if (needUbcList?.length === 1) {
                    return ubcCommonClkSend({
                        value: idx ? 'doctorcard_yygh' : 'doctorcard_ckjs'
                    });
                }
                ubcCommonClkSend({
                    value: idx
                        ? `hospitallist_qck_${curComponentPos + 1}`
                        : `hospitallist_qgh_${curComponentPos + 1}`
                });
            }
        }
    }, []);

    return (
        <View
            className={cx(
                styles.medicalCard,
                !isLastCard ? 'wz-taro-hairline--bottom' : '',
                'wz-flex wz-row-left wz-col-top wz-pb-63 wz-pt-45 wz-mb-45'
            )}
            onClick={() => handleJump(actionInfo)}
        >
            <WImage src={hospitalLogo} round className={cx(styles.avator, 'wz-pr-45')} />
            <View className={cx(styles.content, 'wz-flex wz-col-top')}>
                <Text
                    className={cx(
                        styles.hospitalName,
                        'wz-fw-500 wz-fs-48 wz-mb-27 wz-taro-ellipsis'
                    )}
                >
                    {hospitalName}
                </Text>
                <View className={cx(styles.hospitalRemark, 'wz-flex wz-mb-27 wz-taro-ellipsis')}>
                    {/* 评分 */}
                    {hospitalScore && hospitalScore !== '0' ? (
                        <Text className={cx(styles.score, 'wz-fw-500 wz-fs-42 wz-mr-18')}>
                            {hospitalScore}分
                        </Text>
                    ) : null}
                    {hospitalRemark ? (
                        <Text className={cx(styles.remark, 'wz-fs-42 wz-taro-ellipsis')}>
                            {`“${hospitalRemark}”`}
                        </Text>
                    ) : null}
                </View>
                {/* 复旦排行 */}
                {fudanHospitalRank || fudanDepartmentRank ? (
                    <View className={cx(styles.fudanWrap, 'wz-mb-27 wz-flex wz-col-center')}>
                        <WImage src={imgUrlMap.fudanIcon} className={styles.fudanIcon} />
                        {(fudanHospitalRank && fudanDepartmentRank && (
                            <View className={cx(styles.contentWrap, 'wz-taro-ellipsis')}>
                                <Text
                                    className={cx(styles.fudanText, 'wz-pl-9 wz-fs-36 wz-fw-500')}
                                >
                                    {fudanHospitalRank}
                                </Text>
                                <Text
                                    className={cx(
                                        styles.fudanText,
                                        styles.fudanDepart,
                                        'wz-pr-24 wz-pl-27 wz-fs-36 wz-fw-500'
                                    )}
                                >
                                    {fudanDepartmentRank}
                                </Text>
                            </View>
                        )) ||
                            (fudanHospitalRank && (
                                <Text
                                    className={cx(
                                        styles.fudanText,
                                        'wz-fs-36 wz-fw-500 wz-pl-9 wz-pr-24'
                                    )}
                                >
                                    {fudanHospitalRank}
                                </Text>
                            )) ||
                            (fudanDepartmentRank && (
                                <Text
                                    className={cx(
                                        styles.fudanText,
                                        'wz-fs-36 wz-fw-500 wz-pl-9 wz-pr-24'
                                    )}
                                >
                                    {fudanDepartmentRank}
                                </Text>
                            ))}
                    </View>
                ) : null}
                {!isEmpty(attributeTag) ? (
                    <View className={cx(styles.tagWrap, 'wz-flex wz-col-center')}>
                        {attributeTag.map(el =>
                            el.key === 'hospitalLevel' ? (
                                <SanjiaTag
                                    key={el.key}
                                    content={el.text}
                                    bgColor='#EBF7EF'
                                    color='#39B362'
                                    variant='contained'
                                />
                            ) : (
                                <View
                                    key={el.key}
                                    className={cx(styles.tag, 'wz-plr-12 wz-fs-36 wz-br-9')}
                                >
                                    {el.text}
                                </View>
                            )
                        )}
                    </View>
                ) : null}

                {!isEmpty(btnInfo) ? (
                    <View className={cx(styles.btnGroup, 'wz-flex wz-row-between')}>
                        {btnInfo.map((el, index) => (
                            <View
                                key={index}
                                className={cx(
                                    styles.btn,
                                    ' wz-flex wz-row-center wz-col-center wz-fs-42 wz-fw-500 wz-br-54',
                                    index === 1 && 'wz-ml-27'
                                )}
                                onClick={e => {
                                    e.stopPropagation();
                                    handleJump(el, index, 'btn');
                                }}
                            >
                                {el.value}
                            </View>
                        ))}
                    </View>
                ) : null}
            </View>
        </View>
    );
});

MedicalCard.displayName = 'MedicalCard';
export default MedicalCard;
export {type medicalCardProps};
