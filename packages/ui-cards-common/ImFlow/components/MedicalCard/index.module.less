.medicalCard {
    border-color: #ededf0;

    .avator {
        width: 156px;
        height: 156px;
        flex: none;
    }

    .content {
        flex-direction: column;

        .hospitalName {
            color: #000311;
            max-width: 890px;
        }

        .hospitalRemark {
            max-width: 890px;
        }

        .score {
            color: #ff471a;
        }

        .remark {
            color: #802e00;
            max-width: 750px;
        }

        .fudanWrap {
            height: 48px;
            line-height: 48px;
            max-width: 870px;
            background-color: #fffbe5e5;
            border-radius: 12px;

            .fudanIcon {
                width: 144px;
                height: 48px;
                flex: none;
            }

            .fudanText {
                color: #802e00;
            }

            .fudanDepart {
                position: relative;

                &::before {
                    position: absolute;
                    content: '';
                    top: 10px;
                    left: 10px;
                    width: 3px;
                    height: 30px;
                    background-color: #f3c998;
                }
            }
        }

        .tagWrap {
            flex-wrap: wrap;
            gap: 18px;
            margin-bottom: 60px;

            .tag {
                height: 48px;
                box-sizing: border-box;
                padding-top: 6px;
                padding-bottom: 6px;
                background-color: #edf0fa;
            }
        }

        .btnGroup {
            .btn {
                width: 426px;
                height: 120px;
                background-color: #e4faf9;
            }
        }
    }
}
