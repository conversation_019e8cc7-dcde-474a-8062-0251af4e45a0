import type {ContentList} from '../../index.d';
export interface medicalCardProps {
    curIds: string;
    msgEnd?: boolean;
    contentList?: ContentList;
    isLastCard?: boolean;
    content: {
        hospitalLogo: string;
        hospitalName: string;
        hospitalScore: string;
        hospitalRemark: string;
        fudanHospitalRank: string;
        fudanDepartmentRank: string;
        actionInfo: {
            interaction: string;
            interactionInfo: {
                url: string;
            };
        };
        attributeTag: {
            text: string;
            key: string;
            color: string;
            borderColor: string;
        }[];
        btnInfo: {
            value: string;
            disabled: boolean;
            interaction: string;
            interactionInfo: {
                url: string;
            };
        }[];
    };
}
