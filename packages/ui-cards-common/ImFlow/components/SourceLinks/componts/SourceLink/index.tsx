// 封装一个链接组件
import {pxTransform} from '@tarojs/taro';
import {Text, View, Image} from '@tarojs/components';
import {useCallback, type FC} from 'react';
import cx from 'classnames';
import {navigate} from '../../../../../../pages-im/src/utils/basicAbility/commonNavigate';
import type {SourceLinkProps} from './index.d';
import styles from './index.module.less';

// 头像偏移量
const AVATAR_OFFSET = 40;
// 文本偏移量
const TEXT_OFFSET = 15;

const SourceLink: FC<SourceLinkProps> = ({title, avatarList, source, url, onClick}) => {
    const jumpToLink = useCallback(() => {
        const newUrl = decodeURIComponent(url);
        if (url) {
            if (process.env.TARO_ENV === 'swan') {
                navigate({
                    url: newUrl,
                    openType: 'easybrowse'
                });
            } else {
                location.href = newUrl;
            }
        }
    }, [url]);
    return (
        <View
            className={cx(styles.sourceLink, 'wz-br-27 wz-ptb-27 wz-plr-30')}
            onClick={() => {
                jumpToLink();
                onClick?.();
            }}
        >
            <Text className={cx(styles.title, 'wz-fs-48')}>{title}</Text>
            <View className={cx(styles.content, 'wz-flex wz-row-left wz-col-center wz-mt-24')}>
                <View className={cx(styles.avatars, 'wz-mr-9 wz-flex wz-col-center')}>
                    {avatarList?.map((avatar, index) => (
                        <Image
                            className={cx(styles.avatar)}
                            style={{
                                transform: `translateX(-${index * AVATAR_OFFSET}%)`
                            }}
                            key={index}
                            src={avatar}
                        />
                    ))}
                </View>
                <Text
                    className={cx(styles.label, 'wz-fs-42')}
                    style={{
                        transform: `translateX(-${pxTransform(((avatarList?.length || 1) - 1) * TEXT_OFFSET)})`
                    }}
                >
                    {source}
                </Text>
            </View>
        </View>
    );
};
export default SourceLink;
