// 封装一个链接列表组件
import {View, ScrollView} from '@tarojs/components';
import {type FC, memo, useEffect} from 'react';
import cx from 'classnames';
import SourceLink from './componts/SourceLink';
import type {SourceLinksProps} from './index.d';
import styles from './index.module.less';

const SourceLinks: FC<SourceLinksProps> = ({list, commonUbcInteraction, msgEnd}) => {
    useEffect(() => {
        if (msgEnd) {
            commonUbcInteraction?.({
                type: 'view',
                value: 'sourceLinks'
            });
        }
    }, [commonUbcInteraction, msgEnd]);

    return (
        <View className={cx(styles.sourceLinksWrapper)}>
            <ScrollView
                scrollX
                className={cx(styles.sourceLinks, 'wz-flex wz-row-left wz-col-center wz-mb-45')}
            >
                {list?.map(item => (
                    <SourceLink
                        key={item.title}
                        title={item.title}
                        avatarList={item.avatarList}
                        icon={item.icon}
                        source={item.source}
                        url={item.url}
                        onClick={() => {
                            commonUbcInteraction?.({
                                type: 'clk',
                                value: 'sourceLink',
                                ext: {
                                    product_info: {
                                        dataType: item?.dataType,
                                        title: item?.title,
                                        url: item?.url
                                    }
                                }
                            });
                        }}
                    />
                ))}
            </ScrollView>
        </View>
    );
};
export default memo(SourceLinks);
