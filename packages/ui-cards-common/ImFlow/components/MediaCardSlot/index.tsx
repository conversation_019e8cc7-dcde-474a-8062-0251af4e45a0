import cx from 'classnames';
import {View} from '@tarojs/components';
import {previewImage} from '@tarojs/taro';
import {type FC, memo, useMemo} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';

import {type CommonUbcInteraction} from '../../index.d';

import styles from './index.module.less';

export interface MediaCardSlotProps {
    content: {
        pics?: {
            value: string;
        }[];
    };
    commonUbcInteraction?: CommonUbcInteraction;
}

const MediaCardSlot: FC<MediaCardSlotProps> = props => {
    const {content} = props;

    const previewPic = (list: NonNullable<MediaCardSlotProps['content']['pics']>, idx: number) => {
        const urls = list.map(item => item.value);
        previewImage({
            current: idx,
            urls
        });
    };

    const genPics = useMemo(() => {
        if (!content.pics?.length) {
            return null;
        }

        const pics = content.pics;
        return (
            <View className={cx(styles.MediaCardPicSlot, 'wz-flex')}>
                {pics?.map((item, idx) => {
                    const {value} = item;
                    return (
                        <View
                            key={value}
                            className={cx(styles.MediaCardPicSlotItem, idx % 2 === 0 && 'wz-mr-24')}
                            onClick={() => previewPic(pics, idx)}
                        >
                            <WImage
                                shape='rounded'
                                mode='aspectFill'
                                src={value}
                                className={cx(styles.MediaCardPicSlotItemPic)}
                            />
                        </View>
                    );
                })}
            </View>
        );
    }, [content]);

    return <>{genPics}</>;
};

export default memo(MediaCardSlot);
