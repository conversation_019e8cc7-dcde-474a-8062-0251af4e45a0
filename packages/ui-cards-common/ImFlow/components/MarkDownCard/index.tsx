import cx from 'classnames';
import {useMemo, useState, memo} from 'react';
import {View} from '@tarojs/components';

import {isPaidMed} from '../../../../pages-im/src/utils';

import ToolbarV2 from '../Toolbar/ToolbarV2';
import SectionShowMoreHoc from './components/SectionShowMore';

import styles from './index.module.less';
import type {MarkDownCardProps} from './index.d';

const MarkDownCard = (props: MarkDownCardProps) => {
    const isPaidMedScene = isPaidMed();

    const {
        msgData,
        type = 'doctor',
        children,
        className,
        header = null,
        isInterrupted = false,
        isQuickSkuReply = false,
        sectionShowMore,
        isSectionLast = false,
        isLatest = false,
        page,
        scrollKey,
        commonUbcInteraction
    } = props || {};

    // 判断卡片样式
    const memoRenderCardBorder = useMemo(() => {
        return type === 'doctor' ? styles.doctorCardBorder : styles.patientCardBorder;
    }, [type]);
    const [showMore, setShowMore] = useState(false);
    const [scrollAuto, setScrollAuto] = useState(false);

    // 判断是否显示底部工具栏V2
    const memoRenderToolbar = useMemo(() => {
        return (
            !isInterrupted &&
            msgData?.data?.feature &&
            Object.keys(msgData?.data?.feature).length &&
            isLatest &&
            isSectionLast
        );
    }, [isInterrupted, msgData?.data?.feature, isLatest, isSectionLast]);

    const memoRenderFitContent = useMemo(() => {
        if (isInterrupted) {
            return '';
        }
        if (
            msgData?.data?.feature &&
            Object.keys(msgData?.data?.feature).length &&
            isLatest &&
            isSectionLast
        ) {
            return '';
        }
        if (!isLatest) {
            return styles.aiCardStyleFitContent;
        }
        return '';
    }, [isInterrupted, msgData?.data?.feature, isLatest, isSectionLast]);

    return (
        <View
            className={cx(
                styles.aiCardStyle,
                'wz-mt-51',
                memoRenderCardBorder,
                className,
                header ? '' : 'wz-pt-42',
                memoRenderFitContent
            )}
        >
            {/* 标题 */}
            {header}
            <View className='wz-plr-45'>
                {/* markdown 内容 */}
                <SectionShowMoreHoc
                    sectionShowMore={sectionShowMore}
                    setShowMore={setShowMore}
                    showMore={showMore}
                    type='scroll'
                    scrollAuto={scrollAuto}
                    scrollKey={scrollKey}
                    commonUbcInteraction={commonUbcInteraction}
                >
                    {children}
                </SectionShowMoreHoc>

                {/* 展开更多 */}
                {showMore && (
                    <SectionShowMoreHoc
                        setShowMore={() => {
                            setShowMore(false);
                            setScrollAuto(true);
                        }}
                        sectionShowMore={sectionShowMore}
                        type='showMoreBtn'
                    />
                )}

                {/* 打断 */}
                {isInterrupted && !isQuickSkuReply && isSectionLast && (
                    <View className={cx(styles.interrupted, 'wz-fw-400 wz-fs-48 wz-mb-36')}>
                        回答已终止
                    </View>
                )}

                {/* 工具栏 */}
                <ToolbarV2
                    msgData={msgData}
                    page={page}
                    showToolbarTools={!isPaidMedScene && !!memoRenderToolbar}
                    isMsgEnd={isSectionLast}
                />
            </View>
        </View>
    );
};

export default memo(MarkDownCard);
