.aiCardStyle {
    background: #fff;
    box-sizing: border-box;
    overflow-x: hidden;
    position: relative;

    &FitContent {
        width: fit-content;
    }
}

.aiCardStyle:first-child {
    margin-top: 0;
}

.doctorCardBorder {
    border-radius: 12px 63px 63px;
}

.patientCardBorder {
    border-radius: 63px 12px 63px 63px;
}

.interrupted {
    color: #858585;
}

.aiFeedBack {
    border-top: 1px solid #e0e0e0;
    color: #b7b9c1;
}
