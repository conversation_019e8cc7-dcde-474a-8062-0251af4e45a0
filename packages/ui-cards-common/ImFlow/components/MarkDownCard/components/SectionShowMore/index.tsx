import {
    createIntersectionObserver,
    getCurrentInstance,
    nextTick,
    createSelectorQuery,
    type PageInstance,
    type IntersectionObserver
} from '@tarojs/taro';

import React, {type ReactNode, memo, useRef, useEffect} from 'react';
import {View, Text, ScrollView} from '@tarojs/components';
import {WiseDownArrow} from '@baidu/wz-taro-tools-icons';
import cx from 'classnames';
import type {CommonUbcInteraction} from '../../../../index.d';
import styles from './index.module.less';

interface SectionShowMoreProps {
    content?: string;
    setShowMore?: (value: boolean) => void;
    commonUbcInteraction?: CommonUbcInteraction;
}

const SectionShowMore = memo(
    ({content, setShowMore, commonUbcInteraction}: SectionShowMoreProps) => {
        return (
            <View
                className={cx(styles.sectionShowMore, 'wz-flex wz-row-center wz-col-center')}
                onClick={() => {
                    setShowMore?.(false);
                    commonUbcInteraction?.({type: 'clk', value: 'sectionShowMore'});
                }}
            >
                <Text className='wz-fs-42'>{content}</Text>
                <WiseDownArrow className={cx(styles.sectionShowMoreIcon, 'wz-ml-15')} size={42} />
            </View>
        );
    }
);

SectionShowMore.displayName = 'SectionShowMore';

const ScrollViewComponent = memo(
    (props: {
        children?: ReactNode;
        setShowMore?: (value: boolean) => void;
        scrollAuto?: boolean;
        scrollKey?: string;
    }) => {
        const {children, setShowMore, scrollAuto, scrollKey = ''} = props || {};

        const observer = useRef<IntersectionObserver | null>(null);

        useEffect(() => {
            nextTick(() => {
                observer.current = createIntersectionObserver(
                    getCurrentInstance().page as PageInstance,
                    {
                        initialRatio: 1,
                        observeAll: true,
                        thresholds: [0, 0.25, 0.5, 0.8, 0.9, 1]
                    }
                );
                observer.current
                    ?.relativeTo(`#scroll_${scrollKey}`)
                    .observe(`#observerDom_${scrollKey}`, () => {
                        const query = createSelectorQuery();
                        query.select(`#observerDom_${scrollKey}`).boundingClientRect();
                        query.select(`#scroll_${scrollKey}`).boundingClientRect();
                        query.exec(rects => {
                            const [observerRect, scrollViewRect] = rects;

                            if (observerRect && scrollViewRect) {
                                const observerHeight = observerRect.height;
                                const containerHeight = scrollViewRect.height;

                                // 只有当内容高度真正超过容器高度时才显示展开按钮
                                if (observerHeight > containerHeight) {
                                    setShowMore?.(true);
                                    observer.current?.disconnect();
                                }
                            }
                        });
                    });
            });

            return () => {
                if (observer.current) {
                    observer.current.disconnect();
                }
            };
        }, [scrollKey, setShowMore]);

        return (
            <ScrollView
                id={`scroll_${scrollKey}`}
                className={cx(styles.scrollView, {
                    [styles.scrollViewAuto]: scrollAuto === true
                })}
            >
                <View id={`observerDom_${scrollKey}`}>{children}</View>
            </ScrollView>
        );
    }
);

ScrollViewComponent.displayName = 'ScrollViewComponent';

const SectionShowMoreHoc = memo(
    (props: {
        sectionShowMore?: SectionShowMoreProps;
        children?: ReactNode;
        setShowMore?: (value: boolean) => void;
        type?: 'scroll' | 'showMoreBtn';
        showMore?: boolean;
        scrollAuto?: boolean;
        scrollKey?: string;
        commonUbcInteraction?: CommonUbcInteraction;
    }) => {
        const {
            children = null,
            sectionShowMore,
            type,
            setShowMore,
            scrollAuto,
            scrollKey = ''
        } = props || {};

        if (!sectionShowMore?.content) return children;

        if (type === 'showMoreBtn')
            return <SectionShowMore {...{...sectionShowMore, setShowMore}} />;
        if (type === 'scroll')
            return (
                <ScrollViewComponent
                    setShowMore={setShowMore}
                    scrollAuto={scrollAuto}
                    scrollKey={scrollKey}
                >
                    {children}
                </ScrollViewComponent>
            );

        return children;
    }
);

SectionShowMoreHoc.displayName = 'SectionShowMoreHoc';

export default SectionShowMoreHoc;

export {type SectionShowMoreProps, SectionShowMore};
