import {View, Text, Image} from '@tarojs/components';
import React, {type FC, memo} from 'react';
import {marked, type Token} from 'marked';
import {utils} from '@baidu/vita-pages-im';
import cx from 'classnames';
import {MarkDdownTableCustomStyles} from '../../../../ui-cards-common/ImFlow/index.d';

import './index.less';

// String.prototype.at 的 Polyfill，用于旧版本浏览器
if (!String.prototype.at) {
    String.prototype.at = function (n) {
        // ToInteger() 抽象操作
        let index = Math.trunc(n) || 0;
        if (index < 0) index += this.length;
        if (index < 0 || index >= this.length) return undefined;
        // 获取位置 index 的代码单元
        const first = this.charCodeAt(index);
        // 检查是否是代理对的开始
        if (first >= 0xd800 && first <= 0xdbff && this.length > index + 1) {
            const second = this.charCodeAt(index + 1);
            if (second >= 0xdc00 && second <= 0xdfff) {
                return this.slice(index, index + 2);
            }
        }
        return this.charAt(index);
    };
}

// Array.prototype.at 的 Polyfill，用于旧版本浏览器
if (!Array.prototype.at) {
    Array.prototype.at = function (n) {
        // ToInteger() 抽象操作
        let index = Math.trunc(n) || 0;
        if (index < 0) index += this.length;
        if (index < 0 || index >= this.length) return undefined;
        return this[index];
    };
}

// 扩展 marked 的解析器
const renderer = new marked.Renderer();
const originalText = renderer.text.bind(renderer);

// 添加自定义 loading 语法支持
renderer.text = (token: {type: string; text: string}) => {
    let text = token.text;
    if (text.includes(':loading:')) {
        text = text.replace(/:loading:/g, '<loading-icon></loading-icon>');
    }
    if (text.includes(':gradient:')) {
        text = text.replace(/:gradient:/g, '<gradient-icon></gradient-icon>');
    }

    return originalText({...token, text});
};

marked.setOptions({
    breaks: true,
    gfm: true,
    renderer
});

const customTokenizer = {
    url(src: string) {
        // 匹配 URL 模式，只匹配纯链接
        const rule =
            // eslint-disable-next-line max-len
            /^(https?:\/\/[a-zA-Z0-9.-]+(?:\.[a-zA-Z]{2,})?(?:\/[^\s<>"'，。！？；：()]*)?|www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:\/[^\s<>"'，。！？；：()]*)?)/;
        const match = rule.exec(src);
        if (match) {
            let url = match[0];
            // 移除末尾的标点符号
            url = url.replace(/[，。！？；：、）】}）\]).]+$/, '');

            return {
                type: 'link' as const,
                raw: url,
                href: url,
                text: url,
                tokens: []
            };
        }
        return undefined;
    },

    del(src: string) {
        const cap = /^~~(?=\S)([\s\S]*?\S)~~(?!~)/.exec(src);
        if (cap) {
            return {
                type: 'del' as const,
                raw: cap[0],
                text: cap[1],
                tokens: this.lexer.inlineTokens(cap[1])
            };
        }
        return undefined;
    }
};

// 使用 marked 的 walkTokens 来处理 strong tokens
marked.use({
    tokenizer: customTokenizer
});

// 自定义扩展来处理中文粗体
const chineseStrongExtension = {
    name: 'chineseStrong',
    level: 'inline',
    start(src) {
        return src.match(/\*\*/)?.index;
    },
    tokenizer(src) {
        const rule = /^\*\*([^*\n]+?)\*\*/;
        const match = rule.exec(src);
        if (match) {
            return {
                type: 'chineseStrong',
                raw: match[0],
                text: match[1]
            };
        }
    },
    renderer(token) {
        return `<strong>${token.text}</strong>`;
    }
};

marked.use({extensions: [chineseStrongExtension]});

interface MarkdownRendererProps {
    content: string;
    className?: string;
    customStyles?: {
        table?: MarkDdownTableCustomStyles;
    };
}

interface TableCell {
    text: string;
    tokens?: Token[];
}

type TableToken = Token & {
    type: 'table';
    header: TableCell[];
    rows: TableCell[][];
    align: ('left' | 'center' | 'right' | null)[];
};

// 定义基础 token 类型
interface BaseToken {
    type: string;
    raw: string;
}

// 定义包含 tokens 的 token 类型
interface TokenWithTokens extends BaseToken {
    tokens?: Token[];
    text?: string;
}

// 定义不包含 tokens 的 token 类型
interface TokenWithoutTokens extends BaseToken {
    text: string;
}

// 联合类型，表示所有可能的 token 类型
type MarkdownToken = TokenWithTokens | TokenWithoutTokens;

const elementTypeClass = {
    block: 'element-block',
    inline: 'element-inline'
};

const getNumericPrefix = (str: string): number | null => {
    const match = str.match(/^\d+/);
    return match ? parseInt(match[0], 10) : null;
};

const MarkdownRenderer: FC<MarkdownRendererProps> = ({
    content,
    className = '',
    customStyles = {}
}) => {
    const tokens = marked.lexer(content);

    // 统一处理 token 的渲染逻辑
    const renderTokenContent = (
        token: MarkdownToken,
        idx: number,
        wrapper: (content: React.ReactNode) => React.ReactNode
    ) => {
        return wrapper(
            'tokens' in token && token.tokens ? (
                renderTokens(token.tokens)
            ) : (
                <Text key={`text-${idx}`} className='text'>
                    {token.text || ''}
                </Text>
            )
        );
    };

    // 渲染各类型 token 的独立函数
    function renderHeading(token, idx) {
        return renderTokenContent(token as TokenWithTokens, idx, content => (
            <View className={`h${token.depth} ${elementTypeClass.block}`} key={idx}>
                {content}
            </View>
        ));
    }
    function renderParagraph(token, idx) {
        return renderTokenContent(token as TokenWithTokens, idx, content => (
            <View className={`p ${elementTypeClass.block}`} key={idx}>
                {content}
            </View>
        ));
    }
    function renderList(token, idx) {
        const ordered = token.ordered;
        return (
            <View className={cx(ordered ? 'ol' : 'ul', elementTypeClass.block)} key={idx}>
                {token.items?.map((item, i) => {
                    const numericPrefix = getNumericPrefix(item?.raw);
                    const orderNumber = numericPrefix ? numericPrefix : i + 1;

                    return (
                        <View className='li' key={`${idx}-${i}`}>
                            <Text className={ordered ? 'ol-marker' : 'ul-marker'}>
                                {ordered ? `${orderNumber}.` : '•'}
                            </Text>
                            <View className='li-content'>
                                {item.tokens ? (
                                    renderTokens(item.tokens)
                                ) : (
                                    <Text>{item.text || ''}</Text>
                                )}
                            </View>
                        </View>
                    );
                })}
            </View>
        );
    }
    function renderImage(token, idx) {
        return (
            <View className='image-container' key={idx}>
                <Image src={token.href || ''} className='markdown-image' lazyLoad />
                {token.text && <Text className='image-caption'>{token.text}</Text>}
            </View>
        );
    }
    function renderLink(token, idx) {
        return (
            <View
                className='a'
                key={idx}
                onClick={() => {
                    if (!token.href) return;
                    if (process.env.TARO_ENV === 'h5') {
                        window.location.href = token.href;
                        return;
                    }
                    if (process.env.TARO_ENV === 'swan') {
                        utils.navigate({
                            url: token.href,
                            openType: 'easybrowse'
                        });
                    }
                }}
            >
                {token.tokens?.length > 0 ? renderTokens(token.tokens) : token.text || ''}
            </View>
        );
    }
    function renderCode(token, idx) {
        return (
            <View className={cx('code-block', elementTypeClass.block)} key={idx}>
                <Text className='code-content'>{token.text || ''}</Text>
            </View>
        );
    }
    function renderTable(token, idx) {
        const tableToken = token as TableToken;
        const thBgColors = customStyles?.table?.thBgColors;
        const tableStyle = customStyles?.table?.tableStyle || {};
        const tableContentStyle = customStyles?.table?.tableContentStyle || {};
        const cellStyle = customStyles?.table?.cellStyle || {};
        const firstCellStyle = customStyles?.table?.firstCellStyle || {};
        const thCellStyle = customStyles?.table?.thCellStyle || {};

        const renderTableCell = (cell: TableCell) => {
            // 如果单元格有 tokens，渲染 markdown 内容
            if (cell.tokens && cell.tokens.length > 0) {
                return <View className='table-cell-content'>{renderTokens(cell.tokens)}</View>;
            }

            // 处理特殊语法（保持原有逻辑）
            const text = cell.text;
            if (text?.includes(':gradient:')) {
                const parts = text.split(':gradient:');
                return (
                    <View className='fragment'>
                        {parts.map((part, i) => (
                            <View className='fragment' key={`part-${i}`}>
                                {part && <Text>{part}</Text>}
                                {i < parts.length - 1 && <Text className='gradient-icon'></Text>}
                            </View>
                        ))}
                    </View>
                );
            }
            return <Text>{text}</Text>;
        };

        return (
            <View className={cx('table', elementTypeClass.block)} key={idx} style={tableStyle}>
                <View className='table-content' style={tableContentStyle}>
                    <View className='table-row table-header'>
                        {tableToken.header.map((cell, i) => (
                            <View
                                className='table-cell'
                                key={`header-${i}`}
                                style={{
                                    ...(thBgColors
                                        ? {
                                            backgroundColor:
                                                  thBgColors[(i % thBgColors.length) - 1]
                                        }
                                        : {}),
                                    ...cellStyle,
                                    ...(i === 0 ? firstCellStyle : {}),
                                    ...thCellStyle
                                }}
                            >
                                {renderTableCell(cell)}
                            </View>
                        ))}
                    </View>
                    {tableToken.rows.map((row, rowIdx) => (
                        <View className='table-row' key={rowIdx}>
                            {row.map((cell, i) => (
                                <View
                                    className='table-cell'
                                    key={`row-${rowIdx}-cell-${i}`}
                                    style={{
                                        ...cellStyle,
                                        ...(i === 0 ? firstCellStyle : {})
                                    }}
                                >
                                    {renderTableCell(cell)}
                                </View>
                            ))}
                        </View>
                    ))}
                </View>
            </View>
        );
    }
    function renderText(token, idx) {
        if (token.text?.includes(':loading:')) {
            const parts = token.text.split(':loading:');
            return (
                <View className='fragment' key={idx}>
                    {parts.map((part, i) => (
                        <View className='fragment' key={`part-${i}`}>
                            {part && <Text>{part}</Text>}
                            {i < parts.length - 1 && <Text className='loading-icon'></Text>}
                        </View>
                    ))}
                </View>
            );
        }
        if (token.text?.includes(':gradient:')) {
            const parts = token.text.split(':gradient:');
            return (
                <View className='fragment' key={idx}>
                    {parts.map((part, i) => (
                        <View className='fragment' key={`part-${i}`}>
                            {part && <Text>{part}</Text>}
                            {i < parts.length - 1 && <Text className='gradient-icon'></Text>}
                        </View>
                    ))}
                </View>
            );
        }
        return renderTokenContent(token as TokenWithTokens, idx, content => content);
    }
    function renderStrong(token, idx) {
        return renderTokenContent(token as TokenWithTokens, idx, content => (
            <View key={idx} className='bold'>
                {content}
            </View>
        ));
    }
    function renderEm(token, idx) {
        return renderTokenContent(token as TokenWithTokens, idx, content => (
            <View key={idx} className='italic'>
                {content}
            </View>
        ));
    }
    function renderDel(token, idx) {
        return renderTokenContent(token as TokenWithTokens, idx, content => (
            <View key={idx} className='strikethrough'>
                {content}
            </View>
        ));
    }
    function renderCodespan(token, idx) {
        return (
            <Text key={idx} className='inline-code'>
                {token.text || ''}
            </Text>
        );
    }
    function renderHr(idx) {
        return <View key={idx} className={cx('hr', elementTypeClass.block)} />;
    }
    function renderBlockquote(token, idx) {
        return renderTokenContent(token as TokenWithTokens, idx, content => (
            <View key={idx} className='blockquote'>
                {content}
            </View>
        ));
    }
    function renderHtml(token, idx) {
        const htmlContent = token.text || '';

        // 处理 <br> 标签 - 直接转换为组件
        if (htmlContent.trim() === '<br>' || htmlContent.trim() === '<br/>') {
            return <View key={idx} className='br' />;
        }

        // todo 是否渲染出html字符串
        return (
            <View key={idx} className={cx('html-content', elementTypeClass.block)}>
                <Text>{htmlContent}</Text>
            </View>
        );
    }
    function renderBr(idx) {
        return <View key={idx} className='br' />;
    }
    function renderTag(token, idx) {
        return <Text key={idx}>{token.text || ''}</Text>;
    }
    function renderDefault() {
        return null;
    }

    function renderTokens(tokens: Token[]): React.ReactNode[] {
        return tokens.map((token, idx) => {
            try {
                switch (token.type) {
                    case 'heading':
                        return renderHeading(token, idx);
                    case 'paragraph':
                        return renderParagraph(token, idx);
                    case 'list':
                        return renderList(token, idx);
                    case 'image':
                        return renderImage(token, idx);
                    case 'link':
                        return renderLink(token, idx);
                    case 'autolink':
                        return renderLink(token, idx);
                    case 'code':
                        return renderCode(token, idx);
                    case 'table':
                        return renderTable(token, idx);
                    case 'space':
                        return <View key={idx} className='space' />;
                    case 'text':
                        return renderText(token, idx);
                    case 'strong':
                    case 'chineseStrong':
                        return renderStrong(token, idx);
                    case 'em':
                        return renderEm(token, idx);
                    case 'del':
                        return renderDel(token, idx);
                    case 'codespan':
                        return renderCodespan(token, idx);
                    case 'hr':
                        return renderHr(idx);
                    case 'blockquote':
                        return renderBlockquote(token, idx);
                    case 'html':
                        return renderHtml(token, idx);
                    case 'br':
                        return renderBr(idx);
                    case 'tag':
                        return renderTag(token, idx);
                    default:
                        return renderDefault();
                }
            } catch (error) {
                return null;
            }
        });
    }

    return <View className={`markdown ${className}`}>{renderTokens(tokens)}</View>;
};

export default memo(MarkdownRenderer);
