import cx from 'classnames';
import {eventCenter} from '@tarojs/taro';
import {memo, useCallback, useEffect, useRef} from 'react';
import {ScrollView, View} from '@tarojs/components';

import {
    ubcCommonClkSend,
    ubcCommonViewSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';

import styles from './index.module.less';
import {RhetoricalQuestionsProps} from './index.d';

const RhetoricalQuestions = memo((props: RhetoricalQuestionsProps) => {
    const {
        page,
        rhetoricalQuestions = [],
        msgId = '',
        ext = {},
        ubcValue = 'ImFlowRhetoricalQuestions',
        readonly,
        customStyles = {}
    } = props;

    const isLoading = useRef(false);

    useEffect(() => {
        ubcCommonViewSend({
            value: ubcValue,
            ext: {
                product_info: {
                    question: rhetoricalQuestions?.map(item => item.question),
                    options: rhetoricalQuestions?.map(item => item.options),
                    msgId,
                    ...ext
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 发送
    const handleAnswer = useCallback(
        (answer, question) => {
            if (isLoading.current) {
                return;
            }
            isLoading.current = true;
            // 携带问题答案对，用于反问场景
            eventCenter.trigger('publicSendMsg', {
                args: {
                    msg: {
                        type: 'richText',
                        sceneType: 'imFlowReplyBotQuestions',
                        content: {
                            text: answer,
                            hiddenInfoType: 'rhetoricalQuestion', // 场景枚举，rhetoricalQuestion：反问携带问题答案对
                            hiddenInfo: {
                                rhetoricalQuestionHiddenInfo: {
                                    qaPairs: [
                                        {
                                            question,
                                            answer: [answer]
                                        }
                                    ]
                                }
                            }
                        },
                        contentType: 1000
                    }
                },
                _symbol: 'QuickReply',
                sourcePageType: page === 'im' ? 'im' : 'docIm'
            });

            const t = setTimeout(() => {
                t && clearTimeout(t);
                isLoading.current = false;
            }, 500);

            ubcCommonClkSend({
                value: ubcValue,
                ext: {
                    product_info: {
                        rhetoricalQuestions: answer,
                        msgId,
                        ...ext
                    }
                }
            });
        },
        [ext, msgId, page, ubcValue]
    );

    return (
        <View className={cx('wz-fs-51')}>
            {rhetoricalQuestions?.map((item, index) => {
                if (!item?.question || !item?.options?.length) {
                    return null;
                }
                return (
                    <View key={index} className={cx(styles.container, 'wz-mt-51')}>
                        <View
                            className={cx(styles.question, 'wz-plr-42', 'wz-ptb-45', 'wz-flex-1')}
                        >
                            {item?.question}
                        </View>
                        {!readonly && (
                            <ScrollView scrollX className={cx(styles.optionsLayout, 'wz-mt-36')}>
                                <View className={cx(styles.options)}>
                                    {item.options.map((option, idx) => (
                                        <View
                                            key={idx}
                                            className={cx(
                                                styles.option,
                                                customStyles?.paidActiveBgColor &&
                                                    styles.paidActiveBgColor
                                            )}
                                            onClick={() =>
                                                handleAnswer(option.content, item?.question)
                                            }
                                        >
                                            {option.content}
                                        </View>
                                    ))}
                                </View>
                            </ScrollView>
                        )}
                    </View>
                );
            })}
        </View>
    );
});

RhetoricalQuestions.displayName = 'RhetoricalQuestions';

export default RhetoricalQuestions;
