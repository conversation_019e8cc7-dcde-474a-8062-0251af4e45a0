.container {
    .question {
        background-color: #fff;
        border-radius: 12px 63px 63px;
        line-height: 87px;
        word-wrap: break-word;
        width: fit-content;
    }

    .options {
        display: flex;
        flex-wrap: nowrap;
        overflow: auto hidden;

        &Layout {
            position: relative;
            flex: 1;

            &::after {
                content: '';
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                margin-right: -6px;
                width: 90px;
                height: 100%;
                background: linear-gradient(-90deg, #eff3f9 0%, rgb(255 255 255 / 0%) 100%)
                    no-repeat;
                pointer-events: none;
                z-index: 100;
            }
        }

        .option {
            padding: 33px 72px;
            flex-shrink: 0;
            background-color: #fff;
            color: #000311;
            margin-right: 36px;
            border-radius: 60px;

            &:last-child {
                margin-right: 90px;
            }

            &:active {
                background-color: #dcdde0;
            }

            &.paidActiveBgColor:active {
                background-color: rgb(78 110 242 / 15%);
            }
        }
    }
}
