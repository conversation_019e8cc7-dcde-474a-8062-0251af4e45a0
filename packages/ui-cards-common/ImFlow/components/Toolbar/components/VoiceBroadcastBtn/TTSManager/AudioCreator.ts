import {IHandlerCallbackNode} from '../index.d';

class AudioCreator {
    private $play: () => void = () => {};
    private $stop: () => void = () => {};
    private $seek: (timing: number) => void = () => {};
    private audioElement: HTMLAudioElement | null = null;
    private callbackNodes: IHandlerCallbackNode | null = null;

    /**
     * @description 初始化内部音频元素
     * @returns {HTMLAudioElement} 返回一个 HTMLAudioElement 类型的音频元素，并将其添加到 body 中
     */
    private initInternalAudioElement() {
        const audioElement = document.createElement('audio');
        audioElement.id = 'vita-tts-audio';
        document.body.appendChild(audioElement);
        return audioElement;
    }

    /**
     * @description
     * TTSPlayer 构造函数，用于初始化 TTSPlayer 对象。
     * 参数：
     * - binarySource {string} - TTS 语音二进制源文件的字符串形式。
     * - callbackNodes {IHandlerCallbackNode} - 回调节点，包含 onPlay、onError、onEnd 三个方法。
     *
     * 返回值：无。
     */
    constructor(binarySource: string, callbackNodes: IHandlerCallbackNode) {
        // 获取 audio 元素
        let audioElement = document.getElementById('vita-tts-audio') as HTMLAudioElement;
        // 如果不存在则创建一个
        if (!audioElement) {
            audioElement = this.initInternalAudioElement();
        }

        // 初始化muted为静音
        audioElement.muted = false;
        // 设置 audio 元素的 src 属性
        audioElement.src = binarySource;

        this.$play = audioElement.play.bind(audioElement);
        this.$stop = audioElement.pause.bind(audioElement);
        this.$seek = (timing: number) => (audioElement.currentTime = timing);
        this.audioElement = audioElement;
        this.callbackNodes = callbackNodes;

        audioElement.addEventListener('play', this.callbackNodes.onPlay);
        audioElement.addEventListener('error', this.callbackNodes.onError);
        audioElement.addEventListener('ended', this.callbackNodes.onEnd);
    }

    play() {
        this.$play();
    }

    stop() {
        this.$stop();
        this.$seek(0);
    }

    remove() {
        this.audioElement?.removeEventListener('play', this.callbackNodes!.onPlay);
        this.audioElement?.removeEventListener('error', this.callbackNodes!.onError);
        this.audioElement?.removeEventListener('ended', this.callbackNodes!.onEnd);
    }

    destroy() {
        this.stop();
        this.remove();
        setTimeout(() => {
            this.audioElement = null;
        });
    }
}

export default AudioCreator;
