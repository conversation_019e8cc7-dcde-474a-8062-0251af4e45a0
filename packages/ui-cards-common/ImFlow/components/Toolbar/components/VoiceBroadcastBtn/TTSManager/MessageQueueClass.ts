interface MessageBase {
    msgKey: string;
    msgStatus: MessageQueueState;
    [K: string]: any;
}

export enum MessageQueueState {
    RENDERING,
    FINISHED,
    WAITING,
    INTERRUPT
}

interface MessageQueue<T extends MessageBase> {
    current(): T | undefined;
    push(message: T): number;
    clear(): void;
    findIdxById(id: string, idType?: keyof T): number;
    nextWaiting(): T | undefined;
    updateItemStatusById(id: string, idType: keyof T, msgStatus: MessageQueueState): void;
    getItemById(id: string, idType?: keyof T): T | undefined;
    getItemByIndex(index: number): T | undefined;
    updateItemById(id: string, idType: keyof T, message: T): void;
}

class MessageQueueClass implements MessageQueue<MessageBase> {
    private messages: MessageBase[] = [];

    /**
     * 构造函数，初始化消息列表为空数组
     */
    constructor() {
        this.messages = [];
    }

    /**
     * 返回当前消息，如果没有则返回undefined
     *
     * @returns {MessageBase|undefined} 返回当前消息或undefined
     */
    current(): MessageBase | undefined {
        return this.messages[0];
    }
    /**
     * @description
     * 将一个消息推入到队列中，返回新的长度。
     *
     * @param message {MessageBase} 需要推入的消息对象，必须是继承自MessageBase类的对象。
     *
     * @returns {number} 返回新的队列长度，即推入后的队列长度。
     */
    push(message: MessageBase): number {
        return this.messages.push(message);
    }
    /**
     * 清空消息队列
     *
     * @returns {void} 无返回值
     */
    clear(): void {
        this.messages.length = 0;
        this.messages = [];
    }
    /**
     * 根据ID查找消息在数组中的索引，可指定ID类型。如果未提供ID类型，则默认为'msgKey'。
     *
     * @param id 目标ID字符串
     * @param idType ID类型，默认为undefined，表示使用'msgKey'作为ID类型
     * @returns 返回目标ID在数组中的索引，若不存在则返回-1
     */
    findIdxById(id: string, idType?: keyof MessageBase | undefined): number {
        return this.messages.findIndex(message => message[idType || 'msgKey'] === id);
    }
    /**
     * 返回等待中的下一条消息，如果没有则返回undefined
     *
     * @returns {MessageBase | undefined} 等待中的下一条消息，如果没有则返回undefined
     */
    nextWaiting(): MessageBase | undefined {
        return this.messages.find(message => message.msgStatus === MessageQueueState.WAITING);
    }
    /**
     * @description 根据ID更新消息状态
     *
     * @param {string} id ID值，可以是messageId、correlationId或replyTo
     * @param {keyof MessageBase} idType ID类型，可选值为'messageId', 'correlationId', 'replyTo'中的一个
     * @param {MessageQueueState} msgStatus 消息状态，可选值为'success', 'failure', 'retrying'中的一个
     *
     * @returns {void} 无返回值
     */
    updateItemStatusById(
        id: string,
        idType: keyof MessageBase,
        msgStatus: MessageQueueState
    ): void {
        const index = this.findIdxById(id, idType);
        if (index > -1) {
            this.messages[index].msgStatus = msgStatus;
        }
    }
    /**
     * @description 根据KEY获取消息对象，可指定KEY类型
     * @param {string} key KEY值，必填参数
     * @param {keyof MessageBase} [idType] KEY类型，默认为'msgKey'，可选参数
     * @returns {MessageBase | undefined} 返回匹配的消息对象或undefined，如果没有找到匹配项
     */
    getItemById(id: string, idType?: keyof MessageBase | undefined): MessageBase | undefined {
        return this.messages.find(message => message[idType || 'msgKey'] === id);
    }
    /**
     * @description 根据索引获取消息对象，返回undefined表示不存在该索引的消息对象
     * @param {number} index 消息索引，从0开始
     * @returns {MessageBase|undefined} 返回消息对象或undefined，如果不存在该索引的消息对象
     */
    getItemByIndex(index: number): MessageBase | undefined {
        return this.messages[index];
    }
    /**
     * @description 根据KEY更新消息项，并将其合并到原始消息中
     *
     * @param {string} key KEY值
     * @param {keyof MessageBase} idType ID类型，可以是'id', 'cid', 'mid'之一
     * @param {MessageBase} message 包含要更新的属性的消息对象
     *
     */
    updateItemById(id: string, idType: keyof MessageBase, message: MessageBase): void {
        const index = this.findIdxById(id, idType);
        if (index > -1) {
            this.messages[index] = Object.assign(this.messages[index], message);
        }
    }
}

export default MessageQueueClass;
