// import request from '../utils/request';
import {text2audio} from '../../../../../models/services/tts';
import AudioCreator from './AudioCreator';

interface TTSSourceParams {
    content: string;
    contentSign: string;
    input: string;
    tts?: Tts;
}

interface Tts {
    cuid: string;
    audioCtrl?: string;
    vol?: number;
    xml?: number;
    pit?: number;
    rate?: number;
    per?: number;
    aue?: number;
    textCtrl?: string;
    spd?: number;
}

interface CallbackData {
    msgKey: string;
    url?: string;
}

interface TTSCallback {
    onStart: (data: CallbackData) => void;
    onEnd: (data?: CallbackData) => void;
    onPreLoadFinished: (data?: CallbackData) => void;
    onError: (err: CallbackData) => void;
}

interface TTSService {
    getSource: (params: TTSSourceParams) => Promise<any>;
    textRender: (params: any) => Promise<any>;
    stop: () => void;
}

export class AudioHandlerType {
    play() {}
    stop() {}
    destroy() {}
    remove() {}
}

export default class TTSServiceImpl implements TTSService {
    // 语音播放回调
    private callback: TTSCallback | null = null;
    // 音频处理器
    private audioHandler: AudioHandlerType | null = null;
    private msgKey: string = '';

    /**
     * 构造函数，用于初始化对象属性和方法。
     *
     * @param callback TTSCallback 类型，回调函数，在TTS播放完成或出错时会被调用。
     */
    constructor(callback: TTSCallback) {
        // 初始化回调
        this.callback = callback;
    }

    /**
     * 当音频开始播放时触发的回调函数。
     * 向回调函数传递一个对象，包含消息KEY（msgKey）属性。
     */
    private onAudioStart() {
        this.callback?.onStart({
            msgKey: this.msgKey
        });
    }

    /**
     * 当音频播放结束时触发的回调函数。
     * 在音频播放完成后，会调用此函数并向回调函数传入一个包含 msgKey 属性的对象。
     *
     * @private
     */
    private onAudioEnd() {
        this.callback?.onEnd({
            msgKey: this.msgKey
        });
    }

    /**
     * 当音频播放出错时触发的回调函数。
     * 将错误信息和消息ID一起传给回调函数。
     *
     * @param err 错误对象，包含错误码、错误信息等属性。
     */
    private onAudioError(err) {
        this.callback?.onError(Object.assign({}, err, {msgKey: this.msgKey}));
    }

    // 获取音频源
    async getSource(params) {
        const paramsTrans = {
            data: {
                tex: params.message,
                msgCnt: params.msgCnt || '',
                msgCntSign: params.msgCntSign || '',
                sessionId: params.sessionId,
                msgId: params.msgId,
                msgKey: params.msgKey
            }
        };
        const [err, res] = await text2audio(paramsTrans);
        if (!err && res?.data) {
            return res.data.link || '';
        } else {
            this.callback?.onError(Object.assign({}, {...err}, {msgKey: this?.msgKey}));
            throw err;
        }
    }

    // 播放消息
    async textRender(params) {
        this.msgKey = params?.msgKey;
        // 使用http播放，清除audio的绑定事件
        this.audioHandler?.remove?.();
        // 如果有预加载的音频，先播放预加载的音频
        if (params?.url) {
            this.audioHandler = new AudioCreator(params?.url, {
                onPlay: this.onAudioStart.bind(this),
                onEnd: this.onAudioEnd.bind(this),
                onError: this.onAudioError.bind(this)
            });
            this.audioHandler?.play();
            return;
        }
        try {
            const link = await this.getSource(params);
            this.audioHandler = new AudioCreator(link, {
                onPlay: this.onAudioStart.bind(this),
                onEnd: this.onAudioEnd.bind(this),
                onError: this.onAudioError.bind(this)
            });
            this.audioHandler?.play();
        } catch (err) {
            this.callback?.onError(Object.assign({}, err, {msgKey: params?.msgKey}));
        }
    }

    // 预加载消息
    async textPreLoad(params) {
        try {
            const link = await this.getSource(params);
            this.callback?.onPreLoadFinished({
                url: link,
                msgKey: params?.msgKey
            });
        } catch (err) {
            this.callback?.onError(Object.assign({}, err, {msgKey: params?.msgKey}));
        }
    }

    // 停止
    stop() {
        this.audioHandler?.destroy();
        this.audioHandler = null;
    }
}
