import {IServiceHandlerType, TTSOptions, Message} from '../index.d';
import MessageQueueClass from './MessageQueueClass';
import TTSService from './TTSService';

export const MessageQueueState = {
    RENDERING: 0,
    FINISHED: 1,
    WAITING: 2,
    INTERRUPT: 3
} as const;

/**
 * TTSManager
 * 播放服务
 */
export default class TTSManager {
    private static instance: TTSManager | null = null;
    private messageQueue: any;
    private player: any;
    private currentOptions: TTSOptions | null = null;
    private inputLimit: number = 50;
    private preloadedMessagesSet = new Set<string>(); // 用于跟踪已预加载的消息ID

    private constructor() {
        this.initPlayer();
    }

    /**
     * 获取单例对象，如果没有则创建一个新的实例并返回。
     *
     * @static
     * @returns {TTSManager} 返回单例对象 TTSManager
     */
    public static getInstance(): TTSManager {
        if (!TTSManager.instance) {
            TTSManager.instance = new TTSManager();
        }
        return TTSManager.instance;
    }

    /**
     * 初始化播放器
     */
    private initPlayer() {
        // 构建空消息队列
        this.messageQueue = new MessageQueueClass();
        // 构建播放器实例
        this.player = new TTSService({
            onStart: data => this.serviceDispatch(IServiceHandlerType.TTS_START, data),
            onEnd: data => this.serviceDispatch(IServiceHandlerType.TTS_END, data),
            onError: data => this.serviceDispatch(IServiceHandlerType.TTS_ERROR, data),
            onPreLoadFinished: data =>
                this.serviceDispatch(IServiceHandlerType.TTS_PRELOAD_FINISHED, data)
        });
    }

    /**
     * 可能渲染的消息
     */
    private async maybeRenderMessage() {
        const currentMessage = this.messageQueue.current();

        if (currentMessage?.msgStatus === MessageQueueState.WAITING) {
            this.messageQueue.updateItemStatusById(
                currentMessage.msgKey,
                'msgKey',
                MessageQueueState.RENDERING
            );
            await this.pushMessage(currentMessage);
        }
    }

    /**
     * 发送消息
     */
    private async pushMessage(data: Message) {
        const _params = {
            ...this.currentOptions?.params,
            ...data
        };
        // 更新预加载消息状态为RENDERING
        this.messageQueue.updateItemStatusById(data.msgKey, 'msgKey', MessageQueueState.RENDERING);
        this.player?.textRender(_params);
    }

    /**
     * 停止播放
     */
    public stop(): void {
        if (this.player) {
            this.messageQueue.clear();
            this.player.stop();
        }
    }

    /**
     * 预加载下一条消息的逻辑
     */
    private async preloadNextMessage(currentMsgKey: string) {
        const currentIndex = this.messageQueue.findIdxById(currentMsgKey, 'msgKey');
        let nextIndex = currentIndex + 1;
        let nextMsg = this.messageQueue.getItemByIndex(nextIndex);

        while (nextMsg) {
            if (
                !this.preloadedMessagesSet.has(nextMsg.msgKey) &&
                nextMsg.msgStatus === MessageQueueState.WAITING
            ) {
                this.player
                    ?.textPreLoad({
                        ...this.currentOptions?.params,
                        ...nextMsg
                    })
                    .then()
                    .catch(error => console.error('Preload error:', error));
                break; // 退出循环，一次只预加载一个消息
            }
            nextIndex++;
            nextMsg = this.messageQueue.getItemByIndex(nextIndex);
        }
    }

    /**
     * @description 处理 TTS 开始事件，包括预加载下一条消息和通知消息开始
     * @param {Object} data 包含 msgKey的对象
     * @param {string} data.msgKey 消息 KEY
     * @returns {Promise<void>} Promise，无返回值
     * @async 是一个异步函数，需要使用 await 进行等待
     */
    private async handleTTSStart(data: {msgKey: string}) {
        // 预加载下一条消息
        await this.preloadNextMessage(data.msgKey);
        // 获取当前索引
        const idx = this.messageQueue.findIdxById(data.msgKey);

        if (idx > -1) {
            // 通知消息开始
            this.currentOptions?.onStart?.();
        }
    }

    /**
     * @description 处理 TTS 错误，包括停止播放和通知消息错误
     * @param {Object} data TTS 错误信息，包括 err（可选）字段
     * @param {string} [data.err] TTS 错误信息，如果不传则为空字符串
     */
    private async handleTTSError(data) {
        this.stop();
        // 通知消息错误
        this.currentOptions?.onError?.(data.err || '');
    }

    /**
     * @description
     * 处理TTS播放完成事件，包括移除已播放消息的ID、更新消息状态、检查是否有下一条等待状态的消息并播放下一条消息。
     *
     * @param data {object} - 包含消息ID的对象，格式为{msgKey: string}
     * @param data.msgKey {string} - 消息ID
     *
     * @returns {Promise<void>} - 返回一个Promise，当消息播放完成后resolve，否则reject。
     *
     * @async
     */
    private async handleTTSFinish(data: {msgKey: string}) {
        // 从预加载队列中移除已播放消息的ID
        this.preloadedMessagesSet.delete(data.msgKey);
        // 更新消息状态为FINISHED
        this.messageQueue.updateItemStatusById(data.msgKey, 'msgKey', MessageQueueState.FINISHED);

        // 检查是否有下一条等待状态的消息
        const nextWaitingMsg = this.messageQueue.nextWaiting();
        if (nextWaitingMsg) {
            // 如果有下一条等待的消息，播放下一条消息
            await this.pushMessage(nextWaitingMsg);
        } else {
            this.stop();
            this.currentOptions?.onEnd?.();
        }
    }

    /**
     * @description
     * 处理 TTS 预加载完成的回调函数。
     * 更新预加载队列和队列消息，添加url。
     *
     * @param {object} data - 包含 msgKey 和 url 属性的对象，分别表示消息id和预加载完成后的url。
     * @param {string} data.msgKey - 消息id。
     * @param {string} data.url - 预加载完成后的url。
     *
     */
    private handleTTSPreloadFinished(data: {msgKey: string; url: string}) {
        // 预加载完成 更新预加载队列
        this.preloadedMessagesSet.add(data.msgKey);
        // 更新队列消息，添加url
        this.messageQueue.updateItemById(data.msgKey, 'msgKey', {url: data.url});
    }

    /**
     * @description 调度服务处理器，根据类型分发不同的处理逻辑
     * @param {IServiceHandlerType} type 处理器类型，包括 TTS_START、TTS_ERROR、TTS_END、TTS_PRELOAD_FINISHED
     * @param {any} [data={}] 可选参数，默认为空对象，用于传递需要处理的数据
     * @returns {void}
     * @private
     */
    private serviceDispatch(type: IServiceHandlerType, data: any = {}) {
        switch (type) {
            case IServiceHandlerType.TTS_START:
                this.handleTTSStart(data);
                break;
            case IServiceHandlerType.TTS_ERROR:
                this.handleTTSError(data);
                break;
            case IServiceHandlerType.TTS_END:
                this.handleTTSFinish(data);
                break;
            case IServiceHandlerType.TTS_PRELOAD_FINISHED:
                this.handleTTSPreloadFinished(data);
                break;
            default:
                console.warn(`Unknown service handler type: ${type}`);
                break;
        }
    }

    /**
     * @description
     * 播放文本到语音合成器中。
     * 如果输入的内容超过限制，则会将其分割为多个部分进行处理。
     * 每次处理完一个部分后，都会等待语音合成器渲染结果，然后再继续处理下一个部分。
     *
     * @param options {TTSOptions} - 包含要播放的文本内容的选项对象
     * @param options.content {string[]} - 需要播放的文本内容，可以是字符串或者包含多个字符串的数组
     *
     * @returns {Promise<void>} - 返回一个Promise，当所有内容已经播放完成时resolve，否则reject
     *
     * @async
     */
    public async play(options: TTSOptions): Promise<void> {
        const {content} = options;
        this.currentOptions = options;

        const pushMsgItem = async (item: string) => {
            if (item === undefined) {
                return;
            }

            const msgKey = Math.random().toString(24).substring(2);
            this.messageQueue.push({
                msgStatus: MessageQueueState.WAITING,
                msgKey: msgKey,
                message: item
            });
            await this.maybeRenderMessage();
        };

        const processInput = async () => {
            const inputs = content.slice(0, this.inputLimit) ?? [];
            for (const item of inputs) {
                await pushMsgItem(item.data);
            }
        };

        await processInput();
    }

    /**
     * 销毁实例，在组件卸载时调用
     */
    public destroy(): void {
        this.stop();
        this.player = null;
        TTSManager.instance = null;
    }
}
