import cx from 'classnames';
import {View, Image} from '@tarojs/components';
import {memo, useCallback, useRef, useMemo, useState, useEffect} from 'react';
import {utils} from '@baidu/vita-pages-im';
import {getVoiceTexts} from '@baidu/vita-pages-im/src/utils/markDown';
import {eventCenter, useDidHide} from '@tarojs/taro';
import ErrorBoundary from '@baidu/wz-taro-tools-core/error-boundary';
import {voiceIcon, voicePlayingIcon} from '../../../../constants';
import TTSManager from './TTSManager';
import styles from './index.module.less';
import {VoiceBtnProps} from './index.d';

const VoiceBroadcastBtn = memo((props: VoiceBtnProps) => {
    const {voiceInfo, voiceContent = '', showVoice, sessionId = '', msgId = ''} = props;
    const {speaker = ''} = voiceInfo;
    const [playing, setPlaying] = useState(false);
    const tts = useRef(TTSManager.getInstance());
    const {ubcCommonClkSend} = utils;

    const ttsInfo = useMemo(() => {
        const voiceInfo = getVoiceTexts(voiceContent);
        return voiceInfo;
    }, [voiceContent]);

    const stopTTS = useCallback(() => {
        tts.current.stop();
        setPlaying(false);
        eventCenter.trigger(`updateTTSStatus_${sessionId}_${msgId}`, false);
    }, []);

    // 点击播报
    const handleClickVoice = useCallback(async () => {
        ubcCommonClkSend({
            value: 'agentReply_play_voice'
        });

        if (playing) {
            setPlaying(false);
            eventCenter.trigger(`updateTTSStatus_${sessionId}_${msgId}`, false);
            await tts.current.stop();
        } else {
            stopTTS();
            setPlaying(true);
            await tts.current.play({
                params: {
                    sessionId,
                    msgId
                },
                content: ttsInfo,
                speaker,
                onStart: () => {
                    setPlaying(true);
                    eventCenter.trigger(`updateTTSStatus_${sessionId}_${msgId}`, true);
                },
                onEnd: () => {
                    setPlaying(false);
                    eventCenter.trigger(`updateTTSStatus_${sessionId}_${msgId}`, false);
                },
                onError: error => {
                    setPlaying(false);
                    console.error('播放出错:', error);
                    eventCenter.trigger(`updateTTSStatus_${sessionId}_${msgId}`, false);
                }
            });
        }
    }, [ubcCommonClkSend, playing, sessionId, msgId, stopTTS, ttsInfo, speaker]);

    useEffect(() => {
        eventCenter.on('stopTTS', stopTTS);
        eventCenter.on(`startTTS_${sessionId}_${msgId}`, handleClickVoice);
        return () => {
            eventCenter.off('stopTTS', stopTTS);
            eventCenter.off(`startTTS_${sessionId}_${msgId}`, handleClickVoice);
            tts.current.destroy();
        };
    }, [stopTTS]);

    useDidHide(() => {
        stopTTS();
    });

    if (!showVoice) {
        return null;
    }

    return (
        <ErrorBoundary>
            <View
                className={cx(styles.toolIcon, 'wz-flex wz-row-center')}
                onClick={handleClickVoice}
            >
                {playing ? (
                    <Image className={styles.voiceIcon} src={voicePlayingIcon} />
                ) : (
                    <Image className={styles.voiceIcon} src={voiceIcon} />
                )}
            </View>
        </ErrorBoundary>
    );
});

VoiceBroadcastBtn.displayName = 'VoiceBroadcastBtn';

export default VoiceBroadcastBtn;
