import React, {memo, type FC, useCallback} from 'react';
import {View} from '@tarojs/components';
import cx from 'classnames';
import {WiseRightArrow} from '@baidu/wz-taro-tools-icons';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';
import styles from './index.module.less';
import type {ViewShowMoreProps} from './index.d';
const ViewShowMore: FC<ViewShowMoreProps> = ({content, commonUbcInteraction}) => {
    const handleJump = useCallback((data: ViewShowMoreProps['content']['actionInfo']) => {
        const {interaction, interactionInfo} = data;
        if (interaction === 'openLink') {
            const {url} = interactionInfo || {};
            navigate({
                url,
                openType: 'navigate'
            });
        }
    }, []);
    return (
        <View
            onClick={() => {
                handleJump(content.actionInfo);
                commonUbcInteraction?.({type: 'clk', value: 'viewMore'});
            }}
            className={cx(styles.more, 'wz-flex wz-row-center wz-fs-42 wz-mb-45')}
        >
            {content.title}
            <WiseRightArrow className='wz-fs-42 wz-ml-9' color='#848691' />
        </View>
    );
};

export default memo(ViewShowMore);
export type {ViewShowMoreProps};
