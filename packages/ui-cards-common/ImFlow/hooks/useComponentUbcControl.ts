/**
 * <AUTHOR>
 * @description  针对markdownList组件模块顺序打点上报钩子hooks 封装
 */
import {useEffect, useMemo} from 'react';
import type {ContentList, CardComponentType, ComponentUbcPosProps} from '../index.d';

export const useComponentUbcControl = ({
    type,
    msgEnd,
    curIds,
    dataSource,
    getComponentUbcIds = () => {},
    onMsgEndComponentRender = () => {}
}: {
    type: CardComponentType;
    curIds: string;
    msgEnd: boolean;
    dataSource: ContentList;
    getComponentUbcIds?: (value: ComponentUbcPosProps) => void;
    onMsgEndComponentRender?: (value: ComponentUbcPosProps) => void;
}) => {
    const {data} = dataSource;

    // 获取卡片渲染顺序数组&当前卡所在位置
    const filterIdsComponent = useMemo(() => {
        const componentIdsList =
            Object.keys(data)?.filter(key => data[key]?.component === type) || [];
        return {componentIdsList, pos: componentIdsList?.findIndex(el => el === curIds)};
    }, [curIds, data, type]);

    useEffect(() => {
        // 消息结束后对卡进行打点处理
        if (msgEnd) {
            onMsgEndComponentRender?.(filterIdsComponent);
            getComponentUbcIds?.(filterIdsComponent);
        }
    }, [msgEnd]);
};
