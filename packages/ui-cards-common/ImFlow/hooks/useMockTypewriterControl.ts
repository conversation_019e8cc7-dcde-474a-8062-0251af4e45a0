import {useState, useCallback} from 'react';

export const useMockTypewriterControl = () => {
    const [mockTypewriterStates, setMockTypewriterStates] = useState<Record<string, boolean>>({});
    // const [mockTypeWriterItems, setMockTypeWriterItems] = useState<ImFlowData[]>([]);

    // 检查是否应该渲染当前项，当前打字机流程控制下一个列表渲染
    const shouldRenderItem = useCallback(
        (list, currentIndex) => {
            for (let i = 0; i < currentIndex; i++) {
                const prevItem = list[i];
                if (prevItem?.isMockTypeWriter && !mockTypewriterStates[prevItem?.sectionId]) {
                    return false;
                }
            }
            return true;
        },
        [mockTypewriterStates]
    );

    // 检查媒体等全量数据组件是否应该显示
    const shouldShowMedia = useCallback(
        (isMockTypeWriter: boolean, sectionId: string) => {
            return isMockTypeWriter ? mockTypewriterStates[sectionId] || false : true;
        },
        [mockTypewriterStates]
    );

    // 打字机完成回调 - 添加依赖数组
    const handleTypewriterSuccess = useCallback(
        (sectionId: string) => {
            setMockTypewriterStates(prev => {
                const newState = {...prev, [sectionId]: true};
                return newState;
            });
        },
        [mockTypewriterStates]
    ); // 添加依赖数组

    return {
        mockTypewriterStates,
        shouldRenderItem,
        shouldShowMedia,
        handleTypewriterSuccess
    };
};
