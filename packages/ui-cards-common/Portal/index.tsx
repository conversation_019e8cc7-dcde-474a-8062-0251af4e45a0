import {useEffect, useState, type ReactNode} from 'react';
import {PortalProvider, usePortal} from './provider';
import PortalSlot from './slot';

/**
 * Portal 组件
 *
 * 使用 usePortal 钩子将子组件挂载到 slot 组件节点中。
 *
 * @param props - 组件属性
 * @param props.children - 要被挂载到 Portal 中的子组件
 * @returns 返回 null，因为 Portal 组件本身不会渲染任何内容，而是将子组件挂载到 slot 组件节点中
 */
const Portal = ({children}: {children: ReactNode}) => {
    const {mount, unmount} = usePortal();
    const [key] = useState(() => Symbol(`portal-${Date.now()}-${Math.random()}`));

    useEffect(() => {
        mount?.(key, children);

        return () => {
            unmount?.(key);
        };
    }, []);

    return null;
};

Portal.displayName = 'Portal';
Portal.slot = PortalSlot;
Portal.provider = PortalProvider;

export default Portal;
