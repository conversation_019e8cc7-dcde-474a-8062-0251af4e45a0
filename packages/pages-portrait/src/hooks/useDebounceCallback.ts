import {useCallback, useRef, useEffect, DependencyList} from 'react';

interface DebounceOptions {
    leading?: boolean;
    trailing?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
interface DebounceResult<T extends (...args: any[]) => any> {
    debouncedCallback: T;
    flush: () => void;
    cancel: () => void;
}

/**
 * 防抖回调 Hook
 * @param callback - 需要防抖的回调函数
 * @param delay - 延迟时间（毫秒）
 * @param deps - 依赖数组
 * @param options - 配置选项
 * @returns 包含防抖函数和控制方法的对象
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any, space-before-function-paren
function useDebounceCallback<T extends (...args: any[]) => any>(
    callback: T,
    delay: number,
    deps: DependencyList = [],
    options: DebounceOptions = {}
): DebounceResult<T> {
    const {leading = false, trailing = true} = options;
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
    const lastCallTimeRef = useRef<number>(0);
    const lastArgsRef = useRef<Parameters<T>>();

    // 组件卸载时清除定时器
    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    const debouncedCallback = useCallback(
        (...args: Parameters<T>) => {
            const now = Date.now();
            lastArgsRef.current = args;

            // leading 模式：如果是第一次调用或者距离上次调用超过延迟时间，立即执行
            if (leading && (!lastCallTimeRef.current || now - lastCallTimeRef.current >= delay)) {
                lastCallTimeRef.current = now;
                callback(...args);
            }

            // 清除之前的定时器
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }

            // trailing 模式：设置新的定时器
            if (trailing) {
                timeoutRef.current = setTimeout(() => {
                    lastCallTimeRef.current = Date.now();
                    if (lastArgsRef.current) {
                        callback(...lastArgsRef.current);
                    }
                }, delay);
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [callback, delay, leading, trailing, ...deps]
    ) as T;

    // 立即执行函数
    const flush = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
            if (lastArgsRef.current) {
                callback(...lastArgsRef.current);
            }
        }
    }, [callback]);

    // 取消执行函数
    const cancel = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }
        lastArgsRef.current = undefined;
        lastCallTimeRef.current = 0;
    }, []);

    return {
        debouncedCallback,
        flush,
        cancel
    };
}

// 简化版本，只返回防抖函数
// eslint-disable-next-line @typescript-eslint/no-explicit-any, space-before-function-paren
function useSimpleDebounceCallback<T extends (...args: any[]) => any>(
    callback: T,
    delay: number,
    deps: DependencyList = []
): T {
    const {debouncedCallback} = useDebounceCallback(callback, delay, deps);
    return debouncedCallback;
}

export default useDebounceCallback;
export {useSimpleDebounceCallback};
