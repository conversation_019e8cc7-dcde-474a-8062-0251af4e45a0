import {useCallback} from 'react';
import {useAtom} from 'jotai';
import {
    showPopupAtom,
    closePopupAtom,
    popupSuccessAtom,
    updateCurrentPopupDataAtom,
    clearAllPopupsAtom,
    showSequentialPopupsAtom,
    currentPopupAtom,
    popupQueue<PERSON>engthAtom,
    PopupType,
    PopupConfig
} from '../../store/portrait/popupAtom';
import {
    showMultiPopupAtom,
    closeMultiPopupAtom,
    activePopupsAtom,
    topPopupAtom,
    allocateZIndexAtom
} from '../../store/portrait/zIndexAtoms';
import {Patient} from '../../typings/patient';
import {RelationshipOption} from '../../typings/portrait';

// 弹窗管理 Hook
export const usePopup = () => {
    const [, showPopup] = useAtom(showPopupAtom);
    const [, closePopup] = useAtom(closePopupAtom);
    const [, popupSuccess] = useAtom(popupSuccessAtom);
    const [, updatePopupData] = useAtom(updateCurrentPopupDataAtom);
    const [, clearAllPopups] = useAtom(clearAllPopupsAtom);
    const [, showSequentialPopups] = useAtom(showSequentialPopupsAtom);
    const [currentPopup] = useAtom(currentPopupAtom);
    const [queueLength] = useAtom(popupQueueLengthAtom);

    // 多层弹窗相关
    const [, showMultiPopup] = useAtom(showMultiPopupAtom);
    const [, closeMultiPopup] = useAtom(closeMultiPopupAtom);
    const [activePopups] = useAtom(activePopupsAtom);
    const [topPopup] = useAtom(topPopupAtom);
    const [, allocateZIndex] = useAtom(allocateZIndexAtom);

    const showPatientFormPopup = (props?: {
        formData?: FormData;
        relationships?: string[];
        selectedRelationship?: string;
        onFormDataChange?: (data: FormData) => void;
        onRelationshipChange?: (relationship: string) => void;
        onSubmit?: () => void;
        onSuccess?: (data: any) => void;
        allowMultiple?: boolean;
        parentId?: string;
        onClose?: () => void;
    }) => {
        const zIndex = props?.allowMultiple ? allocateZIndex() : 1002;

        const config: PopupConfig = {
            type: PopupType.PATIENT_FORM,
            title: '完善个人信息',
            props,
            height: '75vh',
            allowMultiple: props?.allowMultiple || false,
            zIndex
        };

        if (props?.allowMultiple) {
            return showMultiPopup(config, props.parentId);
        }

        return showPopup({
            type: PopupType.PATIENT_FORM,
            title: '完善个人信息',
            props,
            height: '75vh',
            style: {
                zIndex: zIndex
            }
        });
    };

    const showRelationshipSelectPopup = (props?: {
        relationships?: Array<{
            value: string;
            disabled?: boolean;
        }>;
        currentRelationship?: string; // 添加当前关系参数
        onSelect?: (relationship: string) => void;
        onSuccess?: (data: any) => void;
        allowMultiple?: boolean;
        parentId?: string;
    }) => {
        const zIndex = props?.allowMultiple ? allocateZIndex() : 1002;

        const config: PopupConfig = {
            type: PopupType.RELATIONSHIP_SELECT,
            title: '选择成员关系',
            props,
            height: 'auto',
            allowMultiple: props?.allowMultiple || false,
            zIndex
        };

        if (props?.allowMultiple) {
            return showMultiPopup(config, props.parentId);
        }

        return showPopup({
            type: PopupType.RELATIONSHIP_SELECT,
            title: '选择成员关系',
            props,
            height: 'auto',
            style: {
                zIndex: zIndex
            }
        });
    };

    /**
     * 显示历史就诊人选择弹窗
     */
    const showHistoryPatientSelectPopup = useCallback(
        (props?: {
            historyPatients?: Patient[];
            selectedPatients?: string[];
            onSelectionChange?: (selectedIds: string[]) => void;
            onConfirm?: () => void;
            onSuccess?: (data: any) => void;
            allowMultiple?: boolean;
            parentId?: string;
        }) => {
            const zIndex = props?.allowMultiple ? allocateZIndex() : 1002;

            const config: PopupConfig = {
                type: PopupType.HISTORY_PATIENT_SELECT,
                title: '开启健康管理之旅',
                props,
                height: 'auto',
                allowMultiple: props?.allowMultiple || false,
                zIndex
            };

            if (props?.allowMultiple) {
                return showMultiPopup(config, props.parentId);
            }

            return showPopup({
                type: PopupType.HISTORY_PATIENT_SELECT,
                title: '开启健康管理之旅',
                props,
                height: 'auto',
                style: {
                    zIndex: zIndex
                }
            });
        },
        [showPopup, showMultiPopup, allocateZIndex]
    );

    const showCreateMemberPopup = (props?: {
        mode?: 'simple' | 'withHistory';
        defaultName?: string;
        relationshipOptions: RelationshipOption[];
        historyPatients?: Patient[];
        onSuccess?: (memberData: any) => void;
        allowMultiple?: boolean;
        parentId?: string;
        onClose?: () => void;
        relationships?: Array<{
            code: string;
            value: string;
        }>;
    }) => {
        const zIndex = props?.allowMultiple ? allocateZIndex() : 1002;

        const config: PopupConfig = {
            type: PopupType.CREATE_PATIENT,
            title: '创建成员档案',
            props,
            height: 'auto',
            allowMultiple: props?.allowMultiple || false,
            zIndex
        };

        if (props?.allowMultiple) {
            return showMultiPopup(config, props.parentId);
        }

        return showPopup({
            type: PopupType.CREATE_PATIENT,
            title: '创建成员档案',
            props,
            height: 'auto',
            style: {
                zIndex: zIndex
            }
        });
    };

    const showHistorySyncPopup = (props?: {
        memberId?: string;
        memberName?: string;
        onSuccess?: (syncData: any) => void;
        allowMultiple?: boolean;
        parentId?: string;
    }) => {
        const zIndex = props?.allowMultiple ? allocateZIndex() : 1002;

        const config: PopupConfig = {
            type: PopupType.HISTORY_SYNC,
            title: '历史信息同步',
            props,
            height: '75vh',
            allowMultiple: props?.allowMultiple || false,
            zIndex
        };

        if (props?.allowMultiple) {
            return showMultiPopup(config, props.parentId);
        }

        return showPopup({
            type: PopupType.HISTORY_SYNC,
            title: '历史信息同步',
            props,
            height: '75vh',
            style: {
                zIndex: zIndex
            }
        });
    };

    // 显示成功创建弹窗
    const showSuccessCreatePopup = (props?: {
        message?: string;
        memberData?: any;
        autoCloseTime?: number;
        allowMultiple?: boolean;
        parentId?: string;
    }) => {
        const zIndex = props?.allowMultiple ? allocateZIndex() : 1003; // 比其他弹窗稍高一点

        const config: PopupConfig = {
            type: PopupType.SUCCESS_CREATE,
            title: '创建成功',
            props,
            autoClose: props?.autoCloseTime || 3000,
            height: '50vh',
            placement: 'center',
            allowMultiple: props?.allowMultiple || false,
            zIndex
        };

        if (props?.allowMultiple) {
            return showMultiPopup(config, props.parentId);
        }

        return showPopup({
            type: PopupType.SUCCESS_CREATE,
            title: '创建成功',
            props,
            autoClose: props?.autoCloseTime || 3000,
            height: '50vh',
            placement: 'center',
            style: {
                zIndex: zIndex
            }
        });
    };

    /**
     * 显示历史就诊人同步流程
     * 1. 显示历史就诊人选择弹窗
     * 2. 选择完成后自动创建健康档案
     * 3. 创建成功后显示成功提示并关闭
     * 4. 联动是否展示档案首页关系同步banner
     */
    const showHistoryPatientSyncFlow = useCallback(
        (props?: {
            historyPatients?: Patient[];
            onFlowComplete?: (createdPatients: any) => void;
            onFlowError?: (error: any) => void;
            allowMultiple?: boolean;
            parentId?: string;
        }) => {
            const popupId = showHistoryPatientSelectPopup({
                historyPatients: props?.historyPatients,
                onConfirm: async () => {
                    // todo 判断是否展示关联的banner，改变其显示状态
                    console.log('历史就诊人同步确认');
                },
                onSuccess: data => {
                    // todo 判断是否展示关联的banner，改变其显示状态
                    console.log('历史就诊人同步成功:', data);
                    props?.onFlowComplete?.(data);
                },
                allowMultiple: props?.allowMultiple,
                parentId: props?.parentId
            });

            return popupId;
        },
        [showHistoryPatientSelectPopup]
    );

    const showHealthManagementGuidePopup = useCallback(
        (props?: {
            illustrationImage?: string;
            maxImageHeight?: number;
            title?: string;
            description?: string;
            buttonText?: string;
            onConfirm?: () => void;
            onSuccess?: (data: any) => void;
            showPrivacy?: boolean;
            privacyText?: string;
            privacyLinkText?: string;
            onPrivacyClick?: () => void;
            allowMultiple?: boolean;
            parentId?: string;
        }) => {
            const zIndex = props?.allowMultiple ? allocateZIndex() : 1002;

            const config: PopupConfig = {
                type: PopupType.HEALTH_MANAGEMENT_GUIDE,
                title: '开启健康管理之旅',
                props,
                height: '75vh',
                placement: 'center',
                allowMultiple: props?.allowMultiple || false,
                zIndex
            };

            if (props?.allowMultiple) {
                return showMultiPopup(config, props.parentId);
            }

            return showPopup({
                type: PopupType.HEALTH_MANAGEMENT_GUIDE,
                title: '开启健康管理之旅',
                props,
                height: '75vh',
                placement: 'center',
                style: {
                    zIndex: zIndex,
                    backgroundColor: '#fff'
                }
            });
        },
        [showPopup, showMultiPopup, allocateZIndex]
    );

    const showChildPopup = (parentId: string, config: Omit<PopupConfig, 'allowMultiple'>) => {
        return showMultiPopup(
            {
                ...config,
                allowMultiple: true,
                zIndex: allocateZIndex() // 确保子弹窗有更高的层级
            },
            parentId
        );
    };

    const closePopupWithChildren = (popupId: string) => {
        if (activePopups.length > 0) {
            closeMultiPopup(popupId);
        } else {
            closePopup(popupId);
        }
    };

    const closeAllPopups = () => {
        clearAllPopups();
        activePopups.forEach(popup => {
            closeMultiPopup(popup.id);
        });
    };

    const isPopupActive = (type: PopupType) => {
        if (currentPopup?.config.type === type) {
            return true;
        }
        return activePopups.some(popup => popup.config.type === type);
    };

    const getActivePopupsByType = (type: PopupType) => {
        const result = [];
        if (currentPopup?.config.type === type) {
            result.push(currentPopup);
        }
        const multiPopups = activePopups.filter(popup => popup.config.type === type);
        result.push(...multiPopups);
        return result;
    };

    const getPopupStats = () => {
        return {
            total: (currentPopup ? 1 : 0) + activePopups.length,
            queue: queueLength,
            active: activePopups.length,
            single: currentPopup ? 1 : 0
        };
    };

    return {
        // 基础方法
        showPopup,
        closePopup,
        popupSuccess,
        updatePopupData,
        clearAllPopups,
        showSequentialPopups,

        // 多层弹窗方法
        showMultiPopup,
        closeMultiPopup,
        showChildPopup,
        closePopupWithChildren,
        closeAllPopups,

        // 状态
        currentPopup,
        queueLength,
        activePopups,
        topPopup,

        // 显示某个弹窗
        showCreateMemberPopup,
        showHistorySyncPopup,
        showSuccessCreatePopup,
        showPatientFormPopup,
        showRelationshipSelectPopup,
        showHistoryPatientSelectPopup,
        showHealthManagementGuidePopup,

        // 组合显示弹窗流程
        showHistoryPatientSyncFlow,

        // 工具方法
        isPopupActive,
        getActivePopupsByType,
        getPopupStats
    };
};
