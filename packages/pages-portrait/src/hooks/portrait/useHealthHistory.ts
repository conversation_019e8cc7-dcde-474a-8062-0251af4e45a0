import {useState, useEffect, useCallback, useRef} from 'react';
import Taro from '@tarojs/taro';
import healthHistoryApi from '../../models/services/portrait/healthHistory';
import type {
    GetHealthHistoryRequest,
    GetHealthHistoryResponse,
    UpdateHealthHistoryRequest
} from '../../models/services/portrait/healthHistory/type';
import {HealthCategory} from '../../typings/patient';

interface UseHealthHistoryOptions {
    patientId: string;
    autoFetch?: boolean; // 是否自动获取数据
    externalData?: GetHealthHistoryResponse; // 外部传入的数据
    onUpdateSuccess?: () => void;
    onUpdateError?: (error: string) => void;
}

interface UseHealthHistoryReturn {
    // 数据状态
    healthData: HealthCategory[];
    formData: Record<string, Record<string, string>>;
    loading: boolean;
    updating: boolean;
    error: string | null;

    // 操作方法
    fetchHealthHistory: (params?: GetHealthHistoryRequest) => Promise<void>;
    updateHealthHistory: (params: UpdateHealthHistoryRequest) => Promise<void>;
    updateFormData: (categoryId: string, itemKey: string, value: string) => void;
    resetFormData: () => void;
    setExternalData: (data: GetHealthHistoryResponse) => void; // 设置外部数据

    // 数据
    rawApiData: GetHealthHistoryResponse | null;
    dataSource?: 'external' | 'fetched';
}

export const useHealthHistory = (
    options: UseHealthHistoryOptions = {
        patientId: ''
    }
): UseHealthHistoryReturn => {
    const {patientId, autoFetch = true, externalData, onUpdateSuccess, onUpdateError} = options;

    // 状态管理
    const [healthData, setHealthData] = useState<HealthCategory[]>([]);
    const [formData, setFormData] = useState<Record<string, Record<string, string>>>({});
    const [loading, setLoading] = useState(false);
    const [updating, setUpdating] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [rawApiData, setRawApiData] = useState<GetHealthHistoryResponse | null>(null);
    const [dataSource, setDataSource] = useState<'external' | 'fetched'>('fetched');

    const initialFormDataRef = useRef<Record<string, Record<string, string>>>({});

    // 初始化表单数据
    const initializeFormData = useCallback(
        (apiData: GetHealthHistoryResponse, source: 'external' | 'fetched' = 'fetched') => {
            if (apiData.status === 0 && apiData.data?.healthHistory) {
                const initialFormData: Record<string, Record<string, string>> = {};

                apiData.data.healthHistory.forEach(category => {
                    initialFormData[category.healthCategoryId] = {};
                    category.items.forEach(item => {
                        initialFormData[category.healthCategoryId][item.key] = item.value || '';
                    });
                });

                setFormData(initialFormData);
                initialFormDataRef.current = JSON.parse(JSON.stringify(initialFormData));
                setHealthData(apiData.data.healthHistory);
                setRawApiData(apiData);
                setDataSource(source);
            }
        },
        []
    );

    // 设置外部数据
    const setExternalData = useCallback(
        (data: GetHealthHistoryResponse) => {
            initializeFormData(data, 'external');
        },
        [initializeFormData]
    );

    // 获取健康史数据
    const fetchHealthHistory = useCallback(
        async (params?: GetHealthHistoryRequest) => {
            try {
                setLoading(true);
                setError(null);

                const requestParams = params || {patientId};
                if (!requestParams.patientId) {
                    throw new Error('缺少 patientId 参数');
                }

                const response = await healthHistoryApi.getHealthHistory(requestParams);
                initializeFormData(response, 'fetched');
            } catch (err) {
                const errorMsg = err instanceof Error ? err.message : '获取健康史数据失败';
                setError(errorMsg);
                console.error('获取健康史失败:', err);

                Taro.showToast({
                    title: errorMsg,
                    icon: 'none',
                    duration: 2000
                });
            } finally {
                setLoading(false);
            }
        },
        [patientId, initializeFormData]
    );

    // 更新健康史数据
    const updateHealthHistory = useCallback(
        async (params: UpdateHealthHistoryRequest) => {
            try {
                setUpdating(true);
                setError(null);

                const response = await healthHistoryApi.updateHealthHistory(params);

                if (response.status === 0) {
                    onUpdateSuccess?.();
                } else {
                    throw new Error(response.msg || '更新失败');
                }
            } catch (err) {
                const errorMsg = err instanceof Error ? err.message : '更新健康史失败';
                setError(errorMsg);
                onUpdateError?.(errorMsg);
                console.error('更新健康史失败:', err);

                Taro.showToast({
                    title: errorMsg,
                    icon: 'none',
                    duration: 2000
                });

                throw err; // 重新抛出错误，让调用方可以处理
            } finally {
                setUpdating(false);
            }
        },
        [onUpdateSuccess, onUpdateError]
    );

    // 更新表单数据
    const updateFormData = useCallback((categoryId: string, itemKey: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [categoryId]: {
                ...prev[categoryId],
                [itemKey]: value
            }
        }));
    }, []);

    // 重置表单数据
    const resetFormData = useCallback(() => {
        if (initialFormDataRef.current) {
            setFormData(JSON.parse(JSON.stringify(initialFormDataRef.current)));
        }
    }, []);

    // 处理外部传入的数据
    useEffect(() => {
        if (externalData) {
            setExternalData(externalData);
        }
    }, [externalData, setExternalData]);

    // 自动获取数据（只有在没有外部数据且autoFetch为true时才执行）
    useEffect(() => {
        if (autoFetch && patientId && !externalData) {
            fetchHealthHistory();
        }
    }, [autoFetch, patientId, externalData, fetchHealthHistory]);

    return {
        // 数据状态
        healthData,
        formData,
        loading,
        updating,
        error,

        // 操作方法
        fetchHealthHistory,
        updateHealthHistory,
        updateFormData,
        resetFormData,
        setExternalData,

        // 原始数据
        rawApiData,
        dataSource
    };
};
