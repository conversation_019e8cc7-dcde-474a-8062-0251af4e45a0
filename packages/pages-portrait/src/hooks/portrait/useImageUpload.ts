import {useState, useCallback} from 'react';
import {useAtom, useSet<PERSON>tom} from 'jotai';
import {showToast, hideToast} from '@tarojs/taro';
import type {IPicProps, ReportTag} from '../../typings/upload';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';
import {uploadFileToBos} from '../../../../pages-im/src/utils/basicAbility/upload';
import {BUCKET_NAME, ONLINE_HOST} from '../../constants/common';
import {
    extractMap<PERSON>tom,
    imgListAtom,
    isParseResultReadyAtom,
    manualFormAtom,
    selectedImgIdAtom,
    statusListAtom
} from '../../store/portrait';
import {API_HOST} from '../../models/apis/host';

import {UploadStatus} from '../../constants/upload';
import {apiToInternal, mergeExtract} from '../../pages/HealthReportUpload/utils';
import {REPORT_UPLOAD_URL} from '../../constants/links';
import {useExportLabelFetch} from './useReportData';
import {usePatientData} from './usePatientData';

const bucketConfName =
    ONLINE_HOST.indexOf(API_HOST as string) > -1 ? BUCKET_NAME[2] : `${BUCKET_NAME[2]}-test`;
const count = 9;

export const useImageUpload = () => {
    const [popupOpenStatus, setPopupOpenStatus] = useState(false);
    const setExtractMap = useSetAtom(extractMapAtom);
    const setSelectedImgId = useSetAtom(selectedImgIdAtom);
    const setIsParseResultReady = useSetAtom(isParseResultReadyAtom);
    const setManualForms = useSetAtom(manualFormAtom);

    const [statusList, setStatusList] = useAtom(statusListAtom);
    const [imgList, setImgList] = useAtom(imgListAtom);
    const {fetchReportData} = useExportLabelFetch();
    const {selectedPatientId} = usePatientData();

    /**
     * 关闭图片上传弹窗
     */
    const closeUploadPopup = useCallback(() => {
        setPopupOpenStatus(false);
    }, []);

    /**
     * 处理图片选择
     * @param res 选择的图片列表
     * @param sceneType 场景类型
     */
    const onSelectedPics = useCallback(
        async (preData: IPicProps[]) => {
            if (!preData?.length) return;
            setIsParseResultReady(false);

            showToast({
                title: '上传中...',
                icon: 'loading',
                mask: true,
                duration: 3000
            });

            try {
                setImgList(preData.map(item => ({...item, uploadStatus: UploadStatus.PENDING})));
                setStatusList(preData.map(() => UploadStatus.PENDING));
                setSelectedImgId(preData[0]?.fileName || '');

                const picData = await uploadFileToBos(preData, {
                    count, // 最大支持9个
                    bucketConfName
                });
                hideToast();
                setStatusList(picData.map(() => UploadStatus.SUCCESS));
                setImgList(picData.map(item => ({...item, uploadStatus: UploadStatus.SUCCESS})));
                setSelectedImgId(picData[0]?.fileName || '');
                navigate({
                    url: `${REPORT_UPLOAD_URL}?patientId=${encodeURIComponent(selectedPatientId || '')}`,
                    openType: 'navigate'
                });
                const res = await fetchReportData(selectedPatientId || '', picData);
                if (res?.list?.length) {
                    setExtractMap(prev => ({...prev, ...mergeExtract(res.list)}));
                    const newManualForms = {};
                    const onlineURLs = [];
                    for (let i = 0; i < res.list.length; i++) {
                        const item = res.list[i];
                        newManualForms[item.objectId] = {
                            labelType: item.reportTags
                                ? apiToInternal(item.reportTags as ReportTag[])
                                : [],
                            time: item.reportTime ?? '',
                            hospital: item.hospitalName ?? '',
                            remark: ''
                        };
                        // FIXME 百度小程序下，blob与base64的图片都无法使用taro的previewImage预览，所以这里通过获取后端返回的
                        // 在线图片的url来预览。后续小程序支持bdFile数据后，可以删除这段代码，直接使用bdFile进行预览
                        onlineURLs.push(item.image?.[0] ?? {});
                    }
                    setManualForms(prev => ({...prev, ...newManualForms}));
                    setImgList(prev => {
                        return [
                            ...prev.map((item, index) => {
                                return {
                                    ...item,
                                    onlineURL: onlineURLs[index]
                                };
                            })
                        ];
                    });
                }
                setIsParseResultReady(true);
            } catch (error) {
                console.error(error);
                hideToast();
                showToast({
                    title: error?.[0]?.toast || '上传失败',
                    icon: 'none',
                    duration: 3000
                });
                setImgList(preData.map(item => ({...item, uploadStatus: UploadStatus.FAILED})));
                setStatusList(preData.map(() => UploadStatus.FAILED));
                setIsParseResultReady(true);
            }
        },
        [
            setImgList,
            setStatusList,
            selectedPatientId,
            setExtractMap,
            setSelectedImgId,
            setIsParseResultReady,
            setManualForms
        ]
    );

    /**
     * 打开图片上传弹窗
     */
    const openUploadPopup = useCallback(() => {
        setPopupOpenStatus(true);
    }, []);

    return {
        imgList,
        setImgList,
        statusList,
        setStatusList,
        popupOpenStatus,
        closeUploadPopup,
        openUploadPopup,
        onSelectedPics
    };
};
