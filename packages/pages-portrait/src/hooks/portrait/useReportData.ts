import {useCallback} from 'react';
import {useSet<PERSON>tom} from 'jotai';
import {getExtractReport} from '../../models/services/portrait/healthReport';
import {ExtractReportParams} from '../../models/services/portrait/healthReport/index.d';
import {IPicProps} from '../../typings/upload';
import {isParseResultReadyAtom} from '../../store/portrait';

export const useExportLabelFetch = () => {
    const setIsParseResultReady = useSetAtom(isParseResultReadyAtom);

    const fetchReportData = useCallback(
        async (patientId: string, imgList: IPicProps[]) => {
            try {
                setIsParseResultReady(false);
                // 从imgList中提取有效的fileName作为objectId
                const objectIds = imgList
                    .map(item => item.fileName)
                    .filter((fileName): fileName is string => !!fileName);

                const params: ExtractReportParams = {
                    patientId,
                    objectIds
                };

                const data = await getExtractReport(params);
                setIsParseResultReady(true);

                return data;
            } catch (err) {
                console.error(err);
            }
        },
        [setIsParseResultReady]
    );

    return {
        fetchReportData
    };
};
