import {useCallback, useEffect, useRef, useState} from 'react';
import {useAtom, useAtomValue, useSet<PERSON>tom} from 'jotai';
// import {debounce} from 'lodash-es';
import {
    patientsAtom,
    selectedPatientIdAtom,
    selectedP<PERSON>ent<PERSON>tom,
    patient<PERSON>asic<PERSON>nfo<PERSON>tom,
    medicalReportsAtom,
    healthHistoryAtom,
    selectedHealthCategoryAtom,
    currentHealthCategoryAtom
} from '../../store/portrait/patientAtoms';
import {
    sortedMedicalReportsAtom,
    reportsByTypeAtom,
    latestMedicalReportAtom,
    healthHistoryStatsAtom,
    overallHealthCompletionAtom
} from '../../store/portrait/derivedAtoms';
import {
    addPatient<PERSON>tom,
    updatePatient<PERSON>tom,
    removePatient<PERSON>tom,
    addMedicalReportAtom,
    updateMedicalReport<PERSON>tom,
    removeMedicalReportAtom,
    addHealthCategoryAtom,
    updateHealthCategory<PERSON>tom,
    removeHealth<PERSON>ate<PERSON>y<PERSON><PERSON>,
    addHealth<PERSON>tem<PERSON>tom,
    updateHealth<PERSON><PERSON><PERSON><PERSON>,
    removeHealth<PERSON><PERSON><PERSON><PERSON>,
    setHistory<PERSON><PERSON>ents<PERSON><PERSON>,
    setReportTagOptionsAtom,
    setRelationshipOptionsAtom,
    addMedicalReportsAtom,
    setHasSelfAtom
} from '../../store/portrait/actionAtoms';
import {isLoginAtom, isLoginSetAtom} from '../../store/portrait/userAtoms';
import {
    historyPatientsAtom,
    pageRenderStageAtom,
    pageRenderStageSetAtom,
    reportTagOptionsAtom,
    relationshipOptionsAtom,
    introPopupDataSetAtom,
    portraitIntroPopupVisibleSetAtom,
    PAGE_RENDER_STAGE_RENDERING,
    PAGE_RENDER_STAGE_RENDERED,
    introPopupDataAtom,
    hasSelfAtom
} from '../../store/portrait';
import {getPatientsReq, getMedicalReportsReq} from '../../models/services/portrait';
import type {GetMedicalReportsReqData} from '../../models/services/portrait/index.d';

export const usePatientData = () => {
    // 基础数据
    const [patients, setPatients] = useAtom(patientsAtom);
    const [selectedPatientId, setSelectedPatientId] = useAtom(selectedPatientIdAtom);
    const selectedPatient = useAtomValue(selectedPatientAtom);
    const [basicInfo, setBasicInfo] = useAtom(patientBasicInfoAtom);
    const [medicalReports, setMedicalReports] = useAtom(medicalReportsAtom);
    const [healthHistory, setHealthHistory] = useAtom(healthHistoryAtom);
    const [selectedHealthCategoryId, setSelectedHealthCategoryId] = useAtom(
        selectedHealthCategoryAtom
    );
    const currentHealthCategory = useAtomValue(currentHealthCategoryAtom);
    const isLogin = useAtomValue(isLoginAtom);

    // 档案相关配置
    const historyPatients = useAtomValue(historyPatientsAtom);
    const reportTagOptions = useAtomValue(reportTagOptionsAtom);
    const relationshipOptions = useAtomValue(relationshipOptionsAtom);
    const hasSelf = useAtomValue(hasSelfAtom);
    const introPopupData = useAtomValue(introPopupDataAtom);

    // 派生数据
    const sortedReports = useAtomValue(sortedMedicalReportsAtom);
    const reportsByType = useAtomValue(reportsByTypeAtom);
    const latestReport = useAtomValue(latestMedicalReportAtom);
    const healthStats = useAtomValue(healthHistoryStatsAtom);
    const overallCompletion = useAtomValue(overallHealthCompletionAtom);

    // 操作方法
    const addPatient = useSetAtom(addPatientAtom);
    const updatePatient = useSetAtom(updatePatientAtom);
    const removePatient = useSetAtom(removePatientAtom);
    const addMedicalReport = useSetAtom(addMedicalReportAtom);
    const updateMedicalReport = useSetAtom(updateMedicalReportAtom);
    const removeMedicalReport = useSetAtom(removeMedicalReportAtom);
    const addHealthCategory = useSetAtom(addHealthCategoryAtom);
    const updateHealthCategory = useSetAtom(updateHealthCategoryAtom);
    const removeHealthCategory = useSetAtom(removeHealthCategoryAtom);
    const addHealthItem = useSetAtom(addHealthItemAtom);
    const updateHealthItem = useSetAtom(updateHealthItemAtom);
    const removeHealthItem = useSetAtom(removeHealthItemAtom);

    // 档案相关配置操作
    const setHistoryPatients = useSetAtom(setHistoryPatientsAtom);
    const setReportTagOptions = useSetAtom(setReportTagOptionsAtom);
    const setRelationshipOptions = useSetAtom(setRelationshipOptionsAtom);

    return {
        // 基础数据
        patients,
        selectedPatientId,
        selectedPatient,
        basicInfo,
        medicalReports,
        healthHistory,
        selectedHealthCategoryId,
        currentHealthCategory,
        isLogin,

        // 档案相关配置
        historyPatients,
        reportTagOptions,
        relationshipOptions,
        introPopupData,
        hasSelf,

        // 派生数据
        sortedReports,
        reportsByType,
        latestReport,
        healthStats,
        overallCompletion,

        // 基础操作
        setPatients,
        setSelectedPatientId,
        setBasicInfo,
        setMedicalReports,
        setHealthHistory,
        setSelectedHealthCategoryId,

        // 患者操作
        addPatient,
        updatePatient,
        removePatient,

        // 就诊资料操作
        addMedicalReport,
        updateMedicalReport,
        removeMedicalReport,

        // 健康史操作
        addHealthCategory,
        updateHealthCategory,
        removeHealthCategory,
        addHealthItem,
        updateHealthItem,
        removeHealthItem,

        // 档案相关配置操作
        setHistoryPatients,
        setReportTagOptions,
        setRelationshipOptions
    };
};

export const usePatientDataFetch = (
    patientId?: string,
    {autoFetch = false}: {autoFetch?: boolean} = {autoFetch: false}
) => {
    const [patients, setPatients] = useAtom(patientsAtom);
    const [selectedPatientId, setSelectedPatientId] = useAtom(selectedPatientIdAtom);
    const selectedPatient = useAtomValue(selectedPatientAtom);
    const [medicalReports, setMedicalReports] = useAtom(medicalReportsAtom);
    const [healthHistory, setHealthHistory] = useAtom(healthHistoryAtom);
    const pageRenderStage = useAtomValue(pageRenderStageAtom);
    const setPageRenderStage = useSetAtom(pageRenderStageSetAtom);
    const setHistoryPatients = useSetAtom(setHistoryPatientsAtom);
    const setReportTagOptions = useSetAtom(setReportTagOptionsAtom);
    const setRelationshipOptions = useSetAtom(setRelationshipOptionsAtom);
    const setIntroPopupData = useSetAtom(introPopupDataSetAtom);
    const setIntroPopupVisible = useSetAtom(portraitIntroPopupVisibleSetAtom);
    const setHasSelf = useSetAtom(setHasSelfAtom);
    const isLogin = useAtomValue(isLoginAtom);
    const setIsLogin = useSetAtom(isLoginSetAtom);

    const fetchPatientData = useCallback(
        async (
            patientId?: string,
            {selectLastPatient = false}: {selectLastPatient?: boolean} = {selectLastPatient: false}
        ) => {
            // 设置主页面渲染状态为渲染中
            setPageRenderStage(PAGE_RENDER_STAGE_RENDERING);
            const res = await getPatientsReq({patientId: patientId ?? ''});
            if (!res) {
                return;
            }
            // 设置用户是否登录
            setIsLogin(res.isLogin);
            // 设置患者数据
            setPatients(res.patients);
            // 设置当前选中患者ID
            // 如果传入patientId，则选中patientId，否则判断selectLastPatient
            // 如果selectLastPatient为true，则选中最后一个患者，否则选中第一个患者
            setSelectedPatientId(
                patientId ??
                    (selectLastPatient
                        ? res.patients[res.patients.length - 1].patientId
                        : res.patients[0].patientId)
            );
            // 设置就诊资料数据
            if (res.medicalReports?.length > 0) {
                setMedicalReports(res?.medicalReports);
            }
            // 设置健康史数据
            if (res.healthHistory?.length > 0) {
                setHealthHistory(res?.healthHistory);
            }
            // 设置历史患者数据
            setHistoryPatients(res.historyPatients);
            // 设置就诊人关系选项
            setRelationshipOptions(res.options.relationships);
            // 设置就诊资料标签
            setReportTagOptions(res.options.reportTags);
            // 设置档案功能介绍弹窗数据
            setIntroPopupData(res.introducePopup);
            // 设置档案功能介绍弹窗展示状态
            setIntroPopupVisible(res.introducePopup?.show ?? false);
            // 设置是否包含本人就诊档案
            setHasSelf(res.hasSelf);
            // 更新主页面渲染状态为渲染完成
            setPageRenderStage(PAGE_RENDER_STAGE_RENDERED);
        },
        [
            setPatients,
            setSelectedPatientId,
            setMedicalReports,
            setHealthHistory,
            setPageRenderStage,
            setHistoryPatients,
            setRelationshipOptions,
            setReportTagOptions,
            setIsLogin,
            setIntroPopupData,
            setIntroPopupVisible,
            setHasSelf
        ]
    );

    useEffect(() => {
        if (autoFetch) {
            fetchPatientData(patientId);
        }
    }, [patientId, fetchPatientData, autoFetch]);

    return {
        patients,
        selectedPatient,
        selectedPatientId,
        medicalReports,
        healthHistory,
        fetchPatientData,
        refreshPatientData: fetchPatientData,
        pageRenderStage,
        isLogin
    };
};

const RN_DEFAULT = 50; // 默认每页条数
export const FETCH_STAGE_INIT = 'init';
export const FETCH_STAGE_FETCHING = 'fetching';
export const FETCH_STAGE_FETCHED = 'fetched';
// const FETCH_STAGE_ERROR = 'error';

export const useMedicalReportsFetch = () => {
    const [fetchStage, setFetchStage] = useState(FETCH_STAGE_INIT);
    const [hasMore, setHasMore] = useState(true);
    const pnRef = useRef(1); // 当前页码
    const rnRef = useRef(RN_DEFAULT); // 每页条数
    const fetchStatusRef = useRef(false); // 是否正在加载

    const [medicalReports, setMedicalReports] = useAtom(medicalReportsAtom);
    const addMedicalReports = useSetAtom(addMedicalReportsAtom);

    const resetMedicalReports = useCallback(() => {
        pnRef.current = 1;
        rnRef.current = RN_DEFAULT;
        setHasMore(true);
    }, []);

    const fetchMedicalReports = useCallback(
        async (
            params: GetMedicalReportsReqData,
            {isReset = false}: {isReset?: boolean} = {isReset: false}
        ) => {
            if (fetchStatusRef.current) {
                return;
            }
            fetchStatusRef.current = true;
            setFetchStage(FETCH_STAGE_FETCHING);

            if (isReset) {
                resetMedicalReports();
            }

            const res = await getMedicalReportsReq({
                ...params,
                pn: pnRef.current,
                rn: rnRef.current
            });

            if (pnRef.current === 1) {
                setMedicalReports(res.medicalReports);
            } else {
                addMedicalReports(res.medicalReports);
            }

            if (res.medicalReports?.length > 0) {
                pnRef.current++;
                // 如果当前页的条数小于每页条数，则设置没有更多
                if (res.medicalReports.length < rnRef.current) {
                    setHasMore(false);
                }
            } else {
                setHasMore(false);
            }

            setFetchStage(FETCH_STAGE_FETCHED);
            fetchStatusRef.current = false;
        },
        [addMedicalReports, resetMedicalReports, setMedicalReports]
    );

    return {
        fetchStage,
        hasMore,
        medicalReports,
        fetchMedicalReports
    };
};
