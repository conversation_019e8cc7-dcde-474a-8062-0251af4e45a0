export enum InteractionEnum {
    '请求' = 'request',
    '打开链接' = 'openLink',
    '底部弹窗' = 'popup',
    '拉起支付' = 'payment',
    '关闭' = 'close',
    '全局 modal' = 'modal',
    '跳过 ai 问题' = 'skipAi',
    '发送ubc日志' = 'sendUbc',
    '重新加载' = 'reload',
    '打开pdf' = 'openPdf',
    '打开第三方小程序' = 'openOtherApp',
    '打开小程序' = 'openMiniApp',
    '请求后打开微信小程序' = 'requestWexinJump',
    '拨打电话' = 'callPhone',
    '异步请求' = 'asyncRequest',
    'toast弹层' = 'toast',
    '渲染物料数据' = 'renderPlanMaterial',
    'ai历史记录弹窗' = 'historyMsgPop'
}

export type InteractionType = `${InteractionEnum}`;

export interface InteractionInfo {
    intent?: string;
    url?: string;
    sceneType?: string;
    method?: 'GET' | 'POST';
    contentType?: string;
    wxPayConfig?: unknown;
    wxPayInfo?: {[key: string]: string};
    inlinePaySign?: string;
    type?: string;
    version?: string;
    fail?: () => void;
    success?: () => void;
}

export interface ActionInfo {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}
