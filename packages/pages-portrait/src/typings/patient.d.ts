// 患者信息
export interface Patient {
    patientId: string;
    name: string;
    age: string;
    gender?: '男' | '女';
    relationship: string; // 与当前用户的关系
    isCertified: boolean; // 是否实名
    isSelf: boolean; // 是否本人
    icon: string; // 头像
    createTime: number; // 创建时间
    birthDate?: string; // 出生日期
    height?: number;
    weight?: number;
    bmi?: number;
    area?: string; // 地址，省市区 。 逗号分隔
    avatar?: string; // 头像
    educationLevel?: string; // 教育程度
    maritalStatus?: string; // 婚姻状况
    medicalReports?: MedicalReport[]; // 就诊资料类
    healthHistory?: HealthCategory[]; // 健康史分类
}

interface ReportImage {
    objectId: string;
    thumb: string; // 缩略图
    origin: string; // 原图
}

// 就诊资料
export interface MedicalReport {
    medicalReportId: string;
    reportTags: string[];
    hospitalName: string;
    reportTime: string; // 报告时间
    createTime: number; // 创建时间
    images: ReportImage[]; // 报告图片
    comments?: string; // 报告备注
}

// 健康史分类
export interface HealthCategory {
    healthCategoryId: string;
    categoryName: string;
    items: HealthItem[];
}

interface HealthItemOption {
    label: string;
    value: string | number;
}

// 健康史项目
export interface HealthItem {
    key: string;
    name: string;
    value: string | number;
    optionType: number;
    options: HealthItemOption[];
}
