import React from 'react';
import {View} from '@tarojs/components';
import {Popup, SafeArea} from '@baidu/wz-taro-tools-core';
import {useAtom} from 'jotai';
import {currentPopupAtom, closePopupAtom, PopupType} from '../../../../store/portrait/popupAtom';
import {activePopupsAtom, closeMultiPopupAtom} from '../../../../store/portrait/zIndexAtoms';
import RelationshipSelectPopup from '../RelationshipSelectPopup';
import HistoryPatientSelectPopup from '../HistoryPatientSelectPopup';
import HealthManagementGuidePopup from '../HealthManagementGuidePopup';
import CreatePatientPopup from '../CreatePatientPopup';
import styles from './index.module.less';

const PopupManager: React.FC = () => {
    const [currentPopup] = useAtom(currentPopupAtom);
    const [, closePopup] = useAtom(closePopupAtom);
    const [activePopups] = useAtom(activePopupsAtom);
    const [, closeMultiPopup] = useAtom(closeMultiPopupAtom);
    // TODO 未来考虑单独拿出去维护，后续用一个regiter给popupmananer进行注册实例
    const getPopupContent = (type: string, data: any) => {
        switch (type) {
            // 添加新弹窗类型
            case PopupType.RELATIONSHIP_SELECT:
                return <RelationshipSelectPopup data={data} />;
            case PopupType.HISTORY_PATIENT_SELECT:
                return <HistoryPatientSelectPopup data={data} />;
            case PopupType.HEALTH_MANAGEMENT_GUIDE:
                return <HealthManagementGuidePopup data={data} />;
            case PopupType.CREATE_PATIENT:
                return <CreatePatientPopup data={data} />;
            default:
                return null;
        }
    };

    // 计算弹窗的 z-index
    const getPopupZIndex = (popup: any, index: number = 0) => {
        // 确保单层弹窗有明确的 z-index
        if (popup.zIndex && popup.zIndex !== 'auto') {
            return popup.zIndex;
        }

        // 为单层弹窗分配固定的高层级
        if (!activePopups.length && currentPopup) {
            return 1002; // 比背景遮罩(999)和容器(1001)都高
        }

        // 为多层弹窗分配递增的层级
        return 1010 + index * 10;
    };

    // 渲染单个弹窗
    const renderPopup = (popup: any, onClose: () => void, index: number = 0) => {
        const {config, data} = popup;
        const zIndex = getPopupZIndex(popup, index);
        // 默认配置
        const defaultConfig = {
            placement: 'bottom' as const,
            height: '75vh',
            style: {backgroundColor: '#F5F5F5'},
            titleStyle: {backgroundColor: '#FFFFFF', border: 'none'},
            catchMove: false,
            rounded: true
        };

        const finalConfig = {...defaultConfig, ...config};

        return (
            <Popup
                key={popup.id}
                open={popup.isVisible}
                rounded={finalConfig.rounded}
                catchMove={finalConfig.catchMove}
                placement={finalConfig.placement}
                title={config.title || ''}
                style={{
                    height: finalConfig.height,
                    zIndex: zIndex,
                    ...finalConfig.style
                }}
                titleStyle={finalConfig.titleStyle}
                onClose={onClose}
                scrollWithAnimation={false}
            >
                <Popup.Close />
                {getPopupContent(config.type, data)}
                <SafeArea position='bottom' />
            </Popup>
        );
    };

    return (
        <View className={styles.popupManager}>
            {/* 单层弹窗 */}
            {currentPopup &&
                currentPopup.isVisible &&
                renderPopup(currentPopup, () => closePopup())}

            {/* 多层弹窗容器 */}
            {activePopups.length > 0 && (
                <View
                    className={styles.multiPopupContainer}
                    style={{
                        zIndex: 1001
                    }}
                >
                    {activePopups
                        .sort((a, b) => a.zIndex - b.zIndex)
                        .map((popup, index) => (
                            <View
                                key={popup.id}
                                className={styles.popupWrapper}
                                style={{
                                    zIndex: getPopupZIndex(popup, index)
                                }}
                            >
                                {renderPopup(popup, () => closeMultiPopup(popup.id), index)}
                            </View>
                        ))}
                </View>
            )}
        </View>
    );
};

export default PopupManager;
