import React, {memo, useMemo} from 'react';
import classNames from 'classnames';
import {View, Text, ScrollView} from '@tarojs/components';
import {HIcon, HImage} from '@baidu/health-ui';
import useDebounceCallback from '../../../../hooks/useDebounceCallback';
import {ubcCommonClkSend} from '../../../../../../pages-im/src/utils/generalFunction/ubc';
import {CLoginButton} from '../../../../../../ui-cards-common';
import {usePatientData, usePatientDataFetch} from '../../../../hooks/portrait/usePatientData';
import {generateScrollIntoViewId} from '../../utils';
import {UBC_FIELD_PAGE} from '../../constants';

import styles from './index.module.less';
interface PatientTabsProps {
    onAddPatient?: () => void;
    onChange?: (patientId: string) => void;
}

const PatientTabs: React.FC<PatientTabsProps> = props => {
    const {patients, selectedPatientId} = usePatientData();
    const {isLogin, refreshPatientData} = usePatientDataFetch();
    const {onAddPatient, onChange} = props;
    const handleTabClick = useDebounceCallback(
        (patientId: string, index: number) => {
            if (patientId === selectedPatientId) {
                return;
            }
            onChange?.(patientId);
            refreshPatientData(patientId);
            if (index < 3) {
                ubcCommonClkSend({
                    value: 'patientTab_' + index,
                    page: UBC_FIELD_PAGE
                });
            }
        },
        300,
        [selectedPatientId],
        {
            leading: true,
            trailing: false
        }
    ).debouncedCallback;

    const handlePatientAddBtnClick = useDebounceCallback(
        () => {
            if (!isLogin) {
                refreshPatientData();
            }
            onAddPatient?.();
            ubcCommonClkSend({
                value: 'patientAddBtn',
                page: UBC_FIELD_PAGE
            });
        },
        500,
        [onAddPatient, isLogin, refreshPatientData],
        {
            leading: true,
            trailing: false
        }
    ).debouncedCallback;

    const renderPatientAddBtn = useMemo(() => {
        return (
            <CLoginButton
                isLogin={isLogin}
                closeShowNewUserTag={true}
                useH5CodeLogin={true}
                onLoginFail={error => {
                    console.error('error', error);
                }}
                onLoginSuccess={handlePatientAddBtnClick}
            >
                <View className={styles.patientAddBtn}>
                    <HIcon className={styles.patientAddBtnIcon} value='wise-increase' />
                </View>
            </CLoginButton>
        );
    }, [isLogin, handlePatientAddBtnClick]);

    return (
        <View className={styles.patientTabsContainer}>
            <ScrollView
                scrollX
                scrollWithAnimation
                scrollIntoView={generateScrollIntoViewId(selectedPatientId)}
                className={styles.patientTabsScrollContainer}
            >
                <View className={styles.patientTabs}>
                    <View className={styles.patientTabItemBefore}></View>
                    {patients.map((patient, index) => (
                        <View
                            className={classNames(styles.patientTabItemOuter, {
                                [styles.patientTabItemActive]:
                                    patient.patientId === selectedPatientId
                            })}
                            id={generateScrollIntoViewId(patient.patientId)}
                            key={patient.patientId}
                            onClick={() => handleTabClick(patient.patientId, index)}
                        >
                            <View className={styles.patientTabItemInner}>
                                <HImage
                                    src={patient.icon}
                                    className={styles.patientTabItemIcon}
                                    width={96}
                                    height={96}
                                />
                                <Text>{patient.relationship}</Text>
                            </View>
                        </View>
                    ))}
                    {renderPatientAddBtn}
                </View>
            </ScrollView>
        </View>
    );
};

export default memo(PatientTabs);
