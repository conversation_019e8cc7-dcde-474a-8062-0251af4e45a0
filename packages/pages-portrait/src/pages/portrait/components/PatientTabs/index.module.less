.patientTabsContainer {
    padding: 54px 0 30px;
}

.patientTabsScrollContainer {
    height: 138px;
}

.patientTabs {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;

    .patientTabItemOuter {
        padding-bottom: 18px;
    }

    .patientTabItemInner {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-right: 24px;
        padding: 12px;
        width: 270px;
        height: 120px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 48px;
        line-height: 48px;
        letter-spacing: 0%;
        vertical-align: middle;
        border: 1.5px solid #fff;
        border-radius: 60px;
        background: #fff6;
        box-sizing: border-box;
    }

    .patientTabItemActive {
        background: transparent
            url('https://med-fe.cdn.bcebos.com/vita/portrait/bg-patient-tab-active.png') no-repeat
            top left / contain;

        .patientTabItemInner {
            background: transparent;
            border: none;
            color: #fff;
        }
    }

    .patientTabItemIcon {
        width: 96px;
        height: 96px;
        margin-right: 18px;
        border-radius: 50%;
        overflow: hidden;
    }

    .patientTabItemBefore {
        margin-right: 51px;
    }

    .patientTabItemAfter {
        margin-right: 51px;
    }

    .patientAddBtn {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 51px;
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 1.5px dashed #00c8c8;
        background: #fff;
        box-sizing: border-box;
        opacity: 0.8;
    }

    .patientAddBtnIcon {
        color: #00c8c8 !important;
        font-size: 54px !important;
    }
}
