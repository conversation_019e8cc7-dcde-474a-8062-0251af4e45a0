.historyItem {
    display: flex;
    align-items: center;
    padding: 0 48px;
    background: #f8f9fa;
    border-radius: 45px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s;
    height: 171px;
    overflow: hidden;

    &:last-child {
        margin-bottom: 0;
    }

    .checkbox {
        width: 51px;
        height: 51px;
        border: 3px solid #e5e5e5;
        border-radius: 50%;
        margin-right: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        transition: all 0.3s;

        .checkmark {
            color: #00cfa3;
            font-size: 36px;
            font-weight: 700;
        }
    }

    &.selected {
        background: #e5f9f9;
        border: 1px solid #00bcbc;

        .checkbox {
            background: #fff;
            border-color: #fff;

            .checkmark {
                width: 51px;
                height: 51px;
            }
        }
    }

    .patientInfo {
        flex: 1;
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .patientName {
            font-size: 51px;
            color: #000311;
            font-weight: 500;
            margin-right: 36px;
            font-family: PingFang SC;
        }

        .patientDetail {
            font-size: 45px;
            color: #000311;
            font-family: PingFang SC;
            margin-right: 16px;
        }

        .certifiedTag {
            background: #00cfa3;
            color: #fff;
            font-size: 33px;
            font-family: PingFang SC;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 15px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;

            .certifiedText {
                color: #fff;
                font-size: 33px;
            }
        }
    }
}