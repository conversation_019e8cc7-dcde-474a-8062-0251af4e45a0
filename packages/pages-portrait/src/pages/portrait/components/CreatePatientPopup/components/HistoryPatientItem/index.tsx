import React, {memo, useCallback} from 'react';
import cx from 'classnames';
import {View, Image, Text} from '@tarojs/components';
import {CHECKED_ICON} from '../../../../../../constants/images';
import {Patient} from '../../../../../../typings/patient';

import styles from './index.module.less';

// 历史就诊人项组件
export const HistoryPatientItem = memo(
    ({
        patient,
        isSelected,
        onSelect
    }: {
        patient: Patient;
        isSelected: boolean;
        onSelect: (patientId: string) => void;
    }) => {
        const handleClick = useCallback(() => {
            onSelect(patient.patientId);
        }, [patient.patientId, onSelect]);

        return (
            <View
                key={patient.patientId}
                className={cx(styles.historyItem, {
                    [styles.selected]: isSelected
                })}
                onClick={handleClick}
            >
                <View className={styles.checkbox}>
                    {isSelected && (
                        <Image className={styles.checkmark} src={CHECKED_ICON} mode='aspectFit' />
                    )}
                </View>
                <View className={styles.patientInfo}>
                    <Text className={styles.patientName}>{patient.name}</Text>
                    <Text className={styles.patientDetail}>
                        {patient.gender} | {patient.age}
                    </Text>
                    {patient.isCertified && (
                        <View className={styles.certifiedTag}>
                            <Text className={styles.certifiedText}>已实名</Text>
                        </View>
                    )}
                </View>
            </View>
        );
    }
);

HistoryPatientItem.displayName = 'HistoryPatientItem';
