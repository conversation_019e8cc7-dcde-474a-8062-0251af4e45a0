import React, {useState, useEffect, memo, useCallback, useMemo} from 'react';
import {View, Text, ScrollView} from '@tarojs/components';
import {useAtom} from 'jotai';
import {But<PERSON>} from '@baidu/wz-taro-tools-core';
import Taro from '@tarojs/taro';
import cx from 'classnames';
import {HImage, HPicker} from '@baidu/health-ui';
import {closeMultiPopupAtom} from '../../../../store/portrait/zIndexAtoms';
import {closePopupAtom} from '../../../../store/portrait/popupAtom';
import TagSelector from '../../../../components/TagSelector';
import DatePicker from '../../../../components/PickerDate';
import {
    createByHistoryPatient,
    getHistoryPatients,
    CreateByHistoryPatientRequest
} from '../../../../models/services/portrait/historyPatient';
import {Patient} from '../../../../typings/patient';
import {usePatientDataFetch, usePatientData} from '../../../../hooks/portrait/usePatientData';
import {
    createHealthRecord,
    CreateHealthRecordRequest
} from '../../../../models/services/portrait/create';
import {LINK_DIRECT} from '../../../../constants/images';
import {
    ubcCommonViewSend,
    ubcCommonClkSend
} from '../../../../../../pages-im/src/utils/generalFunction/ubc';
import {UBC_FIELD_PAGE} from '../../constants';
import {PATIENT_ID_DEFAULT} from '../../../../constants/portrait';
import InputPopup from '../../../../components/PopupInput';
import {HistoryPatientItem} from './components/HistoryPatientItem';
import styles from './index.module.less';

export interface CreatePatientPopupProps {
    data?: {
        mode?: 'simple' | 'withHistory'; //新建就诊人模式或带历史就诊人模式
        defaultName?: string;
        historyPatients?: Patient[]; // 历史就诊人列表
        onSuccess?: (memberData: any) => void;
        allowMultiple?: boolean;
        parentId?: string;
        onClose?: () => void;
        relationshipOptions?: Array<{
            gender: string;
            value: string;
            disabled?: boolean;
        }>;
    };
}

// 性别选择器数据
const GENDER_OPTIONS = [
    [
        {value: 0, text: '男'},
        {value: 1, text: '女'}
    ]
];

const CreatePatientPopup: React.FC<CreatePatientPopupProps> = ({data}) => {
    const [, closeMultiPopup] = useAtom(closeMultiPopupAtom);
    const [, closePopup] = useAtom(closePopupAtom);
    const {refreshPatientData} = usePatientDataFetch();
    const {selectedPatientId} = usePatientData();

    // 表单数据
    const [selectedRelationship, setSelectedRelationship] = useState<number[]>([]);
    const [realName, setRealName] = useState(data?.defaultName || '');
    const [gender, setGender] = useState('');
    const [birthDate, setBirthDate] = useState('');
    const [selectedHistoryPatient, setSelectedHistoryPatient] = useState<string>('');
    const [historyPatients, setHistoryPatients] = useState<Patient[]>(data?.historyPatients || []);

    // 状态
    const [loading, setLoading] = useState(false);
    const [showGenderPicker, setShowGenderPicker] = useState(false);
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [showNameInput, setShowNameInput] = useState(false);

    const [errors, setErrors] = useState<{[key: string]: boolean}>({});

    const mode = data?.mode || 'simple';

    // 弹窗曝光埋点
    useEffect(() => {
        ubcCommonViewSend({
            value: 'createPatientPopup',
            page: UBC_FIELD_PAGE
        });
    }, []);

    const getRelationshipTags = useMemo(() => {
        // 1. withHistory模式下的逻辑
        if (mode === 'withHistory') {
            // 如果选中了历史就诊人，使用该就诊人的relationships
            if (selectedHistoryPatient) {
                const selectedPatient = historyPatients.find(
                    p => p.patientId === selectedHistoryPatient
                );

                if (selectedPatient?.relationships) {
                    return selectedPatient.relationships.map(rel => ({
                        value: rel.value,
                        // 根据性别和disabled字段设置editable
                        editable:
                            !rel.disabled &&
                            (!selectedPatient.gender ||
                                rel.gender === selectedPatient.gender ||
                                !rel.gender)
                    }));
                }
            }
        }

        // 2. 使用传入的relationshipOptions的数据（simple模式或withHistory模式未选中历史就诊人）
        if (data?.relationshipOptions?.length) {
            return data.relationshipOptions.map(item => ({
                value: item.value,
                editable: !item.disabled
            }));
        }

        // 3. 默认空数组
        return [];
    }, [mode, selectedHistoryPatient, historyPatients, data?.relationshipOptions]);

    // 初始化默认选中逻辑
    useEffect(() => {
        // 仅在 simple 模式下，且 selectedPatientId 等于 PATIENT_ID_DEFAULT 时执行默认选中逻辑
        if (
            mode === 'simple' &&
            selectedPatientId === PATIENT_ID_DEFAULT &&
            selectedRelationship.length === 0 &&
            data?.relationshipOptions?.length
        ) {
            const selfRelationshipIndex = data.relationshipOptions.findIndex(
                option => option.value === '本人'
            );

            if (selfRelationshipIndex !== -1) {
                const selfOption = data.relationshipOptions[selfRelationshipIndex];
                if (!selfOption.disabled) {
                    setSelectedRelationship([selfRelationshipIndex]);
                }
            }
        }
    }, [mode, selectedPatientId, data?.relationshipOptions, selectedRelationship.length]);

    // 获取历史就诊人列表
    const fetchHistoryPatients = useCallback(async () => {
        if (mode === 'withHistory' && !data?.historyPatients?.length) {
            setLoading(true);
            try {
                const result = await getHistoryPatients({});
                if (result.patients) {
                    setHistoryPatients(result.patients);
                }
            } catch (error) {
                console.error('获取历史就诊人列表失败:', error);
            } finally {
                setLoading(false);
            }
        }
    }, [mode, data?.historyPatients?.length]);

    useEffect(() => {
        fetchHistoryPatients();
    }, [fetchHistoryPatients]);

    const handleClose = useCallback(() => {
        if (data?.allowMultiple) {
            closeMultiPopup();
        } else {
            closePopup();
        }
        data?.onClose?.();
    }, [data, closeMultiPopup, closePopup]);

    // 关系选择
    const handleRelationshipChange = useCallback(
        (selectedIndices: number[]) => {
            setSelectedRelationship(selectedIndices);

            // 性别联动逻辑
            if (selectedIndices.length > 0) {
                const selectedIndex = selectedIndices[0];
                const selectedTag = getRelationshipTags[selectedIndex];
                const relationshipValue = selectedTag?.value;

                // 获取性别联动的数据源
                let genderDataSource;
                // Simple模式：使用relationshipOptions
                if (mode === 'simple') {
                    genderDataSource = data?.relationshipOptions;
                }
                // WithHistory模式且未选中历史就诊人：使用relationshipOptions
                else if (mode === 'withHistory' && !selectedHistoryPatient) {
                    genderDataSource = data?.relationshipOptions;
                }

                // 性别联动
                if (genderDataSource && relationshipValue) {
                    const relationshipOption = genderDataSource.find(
                        option => option.value === relationshipValue
                    );

                    if (relationshipOption && relationshipOption.gender) {
                        setGender(relationshipOption.gender);
                        // 清除性别错误状态
                        setErrors(prev => ({...prev, gender: false}));
                    }
                }
            }

            setErrors(prev => ({...prev, relationship: false}));
        },
        [getRelationshipTags, mode, data?.relationshipOptions, selectedHistoryPatient]
    );

    // 性别选择
    const handleGenderConfirm = useCallback((_, selectedTexts: string[]) => {
        const selectedGender = selectedTexts && selectedTexts.length > 0 ? selectedTexts[0] : '';
        setGender(selectedGender);
        setShowGenderPicker(false);
        setErrors(prev => ({...prev, gender: false}));
    }, []);

    // 出生日期选择
    const handleDateConfirm = useCallback((date: string) => {
        setBirthDate(date);
        setShowDatePicker(false);
        setErrors(prev => ({...prev, birthDate: false}));
    }, []);

    // 姓名输入处理
    const handleNameConfirm = useCallback((name: string) => {
        setRealName(name);
        setShowNameInput(false);
        setErrors(prev => ({...prev, realName: false}));
    }, []);

    // 打开姓名输入弹窗
    const handleOpenNameInput = useCallback(() => {
        setShowNameInput(true);
    }, []);

    // 关闭姓名输入弹窗
    const handleCloseNameInput = useCallback(() => {
        setShowNameInput(false);
    }, []);

    // 历史就诊人选择 - 单选（支持取消选中）
    const handleHistoryPatientSelect = useCallback(
        (patientId: string) => {
            // 如果点击的是已选中的就诊人，则取消选中
            if (selectedHistoryPatient === patientId) {
                // 取消勾选埋点
                ubcCommonClkSend({
                    value: 'createPatientPopup_shpBtn0',
                    page: UBC_FIELD_PAGE
                });

                setSelectedHistoryPatient('');
                // 取消勾选历史就诊人时，重置成员关系的选中（清空用户手动选中的）
                setSelectedRelationship([]);
                setRealName('');
                setGender('');
                setBirthDate('');
            } else {
                // 勾选埋点
                ubcCommonClkSend({
                    value: 'createPatientPopup_shpBtn1',
                    page: UBC_FIELD_PAGE
                });

                const selectedPatient = historyPatients.find(p => p.patientId === patientId);
                if (selectedPatient) {
                    // 先重置成员关系选择（清空用户之前手动选中的）
                    setSelectedRelationship([]);

                    // 然后设置新的历史就诊人数据
                    setSelectedHistoryPatient(patientId);
                    setRealName(selectedPatient.name);
                    setGender(selectedPatient.gender);
                    setBirthDate(selectedPatient.birthDate);

                    // 如果新选中的历史就诊人有关系数据，自动选中
                    if (
                        selectedPatient.relationship &&
                        selectedPatient.relationships &&
                        selectedPatient.relationships.length > 0
                    ) {
                        // 在relationships中查找匹配的关系
                        const relationshipIndex = selectedPatient.relationships.findIndex(
                            rel => rel.value === selectedPatient.relationship
                        );
                        if (relationshipIndex !== -1) {
                            //判断该关系是否可编辑（未被禁用且性别匹配）
                            const relationshipOption =
                                selectedPatient.relationships[relationshipIndex];
                            const isEditable =
                                !relationshipOption.disabled &&
                                (!selectedPatient.gender ||
                                    relationshipOption.gender === selectedPatient.gender ||
                                    !relationshipOption.gender);

                            if (isEditable) {
                                setSelectedRelationship([relationshipIndex]);
                            }
                        }
                    }
                    // 如果新选中的历史就诊人没有关系数据或关系不可编辑，selectedRelationship保持为[]重置状态
                }
            }
        },
        [selectedHistoryPatient, historyPatients]
    );

    // 表单验证
    const validateForm = useCallback(() => {
        const newErrors: {[key: string]: boolean} = {};

        if (selectedRelationship.length === 0) {
            newErrors.relationship = true;
        }
        if (!realName.trim()) {
            newErrors.realName = true;
        }
        if (!gender) {
            newErrors.gender = true;
        }
        if (!birthDate) {
            newErrors.birthDate = true;
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    }, [selectedRelationship, realName, gender, birthDate]);

    // 提交表单
    const handleSubmit = useCallback(async () => {
        // 创建按钮点击埋点
        ubcCommonClkSend({
            value: 'createPatientPopup_confirmBtn',
            page: UBC_FIELD_PAGE
        });

        if (!validateForm()) {
            Taro.showToast({
                title: '请先完善基础资料',
                icon: 'none'
            });
            return;
        }

        setLoading(true);
        try {
            // 根据当前的标签数据源获取关系值
            const relationship = getRelationshipTags[selectedRelationship[0]]?.value || '';

            let result;
            // 根据模式和是否选中历史就诊人决定调用哪个接口
            if (mode === 'withHistory' && selectedHistoryPatient) {
                // withHistory模式且选中了历史就诊人，使用createByHistoryPatient接口
                const createByHistoryParams: CreateByHistoryPatientRequest = {
                    patients: [
                        {
                            patientId: selectedHistoryPatient,
                            relationship: relationship,
                            name: realName,
                            gender: gender,
                            birthDate: birthDate
                        }
                    ]
                };

                const historyResult = await createByHistoryPatient(createByHistoryParams);

                // 检查创建结果
                if (historyResult.patients && historyResult.patients.length > 0) {
                    const createResult = historyResult.patients[0];
                    if (createResult.isSuccess === 1) {
                        result = {
                            patientId: createResult.patientId
                        };
                    } else {
                        throw new Error('创建健康档案失败');
                    }
                } else {
                    throw new Error('创建健康档案返回数据异常');
                }
            } else {
                // simple模式或withHistory模式但未选中历史就诊人，使用createHealthRecord接口
                const memberData: CreateHealthRecordRequest = {
                    name: realName,
                    gender: gender,
                    birthDate: birthDate,
                    relationship: relationship,
                    historyPatientId: undefined // 确保不传递historyPatientId
                };

                result = await createHealthRecord(memberData);
            }

            Taro.showToast({
                title: '创建成功',
                icon: 'success'
            });

            refreshPatientData(result.patientId);

            data?.onSuccess?.(result);
            handleClose();
        } catch (error: any) {
            console.error('创建成员档案失败:', error);
            Taro.showToast({
                title: error?.[0]?.toast || '创建失败，请重试',
                icon: 'none'
            });
        } finally {
            setLoading(false);
        }
    }, [
        validateForm,
        getRelationshipTags,
        selectedRelationship,
        mode,
        selectedHistoryPatient,
        realName,
        gender,
        birthDate,
        refreshPatientData,
        data,
        handleClose
    ]);

    // 获取性别选择器的值
    const getGenderPickerValue = useCallback(() => {
        if (!gender) return [];
        const genderIndex = GENDER_OPTIONS[0].findIndex(item => item.text === gender);
        return genderIndex !== -1 ? [genderIndex] : [];
    }, [gender]);

    // 判断字段是否可编辑
    const fieldEditability = useMemo(() => {
        if (mode === 'simple') {
            return {
                relationship: true,
                realName: true,
                gender: true,
                birthDate: true
            };
        }
        if (mode === 'withHistory') {
            // 如果选中了历史就诊人，只有成员关系可编辑
            if (selectedHistoryPatient) {
                return {
                    relationship: true,
                    realName: false,
                    gender: false,
                    birthDate: false
                };
            }
            // 未选中历史就诊人时，所有字段都可编辑
            return {
                relationship: true,
                realName: true,
                gender: true,
                birthDate: true
            };
        }
        return {
            relationship: true,
            realName: true,
            gender: true,
            birthDate: true
        };
    }, [mode, selectedHistoryPatient]);

    // 渲染历史就诊人列表
    const historyPatientsList = useMemo(() => {
        if (mode !== 'withHistory') return null;

        return (
            <View className={styles.section}>
                <Text className={styles.sectionTitle}>
                    您有以下{historyPatients.length}位历史就诊人，可直接关联
                </Text>
                <View>
                    <Text className={styles.subText}>*包含身高体重、疾病史、地址等信息</Text>
                </View>

                <View
                    className={cx(styles.historyList, {
                        [styles.error]: errors.historyPatients
                    })}
                >
                    {historyPatients.map(patient => (
                        <HistoryPatientItem
                            key={patient.patientId}
                            patient={patient}
                            isSelected={selectedHistoryPatient === patient.patientId}
                            onSelect={handleHistoryPatientSelect}
                        />
                    ))}
                </View>
            </View>
        );
    }, [
        mode,
        historyPatients,
        selectedHistoryPatient,
        errors.historyPatients,
        handleHistoryPatientSelect
    ]);

    return (
        <View className={styles.container}>
            <ScrollView className={styles.content} scrollY>
                {/* 历史就诊人选择 - 仅在 withHistory 模式下显示 */}
                {historyPatientsList}

                {/* 成员关系 */}
                <View className={styles.section}>
                    <Text className={cx(styles.label, styles.required, styles.relationshipLabel)}>
                        成员关系
                    </Text>
                    <View className={cx({[styles.error]: errors.relationship})}>
                        <TagSelector
                            key={`${selectedHistoryPatient}-${getRelationshipTags.length}`} // 使用key强制重新渲染
                            columns={4}
                            reportTags={getRelationshipTags} // 使用动态获取的标签
                            mode='single'
                            defaultSelected={selectedRelationship}
                            onChange={handleRelationshipChange}
                        />
                    </View>
                </View>

                {/* 真实姓名 - 使用 InputPopup */}
                <View className={styles.section}>
                    <View
                        className={cx(styles.fieldRow, {
                            [styles.disabled]: !fieldEditability.realName
                        })}
                        onClick={fieldEditability.realName ? handleOpenNameInput : undefined}
                    >
                        <Text className={cx(styles.fieldLabel, styles.required)}>真实姓名</Text>
                        <View className={styles.fieldValueContainer}>
                            <Text
                                className={cx(styles.fieldValue, {
                                    [styles.placeholder]: !realName,
                                    [styles.realNameInputError]: errors.realName
                                })}
                            >
                                {realName || '请输入'}
                            </Text>
                            {fieldEditability.realName && (
                                <HImage src={LINK_DIRECT} width={45} height={45}></HImage>
                            )}
                        </View>
                    </View>
                </View>

                {/* 性别 */}
                <View className={styles.section}>
                    <View
                        className={cx(styles.fieldRow, {
                            [styles.disabled]: !fieldEditability.gender
                        })}
                        onClick={() => {
                            if (fieldEditability.gender) {
                                setShowGenderPicker(true);
                            }
                        }}
                    >
                        <Text className={cx(styles.fieldLabel, styles.required)}>性别</Text>
                        <View className={styles.fieldValueContainer}>
                            <Text
                                className={cx(styles.fieldValue, {[styles.placeholder]: !gender})}
                            >
                                {gender || '请选择'}
                            </Text>
                            {fieldEditability.gender && (
                                <HImage src={LINK_DIRECT} width={45} height={45}></HImage>
                            )}
                        </View>
                    </View>
                </View>

                {/* 出生日期 */}
                <View className={styles.section}>
                    <View
                        className={cx(styles.fieldRow, {
                            [styles.disabled]: !fieldEditability.birthDate
                        })}
                        onClick={() => {
                            if (fieldEditability.birthDate) {
                                setShowDatePicker(true);
                            }
                        }}
                    >
                        <Text className={cx(styles.fieldLabel, styles.required)}>出生日期</Text>
                        <View className={styles.fieldValueContainer}>
                            <Text
                                className={cx(styles.fieldValue, {
                                    [styles.placeholder]: !birthDate
                                })}
                            >
                                {birthDate || '请选择'}
                            </Text>
                            {fieldEditability.birthDate && (
                                <HImage src={LINK_DIRECT} width={45} height={45}></HImage>
                            )}
                        </View>
                    </View>
                </View>
            </ScrollView>

            {/* 底部按钮 */}
            <View className={styles.footer}>
                <Button
                    className={styles.submitBtn}
                    onClick={handleSubmit}
                    disabled={loading}
                    loading={loading}
                >
                    {loading ? '创建中...' : '创建'}
                </Button>
            </View>

            {/* 姓名输入弹窗 */}
            <InputPopup
                showPopup={showNameInput}
                onClosePopup={handleCloseNameInput}
                title='真实姓名'
                onConfirm={handleNameConfirm}
                initialValue={realName}
                isSingleLine={true}
            />

            {/* 性别选择器 */}
            {fieldEditability.gender && (
                <HPicker
                    show={showGenderPicker}
                    options={GENDER_OPTIONS}
                    value={getGenderPickerValue()}
                    onClose={() => setShowGenderPicker(false)}
                    onConfirm={handleGenderConfirm}
                    title='性别'
                />
            )}

            {/* 日期选择器 */}
            {fieldEditability.birthDate && (
                <DatePicker
                    show={showDatePicker}
                    value={birthDate}
                    onClose={() => setShowDatePicker(false)}
                    onConfirm={handleDateConfirm}
                />
            )}
        </View>
    );
};

export default memo(CreatePatientPopup);
