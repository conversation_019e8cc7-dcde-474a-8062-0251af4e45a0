.container {
    background: #fff;
    border-radius: 24px 24px 0 0;
    max-height: 68vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0 51px 27px;
}

.content {
    flex: 1;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none;
        width: 0;
        background: transparent;
    }

    scrollbar-width: none;
}

.section {
    margin-bottom: 60px;

    &:first-child {
        margin-top: 40px;
    }
}

.sectionTitle {
    color: #272933;
    margin-bottom: 30px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 45px;
    line-height: 1.5;
}

.subText {
    font-size: 39px;
    color: #f60;
    margin-bottom: 45px;
    font-family: PingFang SC;
    font-weight: 400;
}

.label {
    font-size: 48px;
    color: #333;
    margin-bottom: 30px;
    display: block;
    font-family: PingFang SC;
    font-weight: 400;

    &.required::after {
        content: ' *';
        color: #f60;
    }
}

.arrow {
    font-size: 48px;
    color: #999;
}

.fieldValue {
    font-size: 48px;
    color: #333;
    font-family: PingFang SC;
    margin-right: 16px;

    &.placeholder {
        color: #999;
        font-weight: 400;
    }
}

.fieldRow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;

    &:active:not(.disabled) {
        opacity: 0.7;
    }

    &.disabled {
        cursor: not-allowed;
        opacity: 0.6;

        .fieldValue {
            color: #999;
        }
    }
}

.fieldLabel {
    font-size: 48px;
    color: #333;
    font-family: PingFang SC;
    font-weight: 400;

    &.required::after {
        content: ' *';
        color: #f60;
    }
}

.fieldValueContainer {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
}

.inlineInput {
    flex: 1;
    max-width: 300px;
    height: 80px;
    padding: 0 24px;
    border: 2px solid #e5e5e5;
    border-radius: 40px;
    font-size: 48px;
    color: #333;
    background: #f8f9fa;
    box-sizing: border-box;
    text-align: right;

    &::placeholder {
        color: #999;
    }

    &:focus {
        border-color: #00cfa3;
        background: #fff;
    }

    &.error {
        border-color: #f60;
        background: #fff5f5;
    }
}

.hiddenInput {
    position: fixed;
    top: -1000px;
    left: -1000px;
    opacity: 0;
    pointer-events: none;
}

.input {
    width: 100%;
    height: 120px;
    padding: 0 32px;
    border: 2px solid #e5e5e5;
    border-radius: 60px;
    font-size: 48px;
    color: #333;
    background: #f8f9fa;
    box-sizing: border-box;

    &::placeholder {
        color: #999;
    }

    &:focus {
        border-color: #00cfa3;
        background: #fff;
    }

    &.error {
        border-color: #f60;
        background: #fff5f5;
    }
}

.selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 120px;
    padding: 0 32px;
    border: 2px solid #e5e5e5;
    border-radius: 60px;
    background: #f8f9fa;
    cursor: pointer;
    box-sizing: border-box;

    &:active {
        opacity: 0.7;
    }

    &.error {
        border-color: #f60;
        background: #fff5f5;
    }

    .selectorText {
        font-size: 45px;
        color: #333;
        flex: 1;

        &.placeholder {
            color: #999;
        }
    }

    .arrow {
        font-size: 48px;
        color: #ccc;
        margin-left: 16px;
    }
}

.historyList {
    margin-top: 30px;

    &.error {
        border: 2px solid #f60;
        border-radius: 20px;
        padding: 10px;
    }
}

.footer {
    padding-top: 32px;

    .submitBtn {
        width: 100%;
        height: 132px;
        background: linear-gradient(134deg, #00cfa3 0%, #00d3ea 100%);
        color: #fff;
        border: none;
        border-radius: 66px;
        font-size: 54px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;

        &:disabled {
            background: #ccc;
            opacity: 0.6;
        }

        &:active:not(:disabled) {
            opacity: 0.8;
        }
    }
}

.realNameInput {
    border: none;
    background: none;
    border-radius: 0;
    padding: 0;
    height: auto;
    font-size: 48px;
    color: #333;
    width: 100%;
    text-align: right;

    &.realNameInputError {
        border: 2px solid #f60;
        background: #fff5f5;
        padding: 0 24px;
        border-radius: 40px;
        height: 80px;
    }
}

.relationshipLabel {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 51px !important;
}
