import React, {memo, useEffect, useState} from 'react';
import {useAtomValue, useSetAtom} from 'jotai';
import {HButton, HIcon, HImage} from '@baidu/health-ui';
import {View, Text} from '@tarojs/components';
import useDebounceCallback from '../../../../hooks/useDebounceCallback';
import {usePatientData} from '../../../../hooks/portrait/usePatientData';
import {
    historyPatientsBarCloseCountAtom,
    historyPatientsBarCloseCountSetAtom
} from '../../../../store/portrait';
import {historyPatientsBarVisibleAtom} from '../../../../store/portrait/derivedAtoms';
import {
    ubcCommonClkSend,
    ubcCommonViewSend
} from '../../../../../../pages-im/src/utils/generalFunction/ubc';
import {ICON_LINK_ORANGE} from '../../../../constants/images';
import {UBC_FIELD_PAGE} from '../../constants';

import styles from './index.module.less';

const HistoryPatientBar: React.FC<{onBtnClick: () => void}> = ({onBtnClick}) => {
    const {historyPatients} = usePatientData();
    const historyPatientsBarVisible = useAtomValue(historyPatientsBarVisibleAtom);
    const historyPatientsBarCloseCount = useAtomValue(historyPatientsBarCloseCountAtom);
    const setHistoryPatientsBarCloseCount = useSetAtom(historyPatientsBarCloseCountSetAtom);
    const [visible, setVisible] = useState(true);

    const handleBtnClick = useDebounceCallback(
        () => {
            onBtnClick();
            // ubc打点逻辑
            ubcCommonClkSend({
                value: 'historyPatientBar_btn',
                page: UBC_FIELD_PAGE
            });
        },
        300,
        [],
        {
            leading: true,
            trailing: false
        }
    ).debouncedCallback;

    const handleClose = () => {
        setHistoryPatientsBarCloseCount((Number(historyPatientsBarCloseCount) + 1).toString());
        setVisible(false);
        // ubc打点逻辑
        ubcCommonClkSend({
            value: 'historyPatientBar_closeBtn',
            page: UBC_FIELD_PAGE
        });
    };

    useEffect(() => {
        // ubc打点逻辑
        if (historyPatientsBarVisible && visible) {
            ubcCommonViewSend({
                value: 'historyPatientBar',
                page: UBC_FIELD_PAGE
            });
        }
    }, [historyPatientsBarVisible, visible]);

    if (!historyPatientsBarVisible || !visible) {
        return null;
    }

    return (
        <View className={styles.historyPatientBar}>
            <HImage
                src={ICON_LINK_ORANGE}
                width={54}
                height={54}
                className={styles.historyPatientBarIcon}
            />
            <View className={styles.historyPatientBarText}>
                <Text>您有{historyPatients.length}位历史就诊人待关联</Text>
            </View>
            <HButton
                text='去查看'
                onClick={handleBtnClick}
                className={styles.historyPatientBarBtn}
            />
            <HIcon
                className={styles.closeIcon}
                value='wise-shut'
                size={51}
                color='#848691'
                onClick={handleClose}
            />
        </View>
    );
};

export default memo(HistoryPatientBar);
