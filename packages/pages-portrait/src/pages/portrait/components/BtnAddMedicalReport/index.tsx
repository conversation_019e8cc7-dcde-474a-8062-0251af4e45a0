import {memo, useEffect} from 'react';
import {useAtomValue} from 'jotai';
import classNames from 'classnames';
import {HButton} from '@baidu/health-ui';
import useDebounceCallback from '../../../../hooks/useDebounceCallback';
import {usePatientDataFetch} from '../../../../hooks/portrait/usePatientData';
import {CLoginButton} from '../../../../../../ui-cards-common';
import {isLoginAtom} from '../../../../store/portrait/userAtoms';
import {
    ubcCommonClkSend,
    ubcCommonViewSend
} from '../../../../../../pages-im/src/utils/generalFunction/ubc';
import {UBC_FIELD_PAGE} from '../../constants';

import styles from './index.module.less';

const BtnAddMedicalReport = ({
    onReportAddBtnClick,
    text = '上传资料',
    size = 'sm',
    className
}: {
    onReportAddBtnClick: () => void;
    text?: string;
    size?: 'sm' | 'md';
    className?: string;
}) => {
    const isLogin = useAtomValue(isLoginAtom);
    const {refreshPatientData} = usePatientDataFetch();
    const handleReportAddBtnClick = useDebounceCallback(
        () => {
            if (!isLogin) {
                refreshPatientData();
            }
            onReportAddBtnClick();
            // ubc打点逻辑
            ubcCommonClkSend({
                value: size === 'sm' ? 'medicalReportAddBtn1' : 'medicalReportAddBtn0',
                page: UBC_FIELD_PAGE
            });
        },
        300,
        [onReportAddBtnClick, isLogin, refreshPatientData, size],
        {
            leading: true,
            trailing: false
        }
    ).debouncedCallback;

    useEffect(() => {
        // ubc打点逻辑
        if (size === 'md') {
            ubcCommonViewSend({
                value: 'medicalReportAddBtn0',
                page: UBC_FIELD_PAGE
            });
        }
    }, [size]);

    return (
        <CLoginButton
            isLogin={isLogin}
            closeShowNewUserTag={true}
            useH5CodeLogin={true}
            onLoginFail={error => {
                console.error('error', error);
            }}
            onLoginSuccess={handleReportAddBtnClick}
        >
            <HButton
                className={classNames(
                    styles.medicalReportAddBtn,
                    size === 'sm' ? styles.sm : styles.md,
                    className
                )}
                text={text}
            />
        </CLoginButton>
    );
};

export default memo(BtnAddMedicalReport);
