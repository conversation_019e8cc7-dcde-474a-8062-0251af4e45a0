import React, {memo, useState, useEffect} from 'react';
import {View} from '@tarojs/components';
import {useAtom} from 'jotai';
import {Button} from '@baidu/wz-taro-tools-core';
import TagSelector from '../../../../components/TagSelector';
import {closeMultiPopupAtom} from '../../../../store/portrait/zIndexAtoms';
import styles from './index.module.less';

interface RelationshipSelectPopupProps {
    data?: {
        relationships?: Array<{
            value: string;
            disabled?: boolean;
        }>;
        currentRelationship?: string; // 当前关系，用于回显
        onSelect?: (relationship: string) => void;
        onSuccess?: (data: any) => void;
    };
}

const RelationshipSelectPopup: React.FC<RelationshipSelectPopupProps> = ({data}) => {
    const [selectedRelationship, setSelectedRelationship] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedIndexes, setSelectedIndexes] = useState<number[]>([]);
    const [, closeMultiPopup] = useAtom(closeMultiPopupAtom);

    // 转换关系数据为 TagSelector 需要的格式
    const tagSelectorData =
        data?.relationships?.map(item => ({
            code: item.value,
            value: item.value,
            editable: !item.disabled
        })) || [];

    useEffect(() => {
        // 回显逻辑实现
        if (data?.currentRelationship && data?.relationships) {
            // 1. 根据 currentRelationship 查找对应的索引
            const currentIndex = data.relationships.findIndex(
                rel => rel.value === data.currentRelationship
            );

            if (currentIndex >= 0) {
                // 2. 设置选中的索引数组（TagSelector需要）
                setSelectedIndexes([currentIndex]);
                // 3. 设置选中的关系值
                setSelectedRelationship(data.currentRelationship);
            }
        } else {
            // 如果没有当前关系，默认选择第一个可用的关系
            const firstAvailableIndex = data?.relationships?.findIndex(rel => !rel.disabled) || 0;
            if (firstAvailableIndex >= 0 && data?.relationships?.[firstAvailableIndex]) {
                setSelectedIndexes([firstAvailableIndex]);
                setSelectedRelationship(data.relationships[firstAvailableIndex].value);
            }
        }
    }, [data]);

    // 处理 TagSelector 选择变化
    const handleTagSelectorChange = (selectedIndexes: number[], selectedTags: any[]) => {
        if (selectedTags.length > 0) {
            setSelectedRelationship(selectedTags[0].value);
            setSelectedIndexes(selectedIndexes);
        }
    };

    const handleSubmit = async () => {
        if (!selectedRelationship || isSubmitting) return;

        try {
            setIsSubmitting(true);
            data?.onSelect?.(selectedRelationship);
            data?.onSuccess?.({relationship: selectedRelationship});
            closeMultiPopup();
        } catch (error) {
            console.error('选择关系失败:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <View className={styles.container}>
            <TagSelector
                columns={4}
                reportTags={tagSelectorData}
                mode='single'
                defaultSelected={selectedIndexes}
                onChange={handleTagSelectorChange}
            />

            <View className={styles.buttonGroup}>
                <Button
                    style={{
                        width: '100%',
                        background: selectedRelationship
                            ? 'linear-gradient(134deg, #00CFA3 0%, #00D3EA 100%)'
                            : '#cccccc',
                        color: '#fff'
                    }}
                    size='large'
                    shape='round'
                    disabled={!selectedRelationship}
                    loading={isSubmitting}
                    onClick={handleSubmit}
                >
                    确认
                </Button>
            </View>
        </View>
    );
};

export default memo(RelationshipSelectPopup);
