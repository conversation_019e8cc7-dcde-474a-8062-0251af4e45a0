.healthHistoryContainer {
    padding: 0 48px 0 36px;

    .loadingContainer {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-top: 324px;
    }
}

.healthCard {
    padding: 45px;
    background: #fff;
    border-radius: 63px;
    margin-bottom: 30px;
    overflow: hidden;
    box-shadow: 0 0 60px 0 #00000010;

    &:last-child {
        margin-bottom: 0;
    }

    .cardHeader {
        display: flex;
        align-items: center;
        background: #fff;
        margin-bottom: 60px;

        .categoryIcon {
            width: 63px;
            height: 63px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;

            .iconText {
                font-size: 36px;
            }

            .categoryIconImage {
                border-radius: 0;
            }
        }

        .categoryTitle {
            font-family: PingFang SC;
            font-size: 51px;
            font-weight: 500;
        }
    }

    .cardContent {
        padding: 0 0 16px;

        .healthItem {
            border-bottom: 3px solid #f8f8f8;
            position: relative;

            &:last-child {
                border-bottom: none;
            }

            &.leftRightLayout {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 48px;
                height: 165px;

                .circleDot {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background-color: #ccc;
                    margin-right: 24px;
                    flex-shrink: 0;
                }

                .itemName {
                    font-family: PingFang SC;
                    font-size: 48px;
                    flex: 1;
                    font-weight: 400;
                    display: flex;
                    align-items: center;
                }

                .itemControl {
                    font-family: PingFang SC;
                    font-size: 48px;
                    flex-shrink: 0;
                    margin-left: 16px;
                    min-width: 400px;
                }
            }

            &.topBottomLayout {
                .textareaBox {
                    background: #f5f6fa !important;
                    padding: 40px 45px 60px; /* 增加底部padding为字符计数留出空间 */
                    position: relative;

                    .itemHeader {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 12px;

                        .itemName {
                            font-size: 48px;
                            color: #333;
                            font-weight: 500;
                        }
                    }

                    .itemControl {
                        width: 100%;
                    }
                }

                .charCountContainer {
                    position: absolute;
                    right: 45px;
                    bottom: 24px;
                    display: flex;
                    align-items: center;
                    z-index: 10;

                    .charCountNum {
                        color: #b7b9c1;
                        font-size: 42px;
                    }

                    .charCountTotal {
                        color: #b7b9c1;
                        font-size: 42px;
                    }
                }
            }

            .itemControl {
                .healthInput {
                    width: 100%;
                    border-radius: 6px;
                    background: #f5f6fa;
                    box-sizing: border-box;
                    resize: none;
                    height: 246px;
                    font-weight: 400;
                    font-size: 48px;
                    line-height: 75px;
                    padding-bottom: 60px;

                    &:focus {
                        border-color: #4ecdc4;
                        background: #f5f6fa;
                        outline: none;
                    }

                    &::placeholder {
                        color: #b7b9c1;
                    }
                }

                .textareaPlaceholder {
                    font-size: 48px;
                    line-height: 75px;
                    color: #b7b9c1;
                }

                .tagContainer {
                    display: flex;
                    gap: 24px; /* 减小间距，让标签更紧凑 */
                    justify-content: center; /* 水平居中对齐 */
                    align-items: center; /* 垂直居中对齐 */

                    .tag {
                        border-radius: 42px;
                        color: #848691;
                        background: #f5f6fa;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        user-select: none;
                        font-weight: 400;
                        font-size: 42px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 84px;
                        width: 174px;

                        &.tagActive {
                            background: linear-gradient(115.5deg, #00cfa3 0%, #00d3ea 102.87%);
                            color: #fff;
                        }

                        &:hover {
                            border-color: #4ecdc4;
                        }
                    }
                }

                .pickerSelector {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    border: 1px solid #e0e0e0;
                    border-radius: 6px;
                    background: #fafafa;
                    cursor: pointer;
                    min-width: 100px;

                    &:hover {
                        border-color: #4ecdc4;
                        background: #fff;
                    }
                }
            }
        }
    }
}

.personalTheme {
    padding-bottom: 0;

    .cardHeader {
        margin-bottom: 24px;
    }
}

.pickerWrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 6px 0;

    .arrowIcon {
        font-size: 48px;
        color: #ccc;
        margin-left: 6px;
        flex-shrink: 0;
    }
}

.healthInput :global(.taro-textarea) {
    background: #f5f6fa !important;
    background-color: #f5f6fa !important;
}
