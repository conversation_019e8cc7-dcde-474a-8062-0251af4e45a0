import React, {useState, useCallback, memo, useRef, useEffect, useMemo} from 'react';
import {View, Text, Textarea} from '@tarojs/components';
import cx from 'classnames';
import {throttle} from 'lodash';
import {HImage} from '@baidu/health-ui';

import {useHealthHistory} from '../../../../hooks/portrait/useHealthHistory';
import {ubcCommonClkSend} from '../../../../../../pages-im/src/utils/generalFunction/ubc';
import {GIF_LOADING_GREEN, LINK_DIRECT} from '../../../../constants/images';

import HealthPicker from './components/HealthPicker';
import {getCategoryIcon, getCategoryThemeClass, buildUpdateParams} from './utils';
import {UPDATE_DEBOUNCE_DELAY, MAX_INPUT_LENGTH} from './constants';
import type {HealthHistoryCardsProps, HealthItem} from './types';
import styles from './index.module.less';

const HealthHistoryCards: React.FC<HealthHistoryCardsProps> = ({
    apiData,
    patientId = '',
    onItemChange,
    onUpdateSuccess,
    onUpdateError
}) => {
    const [pickerStates, setPickerStates] = useState<Record<string, boolean>>({});

    // 用于记录上次提交的值，避免重复请求
    const lastSubmittedValues = useRef<Record<string, string>>({});

    // 用于记录输入框聚焦时的值，用于失焦时比对
    const focusValues = useRef<Record<string, string>>({});

    // 存储每个输入框的本地状态（值和字数）
    const [localInputStates, setLocalInputStates] = useState<
        Record<string, {value: string; charCount: number}>
    >({});

    const {healthData, formData, loading, updating, updateHealthHistory, updateFormData} =
        useHealthHistory({
            patientId,
            autoFetch: !apiData, // 有外部数据就不自动获取
            externalData: apiData, // 传入外部数据
            onUpdateSuccess,
            onUpdateError
        });

    // 发送埋点
    const sendTrackingEvent = useCallback((itemKey: string, actionType: 'click' | 'edit') => {
        const eventName = `healthHistory_${itemKey}_${actionType}`;

        try {
            ubcCommonClkSend({
                value: eventName
            });
        } catch (error) {
            console.error('埋点发送失败:', error);
        }
    }, []);

    useEffect(() => {
        if (healthData.length > 0) {
            const initialStates: Record<string, {value: string; charCount: number}> = {};
            healthData.forEach(category => {
                category.items.forEach(item => {
                    if (item.optionType === 1) {
                        // 只处理输入框类型
                        const fieldKey = `${category.healthCategoryId}-${item.key}`;
                        const value = formData[category.healthCategoryId]?.[item.key] || '';
                        initialStates[fieldKey] = {
                            value,
                            charCount: value.length
                        };
                    }
                });
            });
            setLocalInputStates(initialStates);
        }
    }, [healthData, formData]);

    // 处理输入框实时变化 - 只更新本地状态
    const handleLocalInputChange = (categoryId: string, itemKey: string, e: any) => {
        const fieldKey = `${categoryId}-${itemKey}`;
        let value: string;

        // 根据不同环境获取值
        if (process.env.TARO_ENV === 'h5') {
            value = e.target?.value || '';
        } else {
            value = e.detail?.value || '';
        }

        // 只更新本地状态
        setLocalInputStates(prev => ({
            ...prev,
            [fieldKey]: {
                value,
                charCount: value.length
            }
        }));
    };

    // 创建节流的更新函数 - 用于输入框失去焦点时
    const createThrottledUpdate = useCallback(
        async (itemKey: string, value: string, fieldKey: string) => {
            if (!patientId) {
                console.warn('缺少 patientId，无法更新数据');
                return;
            }

            // 检查值是否与上次提交的值相同
            if (lastSubmittedValues.current[fieldKey] === value) {
                return;
            }

            const updateParams = buildUpdateParams(patientId, itemKey, value);
            if (!updateParams) return;

            try {
                await updateHealthHistory(updateParams);
                // 更新成功后记录最新的提交值
                lastSubmittedValues.current[fieldKey] = value;
            } catch (error) {
                console.error('更新失败:', error);
            }
        },
        [patientId, updateHealthHistory]
    );

    // 使用 useMemo 缓存高阶函数返回值
    const throttledUpdate = useMemo(
        () => throttle(createThrottledUpdate, UPDATE_DEBOUNCE_DELAY),
        [createThrottledUpdate]
    );

    // 立即更新函数 - 用于tag和picker选择
    const immediateUpdate = useCallback(
        async (itemKey: string, value: string, fieldKey: string) => {
            if (!patientId) {
                console.warn('缺少 patientId，无法更新数据');
                return;
            }

            // 检查值是否与上次提交的值相同
            if (lastSubmittedValues.current[fieldKey] === value) {
                return;
            }

            const updateParams = buildUpdateParams(patientId, itemKey, value);
            if (!updateParams) return;

            try {
                await updateHealthHistory(updateParams);
                // 更新成功后记录最新的提交值
                lastSubmittedValues.current[fieldKey] = value;
            } catch (error) {
                console.error('立即更新失败:', error);
            }
        },
        [patientId, updateHealthHistory]
    );

    // 处理非输入框值变化 - 立即更新状态
    const handleValueChange = (categoryId: string, itemKey: string, value: string) => {
        updateFormData(categoryId, itemKey, value);
        onItemChange?.(categoryId, itemKey, value);
    };

    const handleInputFocus = (categoryId: string, itemKey: string) => {
        const fieldKey = `${categoryId}-${itemKey}`;
        const currentValue = formData[categoryId]?.[itemKey] || '';
        focusValues.current[fieldKey] = currentValue;
    };

    // 处理tag选择 - 立即发送请求
    const handleTagSelect = (categoryId: string, itemKey: string, value: string) => {
        const fieldKey = `${categoryId}-${itemKey}`;

        // 发送埋点事件
        sendTrackingEvent(itemKey, 'click');

        // 先更新本地状态
        handleValueChange(categoryId, itemKey, value);

        // 立即发送请求（会检查是否与上次值相同）
        if (!updating) {
            immediateUpdate(itemKey, value, fieldKey);
        }
    };

    // 处理picker选择 - 立即发送请求
    const handlePickerSelect = (categoryId: string, itemKey: string, value: string) => {
        const fieldKey = `${categoryId}-${itemKey}`;

        // 发送埋点事件
        sendTrackingEvent(itemKey, 'click');

        // 先更新本地状态
        handleValueChange(categoryId, itemKey, value);

        // 立即发送请求（会检查是否与上次值相同）
        if (!updating) {
            immediateUpdate(itemKey, value, fieldKey);
        }
    };

    // 处理输入框失去焦点 - 同步到表单状态并提交
    const handleLocalInputBlur = (categoryId: string, itemKey: string) => {
        const fieldKey = `${categoryId}-${itemKey}`;
        const currentState = localInputStates[fieldKey];

        if (!currentState) return;

        const currentValue = (currentState.value + '').trim();
        const focusValue = focusValues.current[fieldKey] || '';

        // 同步到全局表单状态
        updateFormData(categoryId, itemKey, currentValue);
        onItemChange?.(categoryId, itemKey, currentValue);

        // 检查变化并发送请求
        if (focusValue !== currentValue) {
            if (!updating) {
                throttledUpdate(itemKey, currentValue, fieldKey);
            }

            // 发送埋点
            if (currentValue.trim() !== '') {
                sendTrackingEvent(itemKey, 'edit');
            }
        }
    };

    // 处理Picker打开
    const handlePickerOpen = (categoryId: string, itemKey: string) => {
        if (updating) return;
        const pickerKey = `${categoryId}-${itemKey}`;
        setPickerStates(prev => ({
            ...prev,
            [pickerKey]: true
        }));
    };

    // 处理Picker关闭
    const handlePickerClose = (categoryId: string, itemKey: string) => {
        const pickerKey = `${categoryId}-${itemKey}`;
        setPickerStates(prev => ({
            ...prev,
            [pickerKey]: false
        }));
    };

    // 判断是否为左右布局（非输入框类型）
    const isLeftRightLayout = (optionType: number) => {
        return optionType === 2 || optionType === 3;
    };
    // 渲染输入框 - 使用本地状态
    const renderInput = (categoryId: string, item: HealthItem) => {
        const fieldKey = `${categoryId}-${item.key}`;
        const localState = localInputStates[fieldKey] || {
            value: formData[categoryId]?.[item.key] || '',
            charCount: 0
        };

        const commonProps = {
            className: cx(styles.healthInput, 'wz-br-36'),
            disabled: updating,
            placeholder: item.placeholder,
            placeholderClass: styles.textareaPlaceholder,
            maxlength: MAX_INPUT_LENGTH,
            value: localState.value, // 使用本地状态的值
            onFocus: () => handleInputFocus(categoryId, item.key),
            onBlur: e => handleLocalInputBlur(categoryId, item.key, e)
        };

        return (
            <View className={cx(styles.textareaBox, 'wz-br-36')}>
                <Textarea
                    {...commonProps}
                    onInput={e => handleLocalInputChange(categoryId, item.key, e)}
                />
                <View className={styles.charCountContainer}>
                    <Text className={styles.charCountNum}>
                        {localState.charCount} {/* 使用本地状态的字符计数 */}
                    </Text>
                    <Text className={styles.charCountTotal}>/{MAX_INPUT_LENGTH}</Text>
                </View>
            </View>
        );
    };

    // 渲染是否选择标签
    const renderBooleanTags = (categoryId: string, item: HealthItem) => (
        <View className={styles.tagContainer}>
            {item.options.map(option => (
                <View
                    key={option.value}
                    className={cx(styles.tag, {
                        [styles.tagActive]: formData[categoryId]?.[item.key] === option.value
                    })}
                    onClick={() => !updating && handleTagSelect(categoryId, item.key, option.value)}
                >
                    {option.label}
                </View>
            ))}
        </View>
    );
    // 渲染选择器
    const renderPicker = (categoryId: string, item: HealthItem) => {
        const pickerKey = `${categoryId}-${item.key}`;
        const isPickerShow = pickerStates[pickerKey] || false;
        const currentValue = formData[categoryId]?.[item.key] || '';
        const displayValue = currentValue || item.value || '';

        return (
            <View
                className={styles.pickerWrapper}
                onClick={() => !updating && handlePickerOpen(categoryId, item.key)}
            >
                <HealthPicker
                    placeholder='请选择'
                    title={item.name}
                    value={displayValue}
                    options={item.options}
                    show={isPickerShow}
                    disabled={updating}
                    onOpen={() => {}} // 清空内部的onOpen，避免重复触发 (扩大了点击区域)
                    onClose={() => handlePickerClose(categoryId, item.key)}
                    onChange={value => handlePickerSelect(categoryId, item.key, value)}
                />
                <HImage src={LINK_DIRECT} className={styles.arrowIcon} width={48} height={48} />
            </View>
        );
    };

    // 渲染项目控件
    const renderItemControl = (categoryId: string, item: HealthItem) => {
        switch (item.optionType) {
            case 1: // 输入框
                return renderInput(categoryId, item);
            case 2: // 是或否
                return renderBooleanTags(categoryId, item);
            case 3: // 多选一
                return renderPicker(categoryId, item);
            default:
                return null;
        }
    };

    // 显示loading状态
    if (loading && !apiData) {
        return (
            <View className={styles.healthHistoryContainer}>
                <View className={styles.loadingContainer}>
                    <HImage src={GIF_LOADING_GREEN} width={200} height={100} />
                </View>
            </View>
        );
    }

    return (
        <View className={styles.healthHistoryContainer}>
            {healthData.map(category => (
                <View
                    key={category.healthCategoryId}
                    className={cx(
                        styles.healthCard,
                        styles[getCategoryThemeClass(category.categoryName)]
                    )}
                >
                    <View className={styles.cardHeader}>
                        <View
                            className={styles.categoryIcon}
                            // style={{backgroundColor: getCategoryColor(category.categoryName)}} 本次无需定制化背景色 （留下拓展样式位置）
                        >
                            <HImage
                                src={getCategoryIcon(category.categoryName)}
                                width={63}
                                height={63}
                                className={styles.categoryIconImage}
                            />
                        </View>
                        <Text className={styles.categoryTitle}>{category.categoryName}</Text>
                    </View>
                    <View className={styles.cardContent}>
                        {category.items.map(item => (
                            <View
                                key={item.key}
                                className={cx(styles.healthItem, {
                                    [styles.leftRightLayout]: isLeftRightLayout(item.optionType),
                                    [styles.topBottomLayout]: !isLeftRightLayout(item.optionType)
                                })}
                            >
                                {isLeftRightLayout(item.optionType) ? (
                                    // 左右布局
                                    <>
                                        <Text className={styles.itemName}>{item.name}</Text>
                                        <View className={styles.itemControl}>
                                            {renderItemControl(category.healthCategoryId, item)}
                                        </View>
                                    </>
                                ) : (
                                    // 上下布局（输入框）
                                    <>
                                        <View className={styles.itemHeader}></View>
                                        <View className={styles.itemControl}>
                                            {renderItemControl(category.healthCategoryId, item)}
                                        </View>
                                    </>
                                )}
                            </View>
                        ))}
                    </View>
                </View>
            ))}
        </View>
    );
};

export default memo(HealthHistoryCards);
