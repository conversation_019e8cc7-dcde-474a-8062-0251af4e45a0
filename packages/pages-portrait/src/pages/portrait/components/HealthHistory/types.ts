import {GetHealthHistoryResponse} from '../../../../models/services/portrait/healthHistory/type';

// 健康史选项
export interface Option {
    /**
     * 前端展示专用，比如 正常/异常
     */
    label: string;
    /**
     * 后端使用的值，比如1，2
     */
    value: string;
}

// 健康史项目
export interface Item {
    /**
     * 标识某个健康史的子项，比如孕产情况
     */
    key: string;
    /**
     * 子项名称，比如孕产情况
     */
    name: string;
    options: Option[];
    /**
     * 1 输入框，2 是或否，3 多选一
     */
    optionType: number;
    /**
     * 子项值，单选框 0，1 输入框就是文字
     */
    value: string;
    /**
     * 输入框占位文案
     */
    placeholder: string;
}

// 健康史分类
export interface HealthHistory {
    /**
     * 健康史子项目名字，例如，个人史
     */
    categoryName: string;
    /**
     * 健康史ID，表明当前项史个人史，疾病史，还是家族史
     */
    healthCategoryId: string;
    items: Item[];
}

export type HealthCategory = HealthHistory;

// 健康史项目
export type HealthItem = Item;

// 组件Props
export interface HealthHistoryCardsProps {
    apiData?: GetHealthHistoryResponse;
    patientId?: string;
    onItemChange?: (categoryId: string, itemKey: string, value: string) => void;
    onUpdateSuccess?: () => void;
    onUpdateError?: (error: string) => void;
}
