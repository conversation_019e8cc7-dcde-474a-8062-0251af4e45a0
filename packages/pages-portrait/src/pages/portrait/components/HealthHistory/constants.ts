// constants/index.ts
import {ApiResponse} from './types';

// 模拟API数据
export const MOCK_API_DATA: ApiResponse = {
    status: 0,
    msg: 'success',
    toast: '',
    applid: '123',
    lid: '456',
    data: {
        healthHistory: [
            {
                healthCategoryId: 'personal',
                categoryName: '个人史',
                items: [
                    {
                        key: 'pregnancy',
                        name: '孕产情况',
                        value: '请选择',
                        optionType: 3,
                        options: [
                            {label: '备孕', value: 'preparing'},
                            {label: '孕期', value: 'pregnant'},
                            {label: '哺乳期', value: 'lactating'},
                            {label: '无', value: 'none'}
                        ]
                    },
                    {
                        key: 'liver',
                        name: '肝功能情况',
                        value: 'normal',
                        optionType: 2,
                        options: [
                            {label: '正常', value: 'normal'},
                            {label: '异常', value: 'abnormal'}
                        ]
                    },
                    {
                        key: 'kidney',
                        name: '肾功能情况',
                        value: 'normal',
                        optionType: 2,
                        options: [
                            {label: '正常', value: 'normal'},
                            {label: '异常', value: 'abnormal'}
                        ]
                    }
                ]
            },
            {
                healthCategoryId: 'disease',
                categoryName: '疾病史',
                items: [
                    {
                        key: 'gastroesophageal_reflux',
                        name: '请描述患过的疾病',
                        value: '胃食管反流，2024年10月，已痊愈',
                        optionType: 1,
                        options: []
                    }
                ]
            },
            {
                healthCategoryId: 'allergy',
                categoryName: '过敏史',
                items: [
                    {
                        key: 'food_allergy',
                        name: '请描述您曾对何种食物、药物过敏',
                        value: '花生、树木坚果引起严重的过敏性反应，包括面部及喉咙肿胀规避护肤风险的第一道防线',
                        optionType: 1,
                        options: []
                    }
                ]
            },
            {
                healthCategoryId: 'family',
                categoryName: '家族史',
                items: [
                    {
                        key: 'diabetes',
                        name: '请描述您家族成员患有过何种遗传性疾病',
                        value: '本人外祖父、舅舅均患有二型糖尿病',
                        optionType: 1,
                        options: []
                    }
                ]
            }
        ]
    }
};

// 更新接口防抖延迟（毫秒）
export const UPDATE_DEBOUNCE_DELAY = 1000;

// 字符限制
export const MAX_INPUT_LENGTH = 250;
