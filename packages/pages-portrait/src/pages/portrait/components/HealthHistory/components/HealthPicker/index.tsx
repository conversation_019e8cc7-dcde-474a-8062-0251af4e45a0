import React, {memo, useCallback, useMemo, type FC} from 'react';
import {HPicker} from '@baidu/health-ui';
import {View} from '@tarojs/components';
import cx from 'classnames';
import Portal from '../../../../../../../../ui-cards-common/Portal';
import styles from './index.module.less';

interface Option {
    label: string;
    value: string;
}

interface HealthPickerProps {
    placeholder?: string;
    title?: string;
    value?: string;
    options: Option[];
    onChange?: (value: string) => void;
    disabled?: boolean;
    show?: boolean;
    onOpen?: () => void;
    onClose?: () => void;
}

const HealthPicker: FC<HealthPickerProps> = props => {
    const {
        placeholder = '请选择',
        title = '请选择',
        value = '',
        options = [],
        onChange,
        disabled = false,
        show = false,
        onOpen,
        onClose
    } = props;

    // 将options转换为HPicker需要的格式 - 单列选择器
    const pickerOptions = useMemo(() => {
        return [
            options.map(opt => ({
                value: opt.value,
                text: opt.label
            }))
        ];
    }, [options]);

    // 获取当前选中项的显示文本
    const selectedText = useMemo(() => {
        if (!value || !options.length) return placeholder;
        const selectedOption = options.find(option => option.value === value);
        return selectedOption ? selectedOption.label : placeholder;
    }, [options, value, placeholder]);

    const pickerValue = useMemo(() => {
        return [value]; // 直接返回当前值的数组
    }, [value]);

    // 默认值也使用值数组
    const pickerDefaultValue = useMemo(() => {
        return options.length > 0 ? [options[0].value] : [''];
    }, [options]);

    // 处理取消
    const handleCancel = useCallback(() => {
        onClose?.();
    }, [onClose]);

    const handleConfirm = useCallback(
        (selectedIndexes: number[]) => {
            // 直接使用HPicker返回的选中值
            const selectedValue = selectedIndexes.length > 0 ? selectedIndexes[0] : '';
            onChange?.(selectedValue);
            onClose?.();
        },
        [onChange, onClose]
    );

    return (
        <View className={cx(styles.healthPicker, {[styles.disabled]: disabled})}>
            <View
                className={cx('wz-flex-1 wz-flex', {
                    'wz-row-right': value
                })}
                onClick={onOpen}
            >
                {value && value !== placeholder ? (
                    <View className={styles.selectedText}>{selectedText}</View>
                ) : (
                    <View className={styles.placeholder}>{placeholder}</View>
                )}
            </View>

            <Portal>
                <HPicker
                    show={show}
                    title={title}
                    options={pickerOptions}
                    value={pickerValue}
                    defaultValue={pickerDefaultValue}
                    onClose={handleCancel}
                    onCancel={handleCancel}
                    onConfirm={handleConfirm}
                />
            </Portal>
        </View>
    );
};

HealthPicker.displayName = 'HealthPicker';
export default memo(HealthPicker);
