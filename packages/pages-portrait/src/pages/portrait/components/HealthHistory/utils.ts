import type {UpdateHealthHistoryRequest} from './types';

// TODO：此处为健康史headerIcon，后续需要和后端约定，定制化样式同样走接口配置！
const PersonalHistory = 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/personalIcon.png';
const DiseaseHistory = 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/diseaseIcon.png';
const AllergyHistory = 'https://med-fe.cdn.bcebos.com/vita/AllergyHstory.png';
const FamilyHistory = 'https://med-fe.cdn.bcebos.com/vita/FamilyHistory.png';

/**
 * 获取分类图标
 * @param categoryName 分类名称
 * @returns 图标文字
 */
export const getCategoryIcon = (categoryName: string): string => {
    const iconMap: Record<string, string> = {
        个人史: PersonalHistory,
        疾病史: DiseaseHistory,
        过敏史: AllergyHistory,
        家族史: FamilyHistory
    };
    return iconMap[categoryName] || '📋';
};

/**
 * 获取分类颜色
 * @param categoryName 分类名称
 * @returns 颜色值
 */
export const getCategoryColor = (categoryName: string): string => {
    const colorMap: Record<string, string> = {
        个人史: '#4ECDC4',
        疾病史: '#9B59B6',
        过敏史: '#E67E22',
        家族史: '#3498DB'
    };
    return colorMap[categoryName] || '#95a5a6';
};

/**
 * 获取分类主题类名
 * @param categoryName 分类名称
 * @returns 主题类名
 */
export const getCategoryThemeClass = (categoryName: string): string => {
    const themeMap: Record<string, string> = {
        个人史: 'personalTheme',
        疾病史: 'medicalTheme',
        家族史: 'familyTheme',
        过敏史: 'allergyTheme'
    };
    return themeMap[categoryName] || 'defaultTheme';
};

/**
 * 构建更新参数
 * @param patientId 患者ID
 * @param itemKey 项目键
 * @param value 值
 * @returns 更新参数对象
 */
export const buildUpdateParams = (
    patientId: string,
    itemKey: string,
    value: string
): UpdateHealthHistoryRequest | null => {
    if (!patientId) {
        console.warn('缺少 patientId');
        return null;
    }

    // 根据itemKey构建对应的更新参数
    const updateParams: UpdateHealthHistoryRequest = {
        patientId
    };

    // 映射关系：itemKey -> API字段
    const keyMapping: Record<string, keyof UpdateHealthHistoryRequest> = {
        gestation: 'gestation', // 孕产情况
        liver: 'liver', // 肝功能
        kidney: 'kidney', // 肾功能
        medicalHistory: 'medicalHistory', // 疾病史
        allergyHistory: 'allergyHistory', // 过敏史
        familyHistory: 'familyHistory', // 家族史
        name: 'name', // 姓名
        gender: 'gender', // 性别
        birthday: 'birthday' // 生日
    };

    const apiField = keyMapping[itemKey];
    if (apiField) {
        updateParams[apiField] = value;
    } else {
        console.warn(`未知的itemKey: ${itemKey}`);
        return null;
    }

    return updateParams;
};
/**
 * 防抖函数
 */
export const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
): ((...args: Parameters<T>) => void) => {
    let timeout;
    return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
};
