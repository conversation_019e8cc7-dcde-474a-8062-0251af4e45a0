.popupIntroContent {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 50vh;
    max-height: 70vh;

    .title {
        padding-bottom: 54px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 54px;
        line-height: 54px;
        letter-spacing: 0%;
        text-align: center;
        color: #272933;
    }

    .body {
        flex: 1;
        overflow-y: auto;
    }

    .bodyItemText {
        margin-bottom: 48px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 45px;
        line-height: 69px;
        letter-spacing: 0;
        color: #272933;
    }

    .bodyItemImage {
        width: 100%;
        height: auto;
    }

    .footer {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        padding-top: 24px;
        background: #fff;
    }

    .confirmButton {
        flex: 1;
        margin: 45px 0 27px;
        height: 132px;
        background: linear-gradient(315deg, #00d3ea 0%, #00cfa3 100%);
    }
}
