import React, {memo, useCallback, useEffect} from 'react';
import {View, Text} from '@tarojs/components';
import {Popup, SafeArea} from '@baidu/wz-taro-tools-core';
import {useAtomValue, useSetAtom} from 'jotai';
import {HB<PERSON>on, HImage} from '@baidu/health-ui';
import cx from 'classnames';
import AgreementPrivacy from '../../../../components/AgreementPrivacy';
import {
    portraitIntroPopupHasShowSetAtom,
    portraitIntroPopupVisibleSetAtom
} from '../../../../store/portrait';
import {portraitIntroPopupVisibleOnceAtom} from '../../../../store/portrait/derivedAtoms';
import {usePatientData} from '../../../../hooks/portrait/usePatientData';
import {
    ubcCommonClkSend,
    ubcCommonViewSend
} from '../../../../../../pages-im/src/utils/generalFunction/ubc';
import {UBC_FIELD_PAGE} from '../../constants';

import styles from './index.module.less';

const PopupIntro: React.FC = () => {
    const {introPopupData} = usePatientData();
    const portraitIntroPopupVisibleSet = useSetAtom(portraitIntroPopupVisibleSetAtom);
    const portraitIntroPopupHasShowSet = useSetAtom(portraitIntroPopupHasShowSetAtom);
    const portraitIntroPopupVisibleOnce = useAtomValue(portraitIntroPopupVisibleOnceAtom);
    const hidePopup = useCallback(() => {
        portraitIntroPopupVisibleSet(false);
        portraitIntroPopupHasShowSet(true);
    }, [portraitIntroPopupVisibleSet, portraitIntroPopupHasShowSet]);

    const handleClose = useCallback(() => {
        hidePopup();
    }, [hidePopup]);

    const handleConfirm = useCallback(() => {
        hidePopup();
        // ubc打点逻辑
        ubcCommonClkSend({
            value: 'introPopup_confirmBtn',
            page: UBC_FIELD_PAGE
        });
    }, [hidePopup]);

    useEffect(() => {
        if (portraitIntroPopupVisibleOnce) {
            // ubc打点逻辑
            ubcCommonViewSend({
                value: 'introPopup',
                page: UBC_FIELD_PAGE
            });
        }
    }, [portraitIntroPopupVisibleOnce]);

    return (
        <Popup
            open={portraitIntroPopupVisibleOnce}
            catchMove={false}
            placement='bottom'
            rounded
            onClose={handleClose}
        >
            <Popup.Close />
            <View className={cx(styles.popupIntroContent, 'wz-plr-51')}>
                <View className={cx(styles.title, 'wz-text-center wz-pt-51 wz-fs-54 wz-fw-500')}>
                    {introPopupData?.title}
                </View>
                <View className={styles.body}>
                    {introPopupData?.content?.map((item, index) => (
                        <>
                            {item.type === 'text' ? (
                                <View className={styles.bodyItemText} key={index}>
                                    <Text>{item.value}</Text>
                                </View>
                            ) : null}
                            {item.type === 'img' ? (
                                <View>
                                    <HImage
                                        src={item.value}
                                        className={styles.bodyItemImage}
                                        width='100%'
                                        modeType='widthFix'
                                        height='auto'
                                        key={index}
                                    />
                                </View>
                            ) : null}
                        </>
                    ))}
                </View>
                <View className={styles.footer}>
                    <AgreementPrivacy />
                    <HButton
                        className={cx(styles.confirmButton, 'wz-mlr-51 wz-mtb-24')}
                        text='我知道了'
                        onClick={handleConfirm}
                    />
                    <SafeArea position='bottom' />
                </View>
            </View>
        </Popup>
    );
};

export default memo(PopupIntro);
