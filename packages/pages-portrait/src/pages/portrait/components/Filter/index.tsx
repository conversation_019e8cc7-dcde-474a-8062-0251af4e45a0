import React, {memo, useState, useRef, useEffect, forwardRef, useImperativeHandle} from 'react';
import {View, Text, ITouchEvent} from '@tarojs/components';
import {HButton, HIcon, HImage} from '@baidu/health-ui';
import cx from 'classnames';
import {createSelectorQuery, getSystemInfoSync, nextTick} from '@tarojs/taro';
import {FILTER} from '../../../../constants/images';
import Portal from '../../../../../../ui-cards-common/Portal';
import styles from './index.module.less';

export interface FilterOption {
    text: string;
    value: string;
    key?: string;
}

export interface FilterRef {
    reset: () => void;
}

interface FilterProps {
    options: FilterOption[];
    interceptorBeforeClick?: () => boolean; // 用于点击前拦截器，返回true后才执行点击
    onChange?: (option: FilterOption | null) => void;
    onConfirm?: (option: FilterOption | null) => void;
    onReset?: () => void;
    onOptionsVisibleChange?: (visible: boolean) => void;
}

const ID_FILTER_HEADER = 'filterHeader';

const Filter = forwardRef<FilterRef, FilterProps>((props, ref) => {
    const {options, interceptorBeforeClick, onChange, onConfirm, onReset, onOptionsVisibleChange} =
        props;
    const [visible, setVisible] = useState(false);
    const [headerTop, setHeaderTop] = useState(0);
    const [selectingOption, setSelectingOption] = useState<FilterOption | null>(null);
    const [selectedOption, setSelectedOption] = useState<FilterOption | null>(null);
    const headerRef = useRef<any>(null);
    const [optionsContainerMaxHeight, setoptionsContainerMaxHeight] = useState(0);

    const calculateHeightsAndShowOptions = (rect: any) => {
        setHeaderTop(rect.bottom);
        // 计算筛选选项容器的高度
        let windowHeight = 667; // 默认高度

        try {
            if (process.env.TARO_ENV === 'swan') {
                // 小程序环境使用 getSystemInfoSync 获取屏幕高度
                const systemInfo = getSystemInfoSync();
                windowHeight = systemInfo.windowHeight;
            } else {
                windowHeight = window.innerHeight;
            }
        } catch (error) {
            console.warn('获取系统信息失败，使用默认高度', error);
        }

        const buttonAreaHeight = 64; // 按钮区域高度，粗估
        const bottomNavHeight = 97; // 底部导航栏高度，粗估
        const maxHeight = windowHeight - rect.bottom - buttonAreaHeight - bottomNavHeight;
        setoptionsContainerMaxHeight(Math.max(maxHeight, 200));
    };

    const handleFilterHeaderClick = () => {
        if (visible) {
            return;
        }
        // 获取filterHeader的位置信息
        if (interceptorBeforeClick && !interceptorBeforeClick()) {
            return;
        }

        setTimeout(() => {
            if (headerRef.current) {
                if (process.env.TARO_ENV === 'swan') {
                    const query = createSelectorQuery();
                    query
                        .select(`#${ID_FILTER_HEADER}`)
                        .boundingClientRect((rect: any) => {
                            if (rect) {
                                calculateHeightsAndShowOptions(rect);
                            }
                            setVisible(true);
                        })
                        .exec();
                } else if (process.env.TARO_ENV === 'h5') {
                    const element = document.getElementById(ID_FILTER_HEADER);
                    if (element) {
                        const rect = element.getBoundingClientRect();
                        calculateHeightsAndShowOptions(rect);
                    }
                    setVisible(true);
                }
            }
        }, 100); // 100ms 后执行，避免页面滚动，获取到 filterHeader 的错误位置信息
    };

    const handleOptionSelect = (option: FilterOption) => {
        setSelectingOption(option);
    };

    const handleReset = () => {
        setVisible(false);
        nextTick(() => {
            setSelectingOption(null);
            setSelectedOption(null);
            onReset?.();
        });
    };

    const handleCancel = () => {
        setVisible(false);
        nextTick(() => {
            setSelectingOption(selectedOption ? {...selectedOption} : null);
        });
    };

    const handleConfirm = () => {
        setVisible(false);
        nextTick(() => {
            setSelectedOption(selectingOption ? {...selectingOption} : null);
            onConfirm?.(selectingOption ? {...selectingOption} : null);
        });
    };

    const handleOverlayClick = (event: ITouchEvent) => {
        event.stopPropagation();
        event.preventDefault();
        handleCancel();
    };

    useEffect(() => {
        onChange?.(selectedOption);
    }, [selectedOption]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        onOptionsVisibleChange?.(visible);
    }, [visible]); // eslint-disable-line react-hooks/exhaustive-deps

    useImperativeHandle(ref, () => {
        return {
            reset: handleReset
        };
    });

    return (
        <View className={styles.filterContainer}>
            <View
                ref={headerRef}
                id={ID_FILTER_HEADER}
                className={styles.filterHeader}
                onClick={handleFilterHeaderClick}
            >
                <View className={styles.filterIcon}>
                    <HImage src={FILTER} width={54} height={54} radius={12} />
                </View>
                <Text className={styles.filterLabelPrefix}>
                    {selectedOption && selectedOption.value ? '已选类型：' : ''}
                </Text>
                {selectedOption && selectedOption.value ? (
                    <Text className={cx(styles.filterLabel, styles.filterLabelSelected)}>
                        {selectedOption.text}
                    </Text>
                ) : (
                    <Text className={styles.filterLabel}>筛选</Text>
                )}
                {visible ? (
                    <HIcon value='wise-fold-up' size={54} />
                ) : (
                    <HIcon value='wise-fold-down' size={54} />
                )}
            </View>
            <Portal>
                {visible && (
                    <View className={styles.filterOptionsOverlay} onClick={handleOverlayClick}>
                        <View
                            className={styles.filterContentOverlay}
                            style={{
                                top: `${headerTop}px`
                            }}
                        >
                            <View
                                className={styles.filterContent}
                                onClick={event => event.stopPropagation()}
                            >
                                <View
                                    className={styles.filterOptions}
                                    style={{
                                        height: optionsContainerMaxHeight
                                    }}
                                >
                                    {options.map(option => (
                                        <View
                                            className={cx(styles.filterOption, {
                                                [styles.filterOptionActive]:
                                                    selectingOption?.value === option.value
                                            })}
                                            key={option.value}
                                            onClick={() => handleOptionSelect(option)}
                                        >
                                            <Text>{option.text}</Text>
                                            {selectingOption?.value === option.value && (
                                                <HIcon
                                                    value='wise-check-succes'
                                                    size={54}
                                                    color='#00c8c8'
                                                    className={styles.filterOptionIcon}
                                                />
                                            )}
                                        </View>
                                    ))}
                                </View>
                                <View className={styles.filterButtons}>
                                    <HButton
                                        text='重置'
                                        className={styles.filterResetButton}
                                        onClick={handleReset}
                                    />
                                    <HButton
                                        text='确认'
                                        className={styles.filterConfirmButton}
                                        onClick={handleConfirm}
                                    />
                                </View>
                            </View>
                        </View>
                    </View>
                )}
            </Portal>
        </View>
    );
});

Filter.displayName = 'Filter';

export default memo(Filter);
