/* stylelint-disable at-rule-no-unknown */
@primaryColor: #00c8c8;

.filterHeader {
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 42px;
    line-height: 42px;
    letter-spacing: 0%;
    vertical-align: middle;
}

.filterIcon {
    margin-right: 6px;
}

.filterLabelSelected {
    color: @primaryColor;
}

.filterOptionsOverlay {
    position: fixed;
    inset: 0;
}

.filterContentOverlay {
    display: flex;
    flex-direction: column;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgb(0 0 0 / 50%);
}

.filterContent {
    width: 100vw;
    margin-top: -3px;
    padding-top: 45px;
    background-color: #fafbff;
    border-bottom-left-radius: 63px;
    border-bottom-right-radius: 63px;
}

.filterOptions {
    overflow-y: auto;
}

.filterOption {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 45px 54px;
    font-size: 48px;
    font-family: PingFang SC;
    color: #000311;
}

.filterOptionIcon {
    color: @primaryColor;
}

.filterOptionActive {
    color: @primaryColor;
    font-weight: 500;
}

.filterButtons {
    display: flex;
    justify-content: space-between;
    padding: 45px 51px 27px;
    border-top: 1.5px solid #ededf0;
}

.filterResetButton,
.filterConfirmButton {
    width: 558px;
    height: 120px;
    box-sizing: border-box;
}

.filterResetButton {
    color: @primaryColor !important;
    background: #00c8c81a !important;
}

.filterConfirmButton {
    background: @primaryColor;
    color: #fff;
}
