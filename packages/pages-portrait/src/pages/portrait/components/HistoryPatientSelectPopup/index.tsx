import React, {useState, useEffect, memo} from 'react';
import {View, Text, ScrollView} from '@tarojs/components';
import {useAtom} from 'jotai';
import {Button} from '@baidu/wz-taro-tools-core';
import Taro from '@tarojs/taro';
import cx from 'classnames';
import {HImage} from '@baidu/health-ui';
import {CHECKED_ICON} from '../../../../constants/images';
import {closeMultiPopupAtom} from '../../../../store/portrait/zIndexAtoms';
import {usePopup} from '../../../../hooks/portrait/useSyncHistoryPatientPopup';
import {usePatientData, usePatientDataFetch} from '../../../../hooks/portrait/usePatientData';
import AgreementPrivacy from '../../../../components/AgreementPrivacy';
import {
    createByHistoryPatient,
    getHistoryPatients
} from '../../../../models/services/portrait/historyPatient';
import {Patient} from '../../../../typings/patient';
import {
    ubcCommonViewSend,
    ubcCommonClkSend
} from '../../../../../../pages-im/src/utils/generalFunction/ubc';
import {UBC_FIELD_PAGE} from '../../constants';
import {HistoryPatientSelectPopupProps} from './types';

import styles from './index.module.less';

const descriptionText =
    '将你在百度健康的历史就诊人信息同步至健康档案，我会为你记录完整就医旅程，解锁专属健康方案。';

const HistoryPatientSelectPopup: React.FC<HistoryPatientSelectPopupProps> = ({data}) => {
    const {refreshPatientData} = usePatientDataFetch();
    const {selectedPatientId} = usePatientData();
    const [, closeMultiPopup] = useAtom(closeMultiPopupAtom);
    const {showRelationshipSelectPopup} = usePopup();
    const [historyPatients, setHistoryPatients] = useState<Patient[]>([]);
    const [selectedPatients, setSelectedPatients] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);
    const [creating, setCreating] = useState(false);

    // 弹窗曝光埋点
    useEffect(() => {
        ubcCommonViewSend({
            value: 'historyPatientPopup',
            page: UBC_FIELD_PAGE
        });
    }, []);

    const handlePatientData = (patients: Patient[]) => {
        // 设置就诊人列表
        setHistoryPatients(patients);
        // 生成所有患者ID的数组（全选）
        const allPatientIds = patients.map(p => p.patientId);
        // 更新选中状态
        setSelectedPatients(allPatientIds);
        data?.onSelectionChange?.(allPatientIds);
    };

    // 获取历史就诊人列表
    const fetchHistoryPatients = async () => {
        setLoading(true);
        try {
            const result = await getHistoryPatients({});
            if (result?.patients) {
                handlePatientData(result.patients);
            }
        } catch (error) {
            console.error('获取历史就诊人列表失败:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // 优先使用传入的数据，否则调用接口获取
        if (data?.historyPatients && data.historyPatients.length > 0) {
            handlePatientData(data.historyPatients);
        } else {
            fetchHistoryPatients();
        }

        // 保留外部传入选中数据的优先级
        if (data?.selectedPatients) {
            setSelectedPatients(data.selectedPatients);
        }
    }, [data]);

    // 处理患者选择
    const handlePatientSelect = (patientId: string) => {
        const isCurrentlySelected = selectedPatients.includes(patientId);

        // 勾选历史就诊人埋点
        ubcCommonClkSend({
            value: isCurrentlySelected
                ? 'historyPatientPopup_selectBtn0'
                : 'historyPatientPopup_selectBtn1',
            page: UBC_FIELD_PAGE
        });

        const newSelection = isCurrentlySelected
            ? selectedPatients.filter(id => id !== patientId)
            : [...selectedPatients, patientId];

        setSelectedPatients(newSelection);
        data?.onSelectionChange?.(newSelection);
    };

    // 打开关系选择popup
    const handleRelationshipSelect = (patientId: string) => {
        const patient = historyPatients.find(p => p.patientId === patientId);
        if (!patient || !patient.relationships) return;

        // 关系切换按钮埋点
        ubcCommonClkSend({
            value: 'historyPatientPopup_rsBtn',
            page: UBC_FIELD_PAGE
        });

        showRelationshipSelectPopup({
            relationships: patient.relationships,
            currentRelationship: patient.relationship,
            onSelect: (relationship: string) => {
                // 更新患者关系 - 这个回调会在关系选择确认时执行
                const updatedPatients = historyPatients.map(p =>
                    p.patientId === patientId ? {...p, relationship} : p
                );
                setHistoryPatients(updatedPatients);
                console.log('关系已更新:', relationship);
            },
            allowMultiple: true // 启用多层弹窗
        });
    };

    // 创建健康档案
    const handleConfirm = async () => {
        if (selectedPatients.length === 0) {
            Taro.showToast({
                title: '请选择至少一个历史就诊人',
                icon: 'none'
            });
            return;
        }

        // 确认同步信息按钮埋点
        ubcCommonClkSend({
            value: 'historyPatientPopup_confirmBtn',
            page: UBC_FIELD_PAGE
        });

        setCreating(true);
        try {
            const selectedPatientsData = historyPatients
                .filter(patient => selectedPatients.includes(patient.patientId))
                .map(patient => ({
                    patientId: patient.patientId,
                    relationship: patient.relationship,
                    name: patient.name,
                    gender: patient.gender,
                    birthDate: patient.birthDate
                }));

            const result = await createByHistoryPatient({
                patients: selectedPatientsData
            });

            const failedPatients = result.patients.filter(p => p.isSuccess === 0);

            if (failedPatients.length > 0) {
                // 有创建失败的患者
                Taro.showToast({
                    title: `${failedPatients.length}位就诊人创建失败`,
                    icon: 'none'
                });
                // 重新获取历史就诊人列表
                await fetchHistoryPatients();
                refreshPatientData(selectedPatientId);
            } else {
                // 全部创建成功
                Taro.showToast({
                    title: '创建成功',
                    icon: 'success'
                });
                await data?.onConfirm?.();
                data?.onSuccess?.(result);
                closeMultiPopup();
                refreshPatientData(undefined, {selectLastPatient: true});
            }
        } catch (error) {
            console.error('创建健康档案失败:', error);
            Taro.showToast({
                title: '创建失败，请重试',
                icon: 'none'
            });
            // 重新获取历史就诊人列表
            await fetchHistoryPatients();
        } finally {
            setCreating(false);
        }
    };

    // 格式化姓名显示（最多5个字，超出显示...）
    const formatName = (name: string) => {
        return name.length > 5 ? `${name.substring(0, 5)}...` : name;
    };

    return (
        <View className={styles.container}>
            <Text className={styles.description}>{descriptionText}</Text>
            <Text className={styles.subText}>*包含身高体重、疾病史、地址等信息</Text>

            {loading ? (
                <View className={styles.loading}>
                    <Text>加载中...</Text>
                </View>
            ) : (
                <ScrollView className={styles.patientList} scrollY>
                    {historyPatients.map(patient => {
                        const isSelected = selectedPatients.includes(patient.patientId);

                        return (
                            <View
                                key={patient.patientId}
                                className={cx(styles.patientItem, {
                                    [styles.selected]: isSelected
                                })}
                            >
                                <View
                                    className={styles.patientContent}
                                    onClick={() => handlePatientSelect(patient.patientId)}
                                >
                                    <View className={styles.checkbox}>
                                        {isSelected && (
                                            <HImage
                                                className={styles.checkmark}
                                                src={CHECKED_ICON}
                                                width={54}
                                                height={54}
                                            />
                                        )}
                                    </View>

                                    <View className={styles.patientInfo}>
                                        <View className={styles.patientRow}>
                                            <Text className={styles.name}>
                                                {formatName(patient.name)}
                                            </Text>
                                            <Text className={styles.gender}>{patient.gender}</Text>
                                            <Text className={styles.divider}>|</Text>
                                            <Text className={styles.age}>{patient.age}</Text>
                                            {patient.isCertified && (
                                                <Text
                                                    className={cx(
                                                        styles.certifiedTag,
                                                        'wz-flex wz-col-center wz-row-center'
                                                    )}
                                                >
                                                    已实名
                                                </Text>
                                            )}
                                            <View
                                                className={styles.relationTag}
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    handleRelationshipSelect(patient.patientId);
                                                }}
                                            >
                                                <View className={styles.relationText}>
                                                    {patient.relationship || '待完善'}
                                                </View>
                                                <View className={styles.arrow}></View>
                                            </View>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        );
                    })}
                </ScrollView>
            )}

            <View className={styles.privacyNote}>
                <AgreementPrivacy />
            </View>

            <View className={styles.buttonGroup}>
                <Button
                    className={styles.submitBtn}
                    onClick={handleConfirm}
                    disabled={selectedPatients.length === 0 || creating}
                    loading={creating}
                >
                    {creating
                        ? '创建中...'
                        : `确认同步信息${selectedPatients.length > 0 ? `(${selectedPatients.length})` : ''}`}
                </Button>
            </View>
        </View>
    );
};

export default memo(HistoryPatientSelectPopup);
