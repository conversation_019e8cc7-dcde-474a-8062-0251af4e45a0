.container {
    padding: 40px 51px 27px;
    background: #fff;
    border-radius: 24px 24px 0 0;
    max-height: 75vh;
    display: flex;
    flex-direction: column;

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 32px;

        .title {
            font-size: 36px;
            font-weight: 600;
            color: #333;
        }

        .closeBtn {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .closeIcon {
                font-size: 32px;
                color: #999;
                line-height: 1;
            }
        }
    }

    .description {
        color: #272933;
        margin-bottom: 16px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 45px;
        line-height: 1.5;
    }

    .subText {
        font-size: 45px;
        color: #f60;
        margin-bottom: 60px;
    }

    .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 30px;
        font-size: 45px;
        color: #666;
        height: 500px;
    }

    .patientList {
        flex: 1;
        margin-bottom: 30px;
        max-height: 500px;
        min-height: 500px;

        .patientItem {
            background: #f8f9fa;
            border-radius: 45px;
            margin-bottom: 30px;
            border: 2px solid transparent;
            transition: all 0.3s;
            overflow: hidden;
            height: 171px;

            .patientContent {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 48px;
                cursor: pointer;
                height: 171px;
            }
            /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
            :global {
                .h-image-content {
                    width: 61px;
                    height: 61px;
                }
            }

            .checkbox {
                width: 51px;
                height: 51px;
                border: 3px solid #b7b9c1;
                border-radius: 50%;
                margin-right: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s;
                flex-shrink: 0;

                .checkmark {
                    font-weight: 700;
                    width: 51px !important;
                    height: 51px !important;
                    transform: translate(-6px, -6px);
                }
            }

            .patientInfo {
                flex: 1;
                min-width: 0;

                .patientRow {
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                }

                .name {
                    font-size: 51px;
                    color: #000311;
                    font-weight: 500;
                    margin-right: 36px;
                    font-family: PingFang SC;
                }

                .divider {
                    font-size: 45px;
                    color: #999;
                    margin-right: 8px;
                    font-family: PingFang SC;
                    font-weight: 400;
                }

                .gender {
                    font-size: 45px;
                    color: #000311;
                    font-family: PingFang SC;
                    margin-right: 8px;
                }

                .age {
                    font-size: 45px;
                    color: #000311;
                    font-family: PingFang SC;
                    margin-right: 16px;
                }

                .certifiedTag {
                    background: #00cfa3;
                    color: #fff;
                    font-size: 33px;
                    font-family: PingFang SC;
                    font-weight: 500;
                    padding: 4px 12px;
                    border-radius: 15px;
                    margin-right: 16px;
                    width: 117px;
                    height: 48px;
                }

                .relationTag {
                    display: flex;
                    align-items: center;
                    padding: 8px 16px;
                    border-radius: 8px;
                    flex-shrink: 0;
                    cursor: pointer;
                    margin-left: auto;

                    .relationText {
                        font-size: 45px;
                        color: #848691;
                        margin-right: 8px;
                        font-family: PingFang SC;
                        font-weight: 400;
                    }

                    .arrow {
                        width: 45px;
                        height: 45px;
                        background-image: url('https://med-fe.cdn.bcebos.com/vita/linkDirect.png');
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
                    }
                }
            }

            &.selected {
                background: #e5f9f9;
                border: 1px solid #00bcbc;

                .checkbox {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #fff;
                    border-color: #fff;
                    width: 51px;
                    height: 51px;

                    .checkmark {
                        width: 51px;
                        height: 51px;
                    }
                }
            }
        }
    }

    .privacyNote {
        text-align: center;
        margin: 40px 0 32px;

        .privacyText {
            font-size: 24px;
            color: #999;
        }
    }

    .buttonGroup {
        padding-top: 16px;
        height: 132px;

        .submitBtn {
            width: 100%;
            height: 132px;
            background: linear-gradient(134deg, #00cfa3 0%, #00d3ea 100%);
            color: #fff;
            border: none;
            border-radius: 66px;
            font-size: 54px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;

            &:disabled {
                background: #ccc;
                opacity: 0.6;
            }

            &:active:not(:disabled) {
                opacity: 0.8;
            }
        }
    }
}
