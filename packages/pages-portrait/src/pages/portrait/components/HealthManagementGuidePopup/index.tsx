import React from 'react';
import {View, Text, ScrollView, Image} from '@tarojs/components';
import {Button} from '@baidu/wz-taro-tools-core';
import {useAtom} from 'jotai';
import cx from 'classnames';
import {closeMultiPopupAtom} from '../../../../store/portrait/zIndexAtoms';
import AgreementPrivacy from '../../../../components/AgreementPrivacy';
import styles from './index.module.less';

interface HealthManagementGuidePopupProps {
    data?: {
        illustrationImage?: string;
        maxImageHeight?: number;
        title?: string;
        description?: string;
        subText?: string;
        buttonText?: string;
        onConfirm?: () => void;
        onSuccess?: (data: any) => void;
        showPrivacy?: boolean;
        useAgreementPrivacy?: boolean;
        privacyText?: string;
        privacyLinkText?: string;
        onPrivacyClick?: () => void;
        showCloseButton?: boolean;
        onClose?: () => void;
        loading?: boolean;
    };
}

const HealthManagementGuidePopup: React.FC<HealthManagementGuidePopupProps> = ({data}) => {
    const [, closeMultiPopup] = useAtom(closeMultiPopupAtom);
    const [isLoading, setIsLoading] = React.useState(false);

    const {
        illustrationImage = '',
        maxImageHeight = 400,
        title = '开启健康管理之旅',
        description = '把就医资料存入健康档案，我将为你记录完整就医历程分析关键指标变化，解锁专属健康方案。',
        subText = '*包含身高体重、疾病史、地址等信息',
        buttonText = '我知道了',
        onConfirm,
        onSuccess,
        showPrivacy = true,
        useAgreementPrivacy = true,
        privacyText = '我们将严格保护您的隐私安全，详见',
        privacyLinkText = '《隐私协议》',
        onPrivacyClick,
        loading = false
    } = data || {};

    const handleConfirm = async () => {
        if (isLoading) return;

        try {
            setIsLoading(true);
            await onConfirm?.();

            const successData = {
                confirmed: true,
                timestamp: Date.now(),
                action: 'health_management_guide_confirmed'
            };

            onSuccess?.(successData);
            closeMultiPopup();
        } catch (error) {
            console.error('健康管理引导确认失败:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handlePrivacyClick = () => {
        onPrivacyClick?.();
    };

    // 图片加载错误处理
    const handleImageError = () => {
        console.warn('健康管理引导图片加载失败');
    };

    return (
        <View className={styles.container}>
            {/* 顶部区域 */}
            <View className={styles.fixedTop}>
                <Text className={styles.title}>{title}</Text>
                <Text className={styles.description}>{description}</Text>
                {subText && <Text className={styles.subText}>{subText}</Text>}
            </View>

            {/* 可滚动的说明图区域 */}
            <View className={styles.scrollableArea}>
                <ScrollView
                    className={styles.illustrationContainer}
                    scrollY
                    style={{maxHeight: `${maxImageHeight}px`}}
                    showScrollbar={false}
                    enableBackToTop={false}
                >
                    {illustrationImage ? (
                        <Image
                            src={illustrationImage}
                            className={styles.illustrationImage}
                            mode='widthFix'
                            lazyLoad
                            onError={handleImageError}
                        />
                    ) : (
                        <View className={styles.placeholderImage}>
                            <Text className={styles.placeholderText}>说明图</Text>
                        </View>
                    )}
                </ScrollView>
            </View>

            {/* 底部区域 */}
            <View className={styles.fixedBottom}>
                {showPrivacy && (
                    <View className={styles.privacyNote}>
                        {useAgreementPrivacy ? (
                            <AgreementPrivacy />
                        ) : (
                            <View className={styles.privacyContent}>
                                <Text className={styles.privacyIcon}>🔒</Text>
                                <Text className={styles.privacyText}>{privacyText}</Text>
                                <Text className={styles.privacyLink} onClick={handlePrivacyClick}>
                                    {privacyLinkText}
                                </Text>
                            </View>
                        )}
                    </View>
                )}

                <View className={styles.buttonGroup}>
                    <Button
                        className={cx(styles.confirmButton, {
                            [styles.loading]: isLoading || loading
                        })}
                        size='large'
                        shape='round'
                        onClick={handleConfirm}
                        disabled={isLoading || loading}
                        loading={isLoading || loading}
                    >
                        {isLoading || loading ? '处理中...' : buttonText}
                    </Button>
                </View>
            </View>
        </View>
    );
};

export default HealthManagementGuidePopup;
