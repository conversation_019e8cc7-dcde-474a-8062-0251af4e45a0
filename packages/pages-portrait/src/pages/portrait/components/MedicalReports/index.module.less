.medicalReportsBody {
    .medicalReportsGroups {
        position: relative;
        margin: 0 36px;
        padding: 60px 45px 0;
        border-radius: 63px;
        background-color: #fff;
        box-shadow: 0 0 20px 0 #00000014;

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 36px;
            width: 18px;
            height: 60px;
            background: linear-gradient(
                90deg,
                rgb(255 255 255 / 0%) 0%,
                rgb(255 255 255 / 100%) 100%
            );
            z-index: 2;
        }
    }
}

.medicalReportGroup {
    position: relative;
    padding-left: 52px;
    padding-bottom: 60px;

    &::before {
        content: '';
        position: absolute;
        top: 22.5px;
        bottom: 0;
        left: 0;

        /* prettier-ignore */
        border-left: 1PX dashed #e0e0e0;
        z-index: 1;
    }

    .medicalReportGroupHeader {
        position: relative;
        margin-bottom: 45px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 45px;
        line-height: 45px;
        letter-spacing: 0%;

        .medicalReportGroupHeaderDot {
            position: absolute;
            top: 50%;
            left: -52px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background: rgb(0 200 200 / 20%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 3;
        }

        .medicalReportGroupHeaderDotInner {
            width: 24px;
            height: 24px;
            background: #00c8c8;
            border-radius: 50%;
        }
    }

    .medicalReportGroupBody {
        padding: 60px 45px;
        border-radius: 63px;
        background: #f5f6fa;
    }
}

.medicalReportItem {
    display: flex;
    justify-content: flex-start;

    &:last-child {
        padding-bottom: 0;
        border-bottom: none;
    }

    .reportImage {
        flex-shrink: 0; /* 防止图片被压缩 */
        width: 216px;
        height: 216px;
        border-radius: 45px;
        margin-right: 36px;
    }

    .reportTextInfo {
        flex: 1;
        width: 640px;
    }

    .reportTags {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        height: 69px;
        font-size: 0;
    }

    .reportTag {
        padding: 15px 21px;
        background: #0000000d;
        margin-right: 18px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 39px;
        line-height: 39px;
        color: #272933;
        border-radius: 18px;
    }

    .reportDate {
        font-family: PingFang SC;
        font-size: 42px;
        line-height: 42px;
        color: #272933;
        margin-bottom: 30px;
    }

    .reportHospital {
        font-family: PingFang SC;
        font-size: 42px;
        line-height: 42px;
        color: #272933;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }
}

.medicalReportItemDivider {
    padding: 0 45px;
    margin: 60px 0;
    border-bottom: 1.5px solid #e0e0e0;
}

.medicalReportsEmpty {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    opacity: 1;

    &.isDataFetching,
    &.isPageRending {
        opacity: 0;
    }

    .medicalReportsEmptyIcon {
        margin-top: 24px;
        width: 330px;
        height: 330px;
    }

    .filterResultEmptyIcon {
        margin-top: 165px;
    }

    .medicalReportsEmptyTitle {
        margin-top: 48px;
        font-size: 48px;
        line-height: 48px;
        color: #272933;
    }

    .medicalReportsEmptyDesc {
        margin-top: 36px;
        font-weight: 400;
        font-size: 39px;
        line-height: 39px;
        letter-spacing: 0%;
        color: #848691;
    }

    .medicalReportsEmptyBtn {
        margin-top: 54px;
    }
}

.medicalReportsLoadingGif {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
}
