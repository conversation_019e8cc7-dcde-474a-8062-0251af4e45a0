import React, {memo, useCallback, useEffect, useMemo} from 'react';
import {useAtomValue} from 'jotai';
import dayjs from 'dayjs';
import classNames from 'classnames';
import {WImage} from '@baidu/wz-taro-tools-core';
import {View, Text} from '@tarojs/components';
import {HImage} from '@baidu/health-ui';
import {navigate} from '../../../../../../pages-im/src/utils/basicAbility/commonNavigate';
import {
    usePatientData,
    useMedicalReportsFetch,
    FETCH_STAGE_FETCHING
} from '../../../../hooks/portrait/usePatientData';
import {PAGE_RENDER_STAGE_RENDERED, pageRenderStageAtom} from '../../../../store/portrait';
import {DEFAULT_DISPLAY_TEXT, PATIENT_ID_DEFAULT} from '../../../../constants/portrait';
import BtnAddMedicalReport from '../BtnAddMedicalReport';
import ScrollAnchor from '../ScrollAnchor';
import {SCROLL_CONTAINER_ID} from '../../constants';
import {EMPTY_LIST_DEFAULT, GIF_LOADING_GRAY} from '../../../../constants/images';
import {REPORT_DETAIL_URL} from '../../../../constants/links';

import styles from './index.module.less';

const MedicalReports: React.FC<{
    onReportAddBtnClick: () => void;
    reportTags: string[];
}> = ({onReportAddBtnClick, reportTags}) => {
    const pageRenderStage = useAtomValue(pageRenderStageAtom);
    const {selectedPatientId, sortedReports, reportTagOptions} = usePatientData();
    const {fetchMedicalReports, hasMore, fetchStage} = useMedicalReportsFetch();
    const isPageRendered = useMemo(() => {
        return pageRenderStage === PAGE_RENDER_STAGE_RENDERED;
    }, [pageRenderStage]);
    const filterResultEmpty = useMemo(() => {
        return !sortedReports.length && reportTags.length > 0;
    }, [sortedReports, reportTags]);

    // code → text 字典
    const tagDict = useMemo(
        () => Object.fromEntries(reportTagOptions.slice(1).map(t => [t.value, t.text])),
        [reportTagOptions]
    );

    const handleReportAddBtnClick = useCallback(() => {
        onReportAddBtnClick?.();
    }, [onReportAddBtnClick]);

    const handelJumpToReportDetail = (medicalReportId: string) => {
        const reportId = encodeURIComponent(medicalReportId || '');
        const patientId = encodeURIComponent(selectedPatientId || '');
        const query = `reportId=${reportId}&patientId=${patientId}`;

        navigate({
            url: `${REPORT_DETAIL_URL}?${query}`,
            openType: 'navigate'
        });
    };

    const handleScrollAnchorVisible = useCallback(
        (visible: boolean) => {
            if (visible) {
                fetchMedicalReports({
                    patientId: selectedPatientId ?? '',
                    reportTags
                });
            }
        },
        [fetchMedicalReports, selectedPatientId, reportTags]
    );

    useEffect(() => {
        // 如果页面未渲染，这时还不知道当前就诊人数据，所以不请求接口
        if (!isPageRendered) {
            return;
        }
        // 如果是前端Mock的患者数据，则不请求接口
        if (selectedPatientId === PATIENT_ID_DEFAULT) {
            return;
        }

        fetchMedicalReports(
            {
                patientId: selectedPatientId ?? '',
                reportTags
            },
            {isReset: true}
        );
    }, [fetchMedicalReports, selectedPatientId, reportTags, isPageRendered]);

    // // 如果页面未渲染，则不渲染
    // if (!isPageRendered) {
    //     return null;
    // }

    // 如果就诊人数据为空，或者就诊人数据为前端Mock的数据，则渲染空状态
    if (!sortedReports.length || selectedPatientId === PATIENT_ID_DEFAULT) {
        return (
            <View
                className={classNames(
                    styles.medicalReportsEmpty,
                    !isPageRendered && styles.isPageRending,
                    fetchStage === FETCH_STAGE_FETCHING && styles.isDataFetching
                )}
            >
                <HImage
                    src={EMPTY_LIST_DEFAULT}
                    width={330}
                    height={330}
                    className={classNames(
                        styles.medicalReportsEmptyIcon,
                        filterResultEmpty && styles.filterResultEmptyIcon
                    )}
                />
                {!filterResultEmpty && (
                    <Text className={styles.medicalReportsEmptyTitle}>
                        上传就医资料，管家帮你管理
                    </Text>
                )}
                <Text className={styles.medicalReportsEmptyDesc}>
                    {filterResultEmpty ? '暂无相关结果' : '影像报告、就诊病历、体检结果放心交给我'}
                </Text>
                {!filterResultEmpty && (
                    <BtnAddMedicalReport
                        onReportAddBtnClick={handleReportAddBtnClick}
                        size='md'
                        className={styles.medicalReportsEmptyBtn}
                    />
                )}
            </View>
        );
    }

    return (
        <View className={styles.medicalReports}>
            <View className={styles.medicalReportsBody}>
                {sortedReports.length > 0 ? (
                    <View className={styles.medicalReportsGroups}>
                        {sortedReports.map(reportsGroup => {
                            return (
                                <View
                                    key={reportsGroup[0].createTime}
                                    className={styles.medicalReportGroup}
                                >
                                    <View className={styles.medicalReportGroupHeader}>
                                        <Text className={styles.medicalReportGroupTitleText}>
                                            {dayjs(reportsGroup[0].createTime * 1000).format(
                                                'YYYY-MM-DD'
                                            )}
                                        </Text>
                                        <View className={styles.medicalReportGroupHeaderDot}>
                                            <View
                                                className={styles.medicalReportGroupHeaderDotInner}
                                            ></View>
                                        </View>
                                    </View>
                                    <View className={styles.medicalReportGroupBody}>
                                        {reportsGroup.map((report, index) => {
                                            return (
                                                <>
                                                    <View
                                                        className={styles.medicalReportItem}
                                                        key={report.medicalReportId}
                                                        onClick={() =>
                                                            handelJumpToReportDetail(
                                                                report.medicalReportId
                                                            )
                                                        }
                                                    >
                                                        <WImage
                                                            src={report.images[0].thumb}
                                                            className={styles.reportImage}
                                                        />
                                                        <View className={styles.reportTextInfo}>
                                                            <View className={styles.reportTags}>
                                                                {report.reportTags?.map?.(code => (
                                                                    <Text
                                                                        key={code}
                                                                        className={styles.reportTag}
                                                                    >
                                                                        {tagDict[code] ?? code}
                                                                    </Text>
                                                                ))}
                                                            </View>
                                                            <View className={styles.reportDate}>
                                                                <Text>
                                                                    就诊时间:{' '}
                                                                    {report.reportTime
                                                                        ? report.reportTime
                                                                        : DEFAULT_DISPLAY_TEXT}
                                                                </Text>
                                                            </View>
                                                            <View
                                                                className={classNames(
                                                                    styles.reportHospital
                                                                )}
                                                            >
                                                                <Text className='wz-taro-ellipsis'>
                                                                    {report.hospitalName
                                                                        ? report.hospitalName
                                                                        : `就诊医院: ${DEFAULT_DISPLAY_TEXT}`}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </View>
                                                    {index !== reportsGroup.length - 1 && (
                                                        <View
                                                            className={
                                                                styles.medicalReportItemDivider
                                                            }
                                                        ></View>
                                                    )}
                                                </>
                                            );
                                        })}
                                    </View>
                                </View>
                            );
                        })}
                    </View>
                ) : null}
            </View>
            {hasMore && (
                <ScrollAnchor
                    swanRelativeSelector={`#${SCROLL_CONTAINER_ID}`}
                    onAnchorVisibleChange={handleScrollAnchorVisible}
                >
                    <View className={styles.medicalReportsLoadingGif}>
                        <HImage src={GIF_LOADING_GRAY} width={200} height={100} />
                    </View>
                </ScrollAnchor>
            )}
        </View>
    );
};

export default memo(MedicalReports);
