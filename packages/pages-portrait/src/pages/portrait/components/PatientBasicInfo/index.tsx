import React, {memo, useCallback, useMemo} from 'react';
import {View, Text} from '@tarojs/components';
import {HIcon} from '@baidu/health-ui';
import classNames from 'classnames';
import {ubcCommonClkSend} from '../../../../../../pages-im/src/utils/generalFunction/ubc';
import {usePatientData, usePatientDataFetch} from '../../../../hooks/portrait/usePatientData';
import {CLoginButton} from '../../../../../../ui-cards-common';
import {PATIENT_NAME_DISPLAY_MAX_LENGTH, UBC_FIELD_PAGE} from '../../constants';
import {DEFAULT_DISPLAY_TEXT} from '../../../../constants/portrait';
import styles from './index.module.less';

const PatientBasicInfo: React.FC<{onPatientInfoEditBtnClick: () => void}> = ({
    onPatientInfoEditBtnClick
}) => {
    const {isLogin, refreshPatientData} = usePatientDataFetch();
    const {basicInfo} = usePatientData();
    const displayPatientName = useMemo(() => {
        const name = basicInfo?.name;
        if (!name) return DEFAULT_DISPLAY_TEXT;
        return name.length > PATIENT_NAME_DISPLAY_MAX_LENGTH
            ? name.slice(0, PATIENT_NAME_DISPLAY_MAX_LENGTH) + '...'
            : name;
    }, [basicInfo?.name]);
    const displayHeight = useMemo(() => {
        return basicInfo?.height ? basicInfo.height / 10 : DEFAULT_DISPLAY_TEXT;
    }, [basicInfo?.height]);
    const displayWeight = useMemo(() => {
        return basicInfo?.weight ? basicInfo.weight / 1000 : DEFAULT_DISPLAY_TEXT;
    }, [basicInfo?.weight]);

    const handlePatientInfoEditBtnClick = useCallback(() => {
        if (!isLogin) {
            refreshPatientData();
        }
        onPatientInfoEditBtnClick?.();
        // ubc打点逻辑
        ubcCommonClkSend({
            value: 'patientBasicInfoEditBtn',
            page: UBC_FIELD_PAGE
        });
    }, [onPatientInfoEditBtnClick, isLogin, refreshPatientData]);

    const renderPatientInfoEditBtn = useMemo(() => {
        return (
            <CLoginButton
                className={styles.patientInfoEditBtn}
                isLogin={isLogin}
                closeShowNewUserTag={true}
                useH5CodeLogin={true}
                onLoginFail={error => {
                    console.error('error', error);
                }}
                onLoginSuccess={handlePatientInfoEditBtnClick}
            >
                <View className={styles.patientInfoEditBtnContent}>
                    <View>
                        <Text>完善资料</Text>
                    </View>
                    <View>
                        <HIcon value='wise-right-arrow' className={styles.patientInfoEditBtnIcon} />
                    </View>
                </View>
            </CLoginButton>
        );
    }, [handlePatientInfoEditBtnClick, isLogin]);

    return (
        <View className={styles.patientBasicInfo}>
            <View className={styles.userDetails}>
                <Text className={styles.patientName}>{displayPatientName}</Text>
                <Text className={classNames(styles.detailItem, styles.patientGender)}>
                    {basicInfo?.gender ? basicInfo.gender : DEFAULT_DISPLAY_TEXT}
                </Text>
                <Text className={styles.detailItem}>
                    {basicInfo?.age ? basicInfo.age : DEFAULT_DISPLAY_TEXT}
                </Text>
            </View>
            <View className={styles.patientInfoStats}>
                <View className={classNames(styles.patientBmi, styles.patientInfoStatsItem)}>
                    <Text className={styles.patientBmiValue}>
                        {basicInfo?.bmi ? basicInfo.bmi : DEFAULT_DISPLAY_TEXT}
                    </Text>
                    <Text className={styles.patientBmiUnit}>BMI</Text>
                </View>
                <View className={classNames(styles.patientHeight, styles.patientInfoStatsItem)}>
                    <Text className={styles.patientHeightValue}>{displayHeight}</Text>
                    <Text className={styles.patientHeightUnit}>身高/cm</Text>
                </View>
                <View className={classNames(styles.patientWeight, styles.patientInfoStatsItem)}>
                    <Text className={styles.patientWeightValue}>{displayWeight}</Text>
                    <Text className={styles.patientWeightUnit}>体重/kg</Text>
                </View>
            </View>
            {renderPatientInfoEditBtn}
        </View>
    );
};

export default memo(PatientBasicInfo);
