.patientBasicInfo {
    position: relative;
    padding: 60px 45px;
    height: 393px;
    margin: 0 36px 30px;
    border-radius: 63px;
    border: 1px solid #fff;
    background: linear-gradient(180deg, rgb(255 255 255 / 40%) 0%, #fff 100%);
    box-sizing: border-box;

    .patientName {
        margin-right: 36px;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 60px;
        color: #000311;
    }

    .patientGender {
        padding-right: 24px;
        margin-right: 24px;
        border-right: 1.5px solid #a7b4c5;
    }

    .userDetails {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 30px;
        height: 60px;
    }

    .detailItem {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 45px;
        line-height: 45px;
        letter-spacing: 0;
        color: #000311;
        margin-left: 12px;
    }

    .patientInfoStats {
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-top: 72px;

        .patientInfoStatsItem {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .patientBmi,
        .patientHeight,
        .patientWeight {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            font-family: BaiduNumberPlus;
            font-weight: 500;
            font-size: 57px;
            line-height: 57px;
            letter-spacing: 0;
            text-align: center;
            color: #000311;
        }

        .patientBmiUnit,
        .patientHeightUnit,
        .patientWeightUnit {
            margin-top: 42px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 42px;
            line-height: 42px;
            letter-spacing: 0;
            text-align: center;
            color: #848691;
        }
    }

    .patientInfoEditBtn {
        position: absolute;
        right: 20px;
        top: 20px;
        padding: 25px;
        color: #00c8c8;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 42px;
        background-color: transparent;
    }

    .patientInfoEditBtnContent {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .patientInfoEditBtnIcon {
        font-size: 48px !important;
        color: #00c8c8 !important;
    }
}
