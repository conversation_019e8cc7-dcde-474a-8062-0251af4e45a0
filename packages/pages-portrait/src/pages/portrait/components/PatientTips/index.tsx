import React from 'react';
import {View, Text} from '@tarojs/components';
import {HImage} from '@baidu/health-ui';
import {ICON_TIP_OF_MEDICAL_REPORT} from '../../../../constants/images';

import styles from './index.module.less';

const PatientTips: React.FC<{text: string}> = ({text}) => {
    return (
        <View className={styles.patientTips}>
            <HImage
                className={styles.patientTipsIcon}
                src={ICON_TIP_OF_MEDICAL_REPORT}
                width={102}
                height={96}
            />
            <Text>{text}</Text>
        </View>
    );
};

export default PatientTips;
