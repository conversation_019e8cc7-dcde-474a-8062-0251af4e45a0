import React, {memo, useCallback, useEffect, useRef} from 'react';
import {
    eventCenter,
    getCurrentInstance,
    createIntersectionObserver,
    type PageInstance
} from '@tarojs/taro';
import {View} from '@tarojs/components';
import cx from 'classnames';
import {debounce} from 'lodash-es';
import {uuid} from '@baidu/vita-utils-shared';

import styles from './index.module.less';

const ZeroWidthNoBreakSpace = '\uFEFF';
const SCROLL_ANCHOR_ID = `js-scroll-anchor-${uuid(4)}`;
const SCROLL_ANCHOR_VISIBLE_EVENT = 'scrollAnchor:visible';

const ScrollAnchor = ({
    onAnchorVisibleChange,
    swanRelativeSelector,
    className,
    children
}: {
    swanRelativeSelector?: string;
    className?: string;
    onAnchorVisibleChange?: (visible: boolean) => void;
    children?: React.ReactNode;
}) => {
    const {observe, disconnect} = useIntersectionObserver({swanRelativeSelector});

    useEffect(() => {
        observe(`#${SCROLL_ANCHOR_ID}`);
        return () => {
            disconnect();
        };
    }, [observe, disconnect]);

    useEffect(() => {
        const debouncedScrollAnchorVisibleChange = debounce(
            (visible: boolean) => {
                onAnchorVisibleChange?.(visible);
            },
            300,
            {leading: false, trailing: true}
        );
        const handleScrollAnchorVisible = (scrollAnchorVisible: boolean) => {
            debouncedScrollAnchorVisibleChange(scrollAnchorVisible);
        };
        eventCenter.on(SCROLL_ANCHOR_VISIBLE_EVENT, handleScrollAnchorVisible);
        return () => {
            eventCenter.off(SCROLL_ANCHOR_VISIBLE_EVENT, handleScrollAnchorVisible);
        };
    }, [onAnchorVisibleChange]);

    return (
        <View id={SCROLL_ANCHOR_ID} className={cx(styles.scrollAnchor, className)}>
            {children || ZeroWidthNoBreakSpace}
        </View>
    );
};

function useIntersectionObserver({swanRelativeSelector}: {swanRelativeSelector?: string}) {
    const observerSwan = useRef<any>(null);
    const observerH5 = useRef<IntersectionObserver | null>(null);

    const handleIntersectionH5 = useCallback((entries: IntersectionObserverEntry[]) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                eventCenter.trigger(SCROLL_ANCHOR_VISIBLE_EVENT, true);
            } else {
                eventCenter.trigger(SCROLL_ANCHOR_VISIBLE_EVENT, false);
            }
        });
    }, []);

    const handleIntersectionSwan = useCallback((entry: IntersectionObserverEntry) => {
        const visibleRange = 0.5;
        if (entry.intersectionRatio > visibleRange) {
            eventCenter.trigger(SCROLL_ANCHOR_VISIBLE_EVENT, true);
        } else if (entry.intersectionRatio <= visibleRange) {
            eventCenter.trigger(SCROLL_ANCHOR_VISIBLE_EVENT, false);
        }
    }, []);

    const observe = useCallback(
        (selector: string) => {
            if (process.env.TARO_ENV === 'swan') {
                if (!observerSwan.current) {
                    observerSwan.current = createIntersectionObserver(
                        getCurrentInstance().page as PageInstance,
                        {
                            thresholds: [0, 0.5, 1],
                            observeAll: true
                        }
                    );
                }
                if (swanRelativeSelector) {
                    observerSwan.current
                        .relativeTo(swanRelativeSelector)
                        .observe(selector, handleIntersectionSwan);
                } else {
                    observerSwan.current
                        .relativeToViewport()
                        .observe(selector, handleIntersectionSwan);
                }
            } else if (process.env.TARO_ENV === 'h5') {
                if (!observerH5.current) {
                    observerH5.current = new IntersectionObserver(handleIntersectionH5, {
                        threshold: [0, 1],
                        rootMargin: '50px'
                    });
                }
                observerH5.current.observe(document.querySelector(selector) as Element);
            }
        },
        [handleIntersectionH5, handleIntersectionSwan, swanRelativeSelector]
    );

    const disconnect = useCallback(() => {
        if (process.env.TARO_ENV === 'swan' && observerSwan.current) {
            observerSwan.current.disconnect();
        } else if (process.env.TARO_ENV === 'h5' && observerH5.current) {
            observerH5.current.disconnect();
        }
    }, []);

    return {observe, disconnect};
}

export default memo(ScrollAnchor);
