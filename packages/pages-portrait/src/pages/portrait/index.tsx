import React, {useEffect, memo, useMemo, useCallback, useState, useRef} from 'react';
import {nextTick} from '@tarojs/taro';
import {View, Text, CommonEvent, ScrollView} from '@tarojs/components';
import {HTabs} from '@baidu/health-ui';
import {useAtomValue, useSetAtom} from 'jotai';
import {CLoginButton} from '../../../../ui-cards-common';
import CPageContainer from '../../../../pages-im/src/components/CPageContainer';
import {createErrorBoundary} from '../../../../pages-im/src/components/ErrorBoundary/ErrorBoundaryHoc';
import {useGetSwanMsgListSceneStatus} from '../../../../pages-im/src/hooks/triageStream/useGetSwanMsgListSceneStatus';
import {useRetentionDialog} from '../../../../pages-im/src/hooks/useRetentionDialog';
import {
    ubcCommonClkSend,
    ubcCommonViewSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';
import {usePatientData, usePatientDataFetch} from '../../hooks/portrait/usePatientData';
import {
    titleAtom,
    patientInfoTabIndexAtom,
    PATIENT_INFO_TAB_INDEX_DEFAULT,
    PATIENT_INFO_TAB_INDEX_HEALTH_HISTORY,
    historyPatientsAtom,
    historyPatientsPopupLastCloseTimeSetAtom,
    PAGE_RENDER_STAGE_RENDERED
} from '../../store/portrait';
import {
    setPatientInfoTabToMedicalReportsAtom,
    setPatientInfoTabToHealthHistoryAtom
} from '../../store/portrait/actionAtoms';
import {
    historyPatientsPopupAutoOpenAtom,
    portraitIntroPopupVisibleOnceAtom
} from '../../store/portrait/derivedAtoms';
import {usePopup} from '../../hooks/portrait/useSyncHistoryPatientPopup';
import {useImageUpload} from '../../hooks/portrait/useImageUpload';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';
import ReportUploadPopup from '../../components/PopupReportUpload';
import {BG_OF_PORTRAIT_PAGE} from '../../constants/images';
import {HEALTH_PROFILE_URL} from '../../constants/links';
import {PATIENT_ID_DEFAULT} from '../../constants/portrait';
import AgreementPrivacy from '../../components/AgreementPrivacy';
import PopupManager from './components/PopupManager';
import PopupIntro from './components/PopupIntro';
import PatientTabs from './components/PatientTabs';
import PatientBasicInfo from './components/PatientBasicInfo';
import MedicalReports from './components/MedicalReports';
import HealthHistoryCards from './components/HealthHistory';
import HistoryPatientBar from './components/HistoryPatientBar';
import BtnAddMedicalReport from './components/BtnAddMedicalReport';
import PatientTips from './components/PatientTips';
import Filter, {FilterOption, type FilterRef} from './components/Filter';
import {SCROLL_CONTAINER_ID, PATIENT_INFO_TABS_ID, UBC_FIELD_PAGE} from './constants';

import styles from './index.module.less';

const h5BgColor = `left top / 100% auto no-repeat url("${BG_OF_PORTRAIT_PAGE}")`;
const barBg = process.env.TARO_ENV === 'h5' ? h5BgColor : 'inherit';

const PortraitPage: React.FC = () => {
    const title = useAtomValue(titleAtom);
    const {pageRenderStage, isLogin, refreshPatientData} = usePatientDataFetch(undefined, {
        autoFetch: true
    });
    const {
        selectedPatientId,
        sortedReports,
        healthHistory,
        reportTagOptions,
        relationshipOptions,
        hasSelf
    } = usePatientData();
    const {status: isSwanMsgListScene} = useGetSwanMsgListSceneStatus();
    const {onBackDetain, backDetain} = useRetentionDialog();
    const patientInfoTabIndex = useAtomValue(patientInfoTabIndexAtom);
    const setPatientInfoTabToMedicalReports = useSetAtom(setPatientInfoTabToMedicalReportsAtom);
    const setPatientInfoTabToHealthHistory = useSetAtom(setPatientInfoTabToHealthHistoryAtom);
    const [isFilterOptionsVisible, setIsFilterOptionsVisible] = useState<boolean>(false);
    const [reportTags, setReportTags] = useState<string[]>([]);
    const historyPatientsPopupAutoOpen = useAtomValue(historyPatientsPopupAutoOpenAtom);
    const portraitIntroPopupVisibleOnce = useAtomValue(portraitIntroPopupVisibleOnceAtom);
    const historyPatients = useAtomValue(historyPatientsAtom);
    const {popupOpenStatus, closeUploadPopup, onSelectedPics, openUploadPopup} = useImageUpload();
    const {showHistoryPatientSyncFlow, showCreateMemberPopup} = usePopup();
    const historyPatientsPopupLastCloseTimeSet = useSetAtom(
        historyPatientsPopupLastCloseTimeSetAtom
    );
    const shouldCreatePatientFirst = useMemo(() => {
        return selectedPatientId === PATIENT_ID_DEFAULT;
    }, [selectedPatientId]);
    const [scrollIntoViewId, setScrollIntoViewId] = useState<string | undefined>();
    const [scrollWithAnimationDisabled, setScrollWithAnimationDisabled] = useState<boolean>(false);
    const [patientInfoTabsRerender, setPatientInfoTabsRerender] = useState<boolean>(false);
    const medicalReportsHeaderContentVisible = useMemo(() => {
        return sortedReports.length > 0 || reportTags.length > 0;
    }, [sortedReports.length, reportTags.length]);
    const addMedicalReportBtnVisible = useMemo(() => {
        return (
            (!isFilterOptionsVisible && sortedReports.length > 0) ||
            (!isFilterOptionsVisible && sortedReports.length === 0 && reportTags.length > 0)
        );
    }, [isFilterOptionsVisible, sortedReports.length, reportTags.length]);
    const filterRef = useRef<FilterRef | undefined>();

    const scrollPatientInfoTabsIntoView = useCallback(() => {
        setScrollIntoViewId(PATIENT_INFO_TABS_ID);
        nextTick(() => {
            setScrollIntoViewId(undefined);
        });
    }, []);

    // 显示历史患者选择弹窗
    const openHistoryPatientSelect = useCallback(() => {
        return showHistoryPatientSyncFlow({
            // 可选：预设的历史就诊人数据
            historyPatients: undefined, // 传 undefined 会自动调用接口获取

            // 流程完成回调
            onFlowComplete: () => {
                // 处理成功情况（入参createdPatients 是新创建的患者列表）
            },

            // 流程出错回调
            onFlowError: error => {
                console.error('历史就诊人同步流程出错:', error);
                // 处理错误情况
            },
            allowMultiple: true
        });
    }, [showHistoryPatientSyncFlow]);

    // 带历史就诊人模式
    const handleCreatePatient = useCallback(() => {
        if (historyPatients.length > 0) {
            return showCreateMemberPopup({
                mode: 'withHistory',
                relationshipOptions: relationshipOptions.map(item => ({
                    gender: item.gender,
                    value: item.value,
                    disabled: item.disabled
                })),
                allowMultiple: true
            });
        }
        return showCreateMemberPopup({
            mode: 'simple',
            relationshipOptions: relationshipOptions.map(item => ({
                gender: item.gender,
                value: item.value,
                disabled: item.disabled
            })),
            allowMultiple: true
        });
    }, [historyPatients, relationshipOptions, showCreateMemberPopup]);

    const handlePatientAdd = () => {
        handleCreatePatient();
    };

    const resetAfterPatientTabChanged = () => {
        filterRef.current?.reset();
        setReportTags([]);
        setPatientInfoTabToMedicalReports();
    };

    const handlePatientTabChange = () => {
        resetAfterPatientTabChanged();
    };

    const rerenderPatientInfoTabs = useCallback(() => {
        setPatientInfoTabsRerender(true);
        nextTick(() => {
            setPatientInfoTabsRerender(false);
        });
    }, []);

    const handlePatientInfoTabClick = useCallback(
        (event: CommonEvent, index: number) => {
            if (index === PATIENT_INFO_TAB_INDEX_DEFAULT) {
                setPatientInfoTabToMedicalReports();
                // ubc打点逻辑
                ubcCommonClkSend({
                    value: 'medicalReportTab',
                    page: UBC_FIELD_PAGE
                });
            } else if (index === PATIENT_INFO_TAB_INDEX_HEALTH_HISTORY) {
                // ubc打点逻辑
                ubcCommonClkSend({
                    value: 'healthHistoryTab',
                    page: UBC_FIELD_PAGE
                });
                // 如果当前没有就诊人，需要创建就诊人
                if (shouldCreatePatientFirst) {
                    handleCreatePatient();
                    setTimeout(() => {
                        rerenderPatientInfoTabs();
                    }, 500); // 500ms，在弹窗后偷偷渲染，避免闪烁
                    return;
                }
                setPatientInfoTabToHealthHistory();
            }
        },
        [
            shouldCreatePatientFirst,
            rerenderPatientInfoTabs,
            setPatientInfoTabToMedicalReports,
            setPatientInfoTabToHealthHistory,
            handleCreatePatient
        ]
    );

    const handleReportAddBtnClick = useCallback(() => {
        // 如果当前没有就诊人，需要创建就诊人
        if (shouldCreatePatientFirst) {
            handleCreatePatient();
            return;
        }
        openUploadPopup?.();
    }, [openUploadPopup, shouldCreatePatientFirst, handleCreatePatient]);

    const handleToHistoryPatientPage = () => {
        // 点击去查看，拉起「批量同步引导弹窗」
        openHistoryPatientSelect();
    };

    const handlePatientInfoEditBtnClick = useCallback(() => {
        // 如果当前没有就诊人，需要创建就诊人
        if (shouldCreatePatientFirst) {
            handleCreatePatient();
            return;
        }
        navigate({
            url: `${HEALTH_PROFILE_URL}?patientId=${selectedPatientId}&hasSelf=${hasSelf}`,
            openType: 'navigate'
        });
    }, [shouldCreatePatientFirst, selectedPatientId, hasSelf, handleCreatePatient]);

    const filterIntercepterBeforeClick = useCallback(() => {
        setScrollWithAnimationDisabled(true);
        scrollPatientInfoTabsIntoView();
        // ubc打点逻辑
        ubcCommonClkSend({
            value: 'medicalReportFilter_btn',
            page: UBC_FIELD_PAGE
        });
        return true;
    }, [scrollPatientInfoTabsIntoView]);

    const handleFilterConfirm = useCallback((option?: FilterOption | null) => {
        setReportTags(option?.value ? [option.value] : []);
        // ubc打点逻辑
        ubcCommonClkSend({
            value: 'medicalReportFilter_confirmBtn',
            page: UBC_FIELD_PAGE
        });
    }, []);

    const handleFilterOptionsVisibleChange = useCallback((visible: boolean) => {
        setScrollWithAnimationDisabled(visible);
        setIsFilterOptionsVisible(visible);
    }, []);

    const renderMedicalReports = useMemo(() => {
        if (patientInfoTabIndex !== PATIENT_INFO_TAB_INDEX_DEFAULT) {
            return null;
        }

        return (
            <>
                {sortedReports.length > 0 && (
                    <PatientTips text='上传病历、检查单等就医资料，大白帮您管理' />
                )}
                <MedicalReports
                    reportTags={reportTags}
                    onReportAddBtnClick={handleReportAddBtnClick}
                />
                {sortedReports.length > 0 && (
                    <View className={styles.agreementPrivacyContainer}>
                        <AgreementPrivacy />
                    </View>
                )}
            </>
        );
    }, [patientInfoTabIndex, handleReportAddBtnClick, reportTags, sortedReports.length]);

    const renderHealthHistory = useMemo(() => {
        if (patientInfoTabIndex !== PATIENT_INFO_TAB_INDEX_HEALTH_HISTORY) {
            return null;
        }
        if (!healthHistory) {
            return <Text>暂无健康史</Text>;
        }
        return (
            <View>
                <PatientTips text='完善健康史信息，以获得专属健康建议' />
                <HealthHistoryCards patientId={selectedPatientId || ''} />
                <View className={styles.agreementPrivacyContainer}>
                    <AgreementPrivacy />
                </View>
            </View>
        );
    }, [healthHistory, patientInfoTabIndex, selectedPatientId]);

    const renderPatientInfoTabs = useMemo(() => {
        return (
            <HTabs
                className={styles.patientInfoTabs}
                selectedIndex={patientInfoTabIndex}
                onTabClick={handlePatientInfoTabClick}
                activatedType='line'
                activeLineMode='fixed'
            >
                <HTabs.TabPane title='就诊资料夹' />
                <HTabs.TabPane title='健康史' />
            </HTabs>
        );
    }, [patientInfoTabIndex, handlePatientInfoTabClick]);

    useEffect(() => {
        if (process.env.TARO_ENV === 'h5') {
            document.title = title;
        }
    }, [title]);

    // 展现打点
    useEffect(() => {
        ubcCommonViewSend({
            value: 'page',
            page: UBC_FIELD_PAGE
        });
    }, []);

    useEffect(() => {
        if (pageRenderStage !== PAGE_RENDER_STAGE_RENDERED) {
            return;
        }

        // 如果档案功能介绍弹窗展示状态为true，且档案功能介绍弹窗未被关闭过，则展示档案功能介绍弹窗
        if (portraitIntroPopupVisibleOnce) {
            return;
        }
        if (historyPatientsPopupAutoOpen) {
            openHistoryPatientSelect();
            historyPatientsPopupLastCloseTimeSet(String(Date.now()));
        }
    }, [
        historyPatientsPopupAutoOpen,
        openHistoryPatientSelect,
        historyPatientsPopupLastCloseTimeSet,
        pageRenderStage,
        portraitIntroPopupVisibleOnce
    ]);

    return (
        <>
            <CPageContainer
                onBackDetain={onBackDetain}
                backDetain={backDetain}
                topBarProps={{
                    title: ' ',
                    className: 'white',
                    textColor: '#000311',
                    barBg: barBg,
                    blank: true,
                    hideHome: true,
                    titleLeftSlot: title,
                    // 手百中心入口进来的隐藏返回按钮 @zhangzhiyu03
                    ...(isSwanMsgListScene ? {hideBack: true, isInTabBar: true} : {})
                }}
            >
                <View className={styles.pageContent}>
                    {/* 患者切换Tabs */}
                    <PatientTabs
                        onAddPatient={handlePatientAdd}
                        onChange={handlePatientTabChange}
                    />
                    {/* 滚动容器 */}
                    <View id={SCROLL_CONTAINER_ID} className={styles.scrollContainer}>
                        <ScrollView
                            scrollY
                            className={styles.scrollView}
                            scrollWithAnimation={!scrollWithAnimationDisabled}
                            scrollIntoView={scrollIntoViewId}
                        >
                            {/* 用户基本信息 */}
                            <View className={styles.patientInfoHeader}>
                                <PatientBasicInfo
                                    onPatientInfoEditBtnClick={handlePatientInfoEditBtnClick}
                                />
                            </View>
                            <HistoryPatientBar onBtnClick={handleToHistoryPatientPage} />
                            <View id={PATIENT_INFO_TABS_ID} className={styles.patientInfoBody}>
                                {/* 就诊资料夹&健康史Tab */}
                                <View className={styles.patientInfoTabsContainer}>
                                    <View className={styles.patientInfoTabsWrapper}>
                                        {/* FIXME HTabs 组件不支持修改selectedIndex状态切换Tab，所以使用重新渲染的方式 */}
                                        {/* FIXME CLoginButton 组件包裹 HTabs会导致该组件失效，所以这里用冗余方式实现 */}
                                        {!patientInfoTabsRerender &&
                                            (!isLogin ? (
                                                <CLoginButton
                                                    isLogin={isLogin}
                                                    closeShowNewUserTag={true}
                                                    useH5CodeLogin={true}
                                                    onLoginFail={error => {
                                                        console.error('error', error);
                                                    }}
                                                    onLoginSuccess={() => {
                                                        refreshPatientData();
                                                        setPatientInfoTabToMedicalReports();
                                                    }}
                                                >
                                                    {renderPatientInfoTabs}
                                                </CLoginButton>
                                            ) : (
                                                renderPatientInfoTabs
                                            ))}
                                    </View>
                                    {patientInfoTabIndex === PATIENT_INFO_TAB_INDEX_DEFAULT && (
                                        <View className={styles.medicalReportsHeader}>
                                            {medicalReportsHeaderContentVisible ? (
                                                <>
                                                    {/* FIXME CLoginButton 组件包裹 Filter 会导致该组件失效，所以这里用冗余方式实现 */}
                                                    {!isLogin ? (
                                                        <CLoginButton
                                                            isLogin={isLogin}
                                                            closeShowNewUserTag={true}
                                                            useH5CodeLogin={true}
                                                            onLoginFail={error => {
                                                                console.error('error', error);
                                                            }}
                                                            onLoginSuccess={() => {
                                                                refreshPatientData();
                                                            }}
                                                        >
                                                            <Filter
                                                                ref={filterRef}
                                                                options={reportTagOptions}
                                                                interceptorBeforeClick={
                                                                    filterIntercepterBeforeClick
                                                                }
                                                                onConfirm={handleFilterConfirm}
                                                                onReset={handleFilterConfirm}
                                                                onOptionsVisibleChange={
                                                                    handleFilterOptionsVisibleChange
                                                                }
                                                            />
                                                        </CLoginButton>
                                                    ) : (
                                                        <Filter
                                                            ref={filterRef}
                                                            options={reportTagOptions}
                                                            interceptorBeforeClick={
                                                                filterIntercepterBeforeClick
                                                            }
                                                            onConfirm={handleFilterConfirm}
                                                            onReset={handleFilterConfirm}
                                                            onOptionsVisibleChange={
                                                                handleFilterOptionsVisibleChange
                                                            }
                                                        />
                                                    )}
                                                    {addMedicalReportBtnVisible && (
                                                        <BtnAddMedicalReport
                                                            onReportAddBtnClick={
                                                                handleReportAddBtnClick
                                                            }
                                                            size='sm'
                                                        />
                                                    )}
                                                </>
                                            ) : null}
                                        </View>
                                    )}
                                </View>
                                {/* 就诊资料夹 */}
                                {renderMedicalReports}
                                {/* 健康史统计 */}
                                {renderHealthHistory}
                            </View>
                        </ScrollView>
                    </View>
                </View>
            </CPageContainer>
            {/* 所有的全局弹窗，统一在这里维护 */}

            {/* 对信息同步所有popup进行管理 */}
            <PopupManager />

            {/* 档案功能介绍弹窗 */}
            <PopupIntro />

            <ReportUploadPopup
                open={popupOpenStatus}
                sceneType='portraitUpload'
                onSelectedPics={onSelectedPics}
                closeUploadPopup={closeUploadPopup}
            />
        </>
    );
};

export default createErrorBoundary(memo(PortraitPage));
