import {uuid} from '@baidu/vita-utils-shared';
export const SCROLL_CONTAINER_ID = `js-scroll-container-${uuid(4)}`;
export const PATIENT_INFO_TABS_ID = `js-patient-info-tabs-${uuid(4)}`;

// 患者姓名展示最大长度
export const PATIENT_NAME_DISPLAY_MAX_LENGTH = 5;

// 业务图片链接
// 患者标签页背景
export const BG_OF_PATIENT_TAB_ACTIVE =
    'https://med-fe.cdn.bcebos.com/vita/portrait/bg-patient-tab-active.png';

// 筛选标签默认文案
export const FILTER_LABEL_DEFAULT = '筛选';

// ubc打点档案主页page字段取值
export const UBC_FIELD_PAGE = 'vita/pages/im/portrait/index';

// ubc打点档案上传page字段取值
export const UBC_FIELD_UPLOAD_PAGE = '/vita/pages/reportUpload/index';

// ubc打点档案详情page字段取值
export const UBC_FIELD_DETAIL_PAGE = '/vita/pages/reportDetail/index';
