.pageContent {
    display: flex;
    flex-direction: column;
    height: 100%;

    ::-webkit-scrollbar {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
        background-color: transparent !important;
    }
}

.scrollContainer {
    flex: 1;
    height: 100%;
    overflow: hidden;
}

.scrollView {
    height: 100%;
}

.patientInfoHeader {
    background: transparent;
}

.patientInfoBody {
    background: #fafbff;
    border-top-left-radius: 63px;
    border-top-right-radius: 63px;
    min-height: calc(100% - 423px);
    padding-bottom: 72px;

    .patientInfoTabsContainer {
        position: sticky;
        top: 0;
        z-index: 100;
        padding-top: 24px;
        padding-bottom: 30px;
        border-top-left-radius: 63px;
        border-top-right-radius: 63px;
        background: #fafbff;
    }

    .patientInfoTabs {
        :global(.h-tabs-list-scroll_wrap) {
            height: 114px;
        }

        :global(.h-tabs-list-scroll) {
            margin: 0 135px;
        }

        :global(.h-tabs-list-content) {
            font-family: PingFang SC;
            padding-top: 30px;
            padding-bottom: 30px;
            color: #848691 !important;
            font-size: 54px !important;
            font-weight: 400 !important;
            line-height: 54px !important;
        }

        :global(.h-tabs-list-content_active) {
            color: #272933 !important;
            font-weight: 500 !important;
        }

        :global(.h-tabs-list-content__item-underline) {
            height: 12px;
            border-radius: 6px;
        }
    }

    .medicalReportsHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 30px 51px 0;
        height: 96px;
        font-size: 0;

        .filterContainer {
            position: relative;
        }
    }

    .safeArea {
        background: transparent !important;
    }
}

.agreementPrivacyContainer {
    margin-top: 72px;
}
