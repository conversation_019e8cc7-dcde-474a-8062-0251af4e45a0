import React, {useState, useEffect} from 'react';
import Taro, {showToast, navigateBack} from '@tarojs/taro';
import {View} from '@tarojs/components';
import {Portal} from '@baidu/vita-ui-cards-common';
import {Dialog, Button} from '@baidu/wz-taro-tools-core';
import CPageContainer from '../../../../pages-im/src/components/CPageContainer';
import {useRetentionDialog} from '../../../../pages-im/src/hooks/useRetentionDialog';
import {useGetUrlParams} from '../../../../pages-im/src/hooks/common';
import {useGetSwanMsgListSceneStatus} from '../../../../pages-im/src/hooks/triageStream/useGetSwanMsgListSceneStatus';
import {usePatientDataFetch} from '../../hooks/portrait/usePatientData';
import {
    ubcCommonViewSend,
    ubcCommonClkSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';
import {
    deletePatient,
    getPatientInfo,
    updatePatientInfo,
    UpdatePatientRequest
} from '../../models/services/portrait/healthProfile';
import HealthProfileForm, {HealthProfileData} from './components/HealthProfileForm';
import style from './index.module.less';

const barBg = '#EFF3F9';

// 按钮文本常量
const BUTTON_TEXT = {
    DELETE_WARNING: '确定要删除该就诊人资料吗？',
    CANCEL: '取消',
    CONFIRM_DELETE: '确认删除'
};

const HealthProfile: React.FC = () => {
    const {patientId, hasSelf} = useGetUrlParams();
    const [initialData, setInitialData] = useState<Partial<HealthProfileData>>({});
    const [formLoading, setFormLoading] = useState(false);
    const [pickerOptions, setPickerOptions] = useState<any>({}); // 存储接口返回的选项数据
    const {onBackDetain, backDetain} = useRetentionDialog();
    const {status: isSwanMsgListScene} = useGetSwanMsgListSceneStatus();
    const [isDeleteConfirmShow, setIsDeleteConfirmShow] = useState(false);

    const {refreshPatientData} = usePatientDataFetch();

    useEffect(() => {
        if (patientId) {
            fetchPatientInfo(patientId);
            ubcCommonViewSend({
                value: 'page'
            });
        }
    }, []);

    const convertMmToCm = (mm: number): number => {
        return Number((mm / 10).toFixed(1));
    };

    const convertGToKg = (g: number): number => {
        return Number((g / 1000).toFixed(3));
    };

    const convertCmToMm = (cm: number): number => {
        return Math.round(cm * 10);
    };

    const convertKgToG = (kg: number): number => {
        return Math.round(kg * 1000);
    };

    // 获取就诊人信息
    const fetchPatientInfo = async (id: string) => {
        setFormLoading(true);
        try {
            const res = await getPatientInfo(id, hasSelf);
            const {data} = res;
            const {patient, options} = data;
            setPickerOptions(options);

            // 转换接口数据为表单所需格式
            const formData: Partial<HealthProfileData> = {
                relation: patient.relationship,
                realName: patient.name,
                gender: patient.gender,
                birthDate: patient.birthDate,
                // // 后端返回的是mm/g，转换为cm/kg显示
                height: patient.height ? convertMmToCm(patient.height) : undefined,
                weight: patient.weight ? convertGToKg(patient.weight) : undefined,
                // 地区数据处理 - 如果area是数组则直接使用，如果是字符串则分割
                residenceCity: Array.isArray(patient.area)
                    ? patient.area
                    : patient.area
                        ? patient.area.split(',')
                        : [],
                residenceAddress: patient.address,
                maritalStatus: patient.maritalStatus,
                educationLevel: patient.educationLevel,
                // 权限控制字段
                isCertified: patient.isCertified,
                canEditBasicInfo: patient.canEditBasicInfo,
                canEditRelationship: patient.canEditRelationship
            };

            setInitialData(formData);
        } catch (error) {
            showToast({
                title: error?.[0]?.toast || '获取就诊信息失败',
                icon: 'none'
            });
        } finally {
            setFormLoading(false);
        }
    };

    // 数值验证
    const validateNumericInput = (
        value: number | string | undefined,
        fieldName: string
    ): string | null => {
        if (value === '' || value === undefined || value === null) return null;

        // 检查是否为有效数字
        if (isNaN(value) || !isFinite(value)) {
            return `${fieldName}必须是有效数字`;
        }

        // 检查是否小于等于0
        if (value <= 0) {
            return `${fieldName}必须大于0`;
        }
        return null;
    };

    // 保存健康档案 - 增量更新
    const handleSaveHealthProfile = async (
        healthProfileData: HealthProfileData,
        changedFields: string[]
    ) => {
        if (!patientId) return;

        ubcCommonClkSend({
            value: 'saveBtn'
        });

        const heightError = validateNumericInput(healthProfileData.height, '身高');
        const weightError = validateNumericInput(healthProfileData.weight, '体重');

        if (heightError) {
            showToast({
                title: heightError,
                icon: 'none'
            });
            return;
        }

        if (weightError) {
            showToast({
                title: weightError,
                icon: 'none'
            });
            return;
        }

        showToast({
            title: '保存中...',
            icon: 'loading',
            duration: 0,
            mask: true
        });

        try {
            // 构建增量更新的数据，只包含变化的字段 + 必需字段
            const updateData: UpdatePatientRequest = {
                patientId: patientId,
                form: {
                    // 必需字段
                    relationship: healthProfileData.relation,
                    name: healthProfileData.realName,
                    gender: healthProfileData.gender,
                    birthDate: healthProfileData.birthDate
                }
            };

            // 只添加变化的字段
            changedFields.forEach(field => {
                switch (field) {
                    case 'height':
                        // 前端输入的cm转换为mm提交给后端
                        updateData.form.height = healthProfileData.height
                            ? convertCmToMm(healthProfileData.height)
                            : undefined;
                        break;
                    case 'weight':
                        // 前端输入的kg转换为g提交给后端
                        updateData.form.weight = healthProfileData.weight
                            ? convertKgToG(healthProfileData.weight)
                            : undefined;
                        break;
                    case 'residenceAddress':
                        updateData.form.address = healthProfileData.residenceAddress;
                        break;
                    case 'residenceCity':
                        updateData.form.area = healthProfileData.residenceCity.join(',');
                        break;
                    case 'maritalStatus':
                        updateData.form.maritalStatus = healthProfileData.maritalStatus;
                        break;
                    case 'educationLevel':
                        updateData.form.educationLevel = healthProfileData.educationLevel;
                        break;
                }
            });

            await updatePatientInfo(updateData);
            Taro.hideToast();
            showToast({
                title: '保存成功',
                icon: 'success',
                duration: 1500
            });
            refreshPatientData(patientId);
            navigateBack();
        } catch (error) {
            Taro.hideToast();
            showToast({
                title: error?.[0]?.toast || '保存失败',
                icon: 'none'
            });
        }
    };

    // 处理取消删除
    const handleCancel = () => {
        setIsDeleteConfirmShow(false);
    };

    // 处理确认删除
    const handleConfirmDelete = async () => {
        if (!patientId) return;

        // 关闭对话框
        setIsDeleteConfirmShow(false);

        showToast({
            title: '删除中...',
            icon: 'loading',
            duration: 0,
            mask: true
        });

        try {
            await deletePatient(patientId);
            Taro.hideToast();
            showToast({
                title: '删除成功',
                icon: 'success',
                duration: 1500
            });
            refreshPatientData();
            navigateBack();
        } catch (error) {
            Taro.hideToast();
            showToast({
                title: error?.[0]?.toast || '删除失败',
                icon: 'none'
            });
        }
    };

    // 删除健康档案 - 显示确认对话框
    const handleDeleteHealthProfile = () => {
        setIsDeleteConfirmShow(true);
    };

    return (
        <Portal.provider>
            <CPageContainer
                className={style.healthProfileContainer}
                onBackDetain={onBackDetain}
                backDetain={backDetain}
                topBarProps={{
                    title: '个人资料',
                    className: 'white',
                    textColor: '#000311',
                    barBg: barBg,
                    blank: true,
                    hideHome: true,
                    titleLeftSlot: '',
                    ...(isSwanMsgListScene ? {hideBack: true, isInTabBar: true} : {})
                }}
            >
                <View className={style.healthProfile}>
                    <HealthProfileForm
                        onSave={handleSaveHealthProfile}
                        onDelete={handleDeleteHealthProfile}
                        initialHealthData={initialData}
                        loading={formLoading}
                        pickerOptions={pickerOptions}
                    />
                </View>
                <Dialog open={isDeleteConfirmShow} onClose={handleCancel}>
                    <Dialog.Content>{BUTTON_TEXT.DELETE_WARNING}</Dialog.Content>
                    <Dialog.Actions>
                        <Button onClick={handleCancel}>{BUTTON_TEXT.CANCEL}</Button>
                        <Button onClick={handleConfirmDelete} style={{color: '#00c8c8'}}>
                            {BUTTON_TEXT.CONFIRM_DELETE}
                        </Button>
                    </Dialog.Actions>
                </Dialog>
            </CPageContainer>
        </Portal.provider>
    );
};

export default HealthProfile;
