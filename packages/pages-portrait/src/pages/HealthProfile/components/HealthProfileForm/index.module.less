.healthProfileForm {
    padding: 30px 36px 303px;
}

.healthFormSection {
    background: #fff;
    overflow: hidden;
    border-radius: 45px;
    margin-bottom: 30px;
}

.valueContainer {
    flex: 1;
    display: flex;
    align-items: center;
    height: 48px;
    justify-content: flex-end;

    .nameContainer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;

        .verifiedTag {
            background-color: #00c8c8;
            border-radius: 12px;
            padding: 9px 12px;
            margin-right: 15px;
            flex-shrink: 0;
            display: flex;
            align-items: center;

            .tagText {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 33px;
                line-height: 33px;
                letter-spacing: 0;
                color: #fff;
            }
        }

        &.editable {
            cursor: pointer;

            &:active {
                opacity: 0.7;
            }
        }
    }
}

.formInput {
    flex: 1;
    height: 100%;
    font-size: 45px;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
    padding: 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &.rightAlign {
        text-align: right;
    }

    &::placeholder {
        color: #b7b9c1;
    }
}

.value {
    font-size: 45px;
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &.placeholder {
        color: #b7b9c1;
    }
}

.unit {
    font-size: 48px;
    color: #666;
    margin-left: 6px;
    font-weight: 400;
}

.arrowIcon {
    font-size: 48px;
    color: #ccc;
    margin-left: 6px;
    flex-shrink: 0;
    transform: rotate(0deg);
    transition: transform 0.2s ease;
}

.healthProfileActions {
    display: flex;
    flex-direction: column;
    z-index: 1;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 6px 51px 27px;
    background-color: #eff3f9;

    .deleteProfileButton {
        width: 100%;
        line-height: 42px;
        background: transparent;
        border: none;
        border-radius: 66px;
        font-size: 42px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 30px;
        padding-bottom: 30px;
        margin-bottom: 30px;

        &::before,
        &::after {
            border: none;
        }

        .deleteIcon {
            font-size: 45px;
            margin-right: 6px;
        }

        .deleteText {
            color: #00c8c8;
            font-size: 42px;
        }

        &:active {
            background-color: rgb(0 200 200 / 10%);
        }
    }

    .saveProfileButton {
        width: 100%;
        height: 132px !important;
        line-height: 54px;
        background: linear-gradient(315deg, #00d3ea 0%, #00cfa3 100%);
        color: #fff;
        border: none;
        border-radius: 66px;
        font-size: 54px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 0;

        &::before,
        &::after {
            border: none;
        }

        &:active {
            opacity: 0.8;
        }
    }
}
