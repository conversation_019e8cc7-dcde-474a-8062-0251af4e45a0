import React, {useEffect, useState} from 'react';
import cx from 'classnames';
import {View, Text, Button} from '@tarojs/components';
import {Input, SafeArea} from '@baidu/wz-taro-tools-core';
import {showToast} from '@tarojs/taro';
import {HImage} from '@baidu/health-ui';
import HealthFormItem from '../../../../components/HealthFormItem';
import HealthPickers from '../../../../components/HealthPickers';
import {usePatientData} from '../../../../hooks/portrait/usePatientData';
import InputPopup from '../../../../components/PopupInput';
import {DISEASE_ICON_DELETE, LINK_DIRECT} from '../../../../constants/images'; // 添加 LINK_DIRECT
import style from './index.module.less';

export interface HealthProfileData {
    relation: string;
    realName: string;
    gender: string;
    birthDate: string;
    height?: number;
    weight?: number;
    residenceCity: string[];
    residenceCityCode: string[];
    residenceAddress: string;
    maritalStatus: string;
    educationLevel: string;
    // 权限控制字段
    isCertified?: boolean;
    canEditBasicInfo?: boolean;
    canEditRelationship?: boolean;
    [key: string]: any;
}

interface ValidationErrors {
    relation: boolean;
    realName: boolean;
    gender: boolean;
    birthDate: boolean;
    [key: string]: boolean;
}

interface HealthProfileFormProps {
    onSave?: (data: HealthProfileData, changedFields: string[]) => void;
    onDelete?: () => void;
    initialHealthData?: Partial<HealthProfileData>;
    loading?: boolean;
    pickerOptions?: any;
}

const capitalize = (str: string) => str.charAt(0).toUpperCase() + str.slice(1);

const HealthProfileForm: React.FC<HealthProfileFormProps> = ({
    onSave,
    onDelete,
    initialHealthData = {},
    loading = false,
    pickerOptions = {}
}) => {
    const [healthProfileData, setHealthProfileData] = useState<HealthProfileData>({
        relation: '本人',
        realName: '',
        gender: '',
        birthDate: '',
        height: undefined,
        weight: undefined,
        residenceCity: [],
        residenceCityCode: [],
        residenceAddress: '',
        maritalStatus: '',
        educationLevel: '',
        isCertified: false,
        canEditBasicInfo: true,
        canEditRelationship: true
    });

    const [originalData, setOriginalData] = useState<HealthProfileData>({} as HealthProfileData);
    const [showNameInput, setShowNameInput] = useState(false);
    const {relationshipOptions} = usePatientData();

    useEffect(() => {
        if (Object.keys(initialHealthData).length > 0) {
            const data = {
                ...healthProfileData,
                ...initialHealthData,
                height: initialHealthData.height || undefined,
                weight: initialHealthData.weight || undefined,
                residenceCity: initialHealthData.residenceCity || [],
                residenceCityCode: initialHealthData.residenceCityCode || []
            };
            setHealthProfileData(data);
            setOriginalData(data); // 保存初始数据用于对比
        }
    }, [initialHealthData]);

    const pickerTypes = [
        'gender',
        'relation',
        'maritalStatus',
        'educationLevel',
        'birthDate',
        'residenceCity'
    ];

    type PickerStates = Record<string, boolean>;
    const [pickerStates, setPickerStates] = useState<PickerStates>(
        pickerTypes.reduce((states, type) => {
            states[`show${capitalize(type)}`] = false;
            return states;
        }, {} as PickerStates)
    );

    const [validationErrors, setValidationErrors] = useState<ValidationErrors>({
        relation: false,
        realName: false,
        gender: false,
        birthDate: false
    });

    const [showValidationTips, setShowValidationTips] = useState(false);

    const handleHealthDataChange = (fieldKey: keyof HealthProfileData, fieldValue: any) => {
        setHealthProfileData(prev => ({
            ...prev,
            [fieldKey]: fieldValue
        }));

        if (validationErrors[fieldKey]) {
            setValidationErrors(prev => ({
                ...prev,
                [fieldKey]: false
            }));
        }
    };

    // 数值输入处理
    const handleNumericInput = (value: number, fieldKey: 'height' | 'weight') => {
        handleHealthDataChange(fieldKey, value);
    };

    const validateRequiredHealthFields = (): ValidationErrors => {
        const errors: ValidationErrors = {
            relation: !healthProfileData.relation,
            realName: !healthProfileData.realName,
            gender: !healthProfileData.gender,
            birthDate: !healthProfileData.birthDate
        };

        // 检查必填项并显示相应toast
        if (errors.relation) {
            showToast({
                title: '成员关系不能为空',
                icon: 'none'
            });
        } else if (errors.realName) {
            showToast({
                title: '真实姓名不能为空',
                icon: 'none'
            });
        } else if (errors.gender) {
            showToast({
                title: '性别不能为空',
                icon: 'none'
            });
        } else if (errors.birthDate) {
            showToast({
                title: '出生日期不能为空',
                icon: 'none'
            });
        }

        return errors;
    };

    // 获取变化的字段
    const getChangedFields = (): string[] => {
        const changedFields: string[] = [];
        Object.keys(healthProfileData).forEach(key => {
            if (['isCertified', 'canEditBasicInfo', 'canEditRelationship'].includes(key)) return; // 跳过标识字段

            const current = healthProfileData[key];
            const original = originalData[key];

            // 特殊处理数组字段
            if (key === 'residenceCity' || key === 'residenceCityCode') {
                if (JSON.stringify(current) !== JSON.stringify(original)) {
                    if (key === 'residenceCity') changedFields.push(key);
                }
            } else if (current !== original) {
                changedFields.push(key);
            }
        });
        return changedFields;
    };

    const getDisplayText = (type: string, value: string): string => {
        if (!value) return '';

        switch (type) {
            case 'maritalStatus': {
                const option = pickerOptions.marital?.find(item => item.value === value);
                return option ? option.text : value;
            }
            case 'educationLevel': {
                const option = pickerOptions.education?.find(item => item.value === value);
                return option ? option.text : value;
            }
            default:
                return value;
        }
    };

    const handleSaveHealthProfile = () => {
        const newValidationErrors = validateRequiredHealthFields();
        setValidationErrors(newValidationErrors);
        setShowValidationTips(true);

        const hasValidationErrors = Object.values(newValidationErrors).some(error => error);

        if (!hasValidationErrors) {
            const changedFields = getChangedFields();
            onSave && onSave(healthProfileData, changedFields);
        }
    };

    const handleDeleteHealthProfile = () => {
        onDelete && onDelete();
    };

    const closePicker = (pickerType: string) => {
        setPickerStates(prev => ({
            ...prev,
            [`show${capitalize(pickerType)}`]: false
        }));
    };

    const handlePickerConfirm = (fieldKey, value, codes: string[] = []) => {
        if (fieldKey === 'residenceCity') {
            handleHealthDataChange('residenceCityCode', codes);
        }
        handleHealthDataChange(fieldKey, value);
        closePicker(fieldKey);
    };

    const openHealthInfoPicker = (pickerType: string) => {
        const newPickerStates = {...pickerStates};
        pickerTypes.forEach(type => {
            newPickerStates[`show${capitalize(type)}`] = false;
        });
        newPickerStates[`show${capitalize(pickerType)}`] = true;
        setPickerStates(newPickerStates);
    };

    // 处理真实姓名修改
    const handleNameEdit = () => {
        if (!healthProfileData.canEditBasicInfo) {
            showToast({
                title: '无法修改',
                icon: 'none'
            });
            return;
        }
        setShowNameInput(true);
    };

    const handleNameSave = (name: string) => {
        if (!name.trim()) {
            showToast({
                title: '必选项不能为空',
                icon: 'none'
            });
            return;
        }
        handleHealthDataChange('realName', name);
        setShowNameInput(false);
    };

    // 检查基本信息编辑权限
    const checkBasicInfoEditPermission = (action: () => void) => {
        if (!healthProfileData.canEditBasicInfo) {
            showToast({
                title: '无法修改',
                icon: 'none'
            });
            return;
        }
        action();
    };

    // 检查关系编辑权限
    const checkRelationshipEditPermission = (action: () => void) => {
        if (!healthProfileData.canEditRelationship) {
            showToast({
                title: '无法修改',
                icon: 'none'
            });
            return;
        }
        action();
    };

    const pickers = pickerTypes.map(type => ({
        type,
        show: pickerStates[`show${capitalize(type)}`],
        value:
            type === 'residenceCity'
                ? healthProfileData.residenceCityCode
                : healthProfileData[type],
        onClose: () => closePicker(type),
        onConfirm: (value, codes?: string[]) =>
            handlePickerConfirm(type as keyof HealthProfileData, value, codes)
    }));

    return (
        <View className={style.healthProfileForm}>
            <View className={style.healthFormSection}>
                <HealthFormItem
                    label='成员关系'
                    required={true}
                    error={showValidationTips && validationErrors.relation}
                    onClick={() =>
                        checkRelationshipEditPermission(() => openHealthInfoPicker('relation'))
                    }
                >
                    <View className={style.valueContainer}>
                        <Text className={style.value}>
                            {healthProfileData.relation || '请选择'}
                        </Text>
                        {healthProfileData.canEditRelationship && (
                            <HImage
                                src={LINK_DIRECT}
                                className={style.arrowIcon}
                                width={48}
                                height={48}
                            />
                        )}
                    </View>
                </HealthFormItem>

                <HealthFormItem
                    label='真实姓名'
                    required={true}
                    error={showValidationTips && validationErrors.realName}
                    onClick={handleNameEdit}
                >
                    <View className={style.valueContainer}>
                        <View className={style.nameContainer}>
                            {healthProfileData.isCertified && (
                                <View className={style.verifiedTag}>
                                    <Text className={style.tagText}>已实名</Text>
                                </View>
                            )}
                            <Text className={style.value}>
                                {healthProfileData.realName || '请输入真实姓名'}
                            </Text>
                            {healthProfileData.canEditBasicInfo && (
                                <HImage
                                    src={LINK_DIRECT}
                                    className={style.arrowIcon}
                                    width={48}
                                    height={48}
                                />
                            )}
                        </View>
                    </View>
                </HealthFormItem>

                <HealthFormItem
                    label='性别'
                    required={true}
                    error={showValidationTips && validationErrors.gender}
                    onClick={() =>
                        checkBasicInfoEditPermission(() => openHealthInfoPicker('gender'))
                    }
                >
                    <View className={style.valueContainer}>
                        <Text className={style.value}>{healthProfileData.gender || '请选择'}</Text>
                        {healthProfileData.canEditBasicInfo && (
                            <HImage
                                src={LINK_DIRECT}
                                className={style.arrowIcon}
                                width={48}
                                height={48}
                            />
                        )}
                    </View>
                </HealthFormItem>

                <HealthFormItem
                    label='出生日期'
                    required={true}
                    error={showValidationTips && validationErrors.birthDate}
                    onClick={() =>
                        checkBasicInfoEditPermission(() => openHealthInfoPicker('birthDate'))
                    }
                >
                    <View
                        className={cx(style.valueContainer, {
                            [style.editable]: healthProfileData.canEditBasicInfo
                        })}
                    >
                        <Text className={style.value}>
                            {healthProfileData.birthDate || '请选择'}
                        </Text>
                        {healthProfileData.canEditBasicInfo && (
                            <HImage
                                src={LINK_DIRECT}
                                className={style.arrowIcon}
                                width={48}
                                height={48}
                            />
                        )}
                    </View>
                </HealthFormItem>
            </View>

            <View className={style.healthFormSection}>
                <HealthFormItem label='身高'>
                    <View className={style.valueContainer}>
                        <Input
                            className={cx(style.formInput, style.rightAlign)}
                            type='digit'
                            value={healthProfileData.height}
                            placeholder='请输入'
                            onInput={e => handleNumericInput(e.detail.value, 'height')}
                        />
                        <Text className={style.unit}>cm</Text>
                    </View>
                </HealthFormItem>

                <HealthFormItem label='体重'>
                    <View className={style.valueContainer}>
                        <Input
                            className={cx(style.formInput, style.rightAlign)}
                            type='digit'
                            value={healthProfileData.weight}
                            placeholder='请输入'
                            onInput={e => handleNumericInput(e.detail.value, 'weight')}
                        />
                        <Text className={style.unit}>kg</Text>
                    </View>
                </HealthFormItem>
            </View>

            <View className={style.healthFormSection}>
                <HealthFormItem
                    label='居住城市'
                    onClick={() => openHealthInfoPicker('residenceCity')}
                >
                    <View className={cx(style.valueContainer, style.editable)}>
                        <Text
                            className={cx(style.value, {
                                [style.placeholder]: !healthProfileData.residenceCity?.length
                            })}
                        >
                            {healthProfileData.residenceCity?.join(' ') || '待完善'}
                        </Text>
                        <HImage
                            src={LINK_DIRECT}
                            className={style.arrowIcon}
                            width={48}
                            height={48}
                        />
                    </View>
                </HealthFormItem>

                <HealthFormItem label='详细地址'>
                    <View className={style.valueContainer}>
                        <Input
                            className={cx(style.formInput, style.rightAlign)}
                            value={healthProfileData.residenceAddress}
                            placeholder='请输入详细地址'
                            onInput={e =>
                                handleHealthDataChange('residenceAddress', e.detail.value)
                            }
                        />
                    </View>
                </HealthFormItem>

                <HealthFormItem
                    label='婚姻状况'
                    onClick={() => openHealthInfoPicker('maritalStatus')}
                >
                    <View className={cx(style.valueContainer, style.editable)}>
                        <Text
                            className={cx(style.value, {
                                [style.placeholder]: !healthProfileData.maritalStatus
                            })}
                        >
                            {getDisplayText('maritalStatus', healthProfileData.maritalStatus) ||
                                '待完善'}
                        </Text>
                        <HImage
                            src={LINK_DIRECT}
                            className={style.arrowIcon}
                            width={48}
                            height={48}
                        />
                    </View>
                </HealthFormItem>

                <HealthFormItem
                    label='教育程度'
                    isLast={true}
                    onClick={() => openHealthInfoPicker('educationLevel')}
                >
                    <View className={cx(style.valueContainer, style.editable)}>
                        <Text
                            className={cx(style.value, {
                                [style.placeholder]: !healthProfileData.educationLevel
                            })}
                        >
                            {getDisplayText('educationLevel', healthProfileData.educationLevel) ||
                                '待完善'}
                        </Text>
                        <HImage
                            src={LINK_DIRECT}
                            className={style.arrowIcon}
                            width={48}
                            height={48}
                        />
                    </View>
                </HealthFormItem>
            </View>

            <View className={style.healthProfileActions}>
                <Button
                    className={style.deleteProfileButton}
                    onClick={handleDeleteHealthProfile}
                    disabled={loading}
                >
                    <HImage
                        className={style.deleteIcon}
                        src={DISEASE_ICON_DELETE}
                        width={48}
                        height={48}
                    ></HImage>
                    <Text className={style.deleteText}>{'删除就诊人'}</Text>
                </Button>
                <Button
                    className={style.saveProfileButton}
                    onClick={handleSaveHealthProfile}
                    disabled={loading}
                >
                    {'保存'}
                </Button>
                <SafeArea position='bottom' style={{backgroundColor: 'transparent'}} />
            </View>

            <HealthPickers
                pickers={pickers}
                pickerOptions={pickerOptions}
                relationshipOptions={relationshipOptions}
            />

            <InputPopup
                showPopup={showNameInput}
                onClosePopup={() => setShowNameInput(false)}
                title='修改姓名'
                initialValue={healthProfileData.realName}
                onConfirm={handleNameSave}
                isSingleLine={true}
            />
        </View>
    );
};

export default HealthProfileForm;
