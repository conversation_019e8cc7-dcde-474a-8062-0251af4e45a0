import {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {View} from '@tarojs/components';
import {previewImage, navigateBack} from '@tarojs/taro';
import {Dialog, Button, WImage, SafeArea} from '@baidu/wz-taro-tools-core';
import {HButton, HImage} from '@baidu/health-ui';
import {allSettled} from '@baidu/vita-pages-im/src/utils/generalFunction/allSettled';
import cx from 'classnames';

import dayjs from 'dayjs';
import {ImFlow, Portal} from '../../../../ui-cards-common';
import HealthFormItem from '../../components/HealthFormItem';
import {ubcCommonClkSend} from '../../../../pages-im/src/utils/generalFunction/ubc';
import {usePatientData, usePatientDataFetch} from '../../hooks/portrait/usePatientData';
import CPageContainer from '../../../../pages-im/src/components/CPageContainer';
import ImThinking from '../../../../pages-im/src/components/ImThinking';
import {useGetSwanMsgListSceneStatus} from '../../../../pages-im/src/hooks/triageStream/useGetSwanMsgListSceneStatus';
import {useRetentionDialog} from '../../../../pages-im/src/hooks/useRetentionDialog';
import type {FormField, IReportTag} from '../../components/PopupLabel/index.d';
import {
    getDeleteMedicalReport,
    getMedicalReportDetail,
    getUpdateMedicalReport
} from '../../models/services/portrait/healthReport';
import {useGetUrlParams} from '../../../../pages-im/src/hooks/common';
import {
    OperateMedicalReportParams,
    UpdateMedicalReportParams
} from '../../models/services/portrait/healthReport/index.d';
import LabelPopup from '../../components/PopupLabel';
import InputPopup from '../../components/PopupInput';
import DatePicker from '../../components/PickerDate';
import {BUTTON_TEXT} from '../../constants/healthReportDetail';

import {SSEResponseType} from '../../models/services/portrait/sse/index.d';
import {conversationSSE, SSEProcessorInstance} from '../../models/services/portrait/sse';
import {UBC_FIELD_DETAIL_PAGE} from '../portrait/constants';
import {DELETE_ICON, EDIT_ICON, LINK_DIRECT} from '../../constants/images';
import {DEFAULT_DISPLAY_TEXT} from '../../constants/portrait';
import ReportCard from './components/ReportCard';
import {getLabelTypeTexts} from './utils';

import styles from './index.module.less';

const PAGE_TITLE = '资料详情';

const EMPTY_VALUE = ''; // 实际存储和提交的空值
const DEFAULT_TIME = dayjs().format('YYYY-MM-DD');

// 表单状态类型
interface ReportFormData {
    labelType: string[];
    time: string;
    hospital: string;
    remark: string;
}

enum AnalyzeStatus {
    INIT = 'init',
    PENDING = 'pending',
    ANALYZING = 'analyzing',
    DONE = 'done'
}

const analyzeBtnConfig = {
    [AnalyzeStatus.INIT]: {
        text: '我要解读资料',
        disabled: false
    },
    [AnalyzeStatus.PENDING]: {
        text: '我要解读资料',
        disabled: false
    },
    [AnalyzeStatus.ANALYZING]: {
        text: '正在解读中...',
        disabled: true
    },
    [AnalyzeStatus.DONE]: {
        text: '已解读',
        disabled: true
    }
};

const HealthReportUpload = () => {
    const {status: isSwanMsgListScene} = useGetSwanMsgListSceneStatus();
    const {onBackDetain, backDetain} = useRetentionDialog();
    const {refreshPatientData} = usePatientDataFetch();
    const {reportId, patientId} = useGetUrlParams();
    const {reportTagOptions} = usePatientData();

    const [isLabelPopupShow, setIsLabelPopupShow] = useState(false);
    const [isInputPopupShow, setIsInputPopupShow] = useState(false);
    const [isDeleteConfirmShow, setIsDeleteConfirmShow] = useState(false);

    const [currentField, setCurrentField] = useState<FormField | null>(null);
    const [isEditing, setIsEditing] = useState(false);
    const [reportImage, setReportImage] = useState('');
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [useStream, setUseStream] = useState(false); // true 用流式(obj)  false 用静态(reportContent)
    const [analyzeStatus, setAnalyzeStatus] = useState<AnalyzeStatus>(AnalyzeStatus.INIT);
    const [reportContent, setReportContent] = useState<any>(null);
    const [checkedList, setCheckedList] = useState<IReportTag[]>([]);

    const [reportFormData, setReportFormData] = useState<ReportFormData>({
        labelType: [],
        time: EMPTY_VALUE,
        hospital: EMPTY_VALUE,
        remark: EMPTY_VALUE
    });

    // 预览图片
    const handleViewPic = useCallback(url => {
        previewImage({
            current: url,
            urls: [url]
        });
    }, []);

    const handleEdit = () => {
        if ([AnalyzeStatus.ANALYZING, AnalyzeStatus.PENDING].includes(analyzeStatus)) {
            return;
        }
        setIsEditing(true);

        // ubc 打点逻辑
        ubcCommonClkSend({
            value: 'editBtn',
            page: UBC_FIELD_DETAIL_PAGE
        });
    };

    const fetchDetail = async () => {
        const res = await getMedicalReportDetail({
            patientId: patientId || '',
            medicalReportId: reportId || ''
        });
        setReportFormData({
            labelType: res.medicalReport.reportTags || [],
            time: res.medicalReport.reportTime || '',
            hospital: res.medicalReport.hospitalName || '',
            remark: res.medicalReport.comments || ''
        });
        res.medicalReport.images && setReportImage(res.medicalReport.images[0].thumb);

        const alreadyExplained = !!res.medicalReport.reportContent;
        if (alreadyExplained) {
            setAnalyzeStatus(AnalyzeStatus.DONE);
        }

        if (alreadyExplained) {
            res.medicalReport.reportContent &&
                setReportContent({
                    action: 'end',
                    content: {
                        data: {
                            cardStyle: res.medicalReport.reportContent.cardStyle,
                            content: {
                                list: [
                                    {
                                        sectionId: 1,
                                        content: res.medicalReport.reportContent.content || '',
                                        planIcon: res.medicalReport.reportContent.planIcon || '',
                                        planProgressDesc:
                                            res.medicalReport.reportContent.planProgressDesc ||
                                            '报告解读结果',
                                        type: 'markdown',
                                        speed: 0,
                                        isFinish: true // 一次性返回，直接标完成
                                    }
                                ]
                            }
                        }
                    }
                });
            setAnalyzeStatus(AnalyzeStatus.DONE);
            setUseStream(false); // 用静态内容
        }
    };

    const handleSave = async () => {
        const params: UpdateMedicalReportParams = {
            patientId: patientId || '',
            form: {
                medicalReportId: reportId || '',
                reportTags: reportFormData.labelType,
                reportTime: reportFormData.time,
                hospitalName: reportFormData.hospital,
                comments: reportFormData.remark
            }
        };
        await getUpdateMedicalReport(params);
        await fetchDetail();

        refreshPatientData(patientId);
        navigateBack();

        setIsEditing(false);
    };

    const handleConfirmDelete = async () => {
        try {
            const params: OperateMedicalReportParams = {
                medicalReportId: reportId || '',
                patientId: patientId || ''
            };
            await getDeleteMedicalReport(params);
            setIsDeleteConfirmShow(false);

            refreshPatientData(patientId);
            navigateBack();
        } catch (err) {
            console.error(err);
        } finally {
            setIsDeleteConfirmShow(false);
        }
    };

    const handleDelete = async () => {
        // 正在解读中不允许操作删除
        if ([AnalyzeStatus.ANALYZING, AnalyzeStatus.PENDING].includes(analyzeStatus)) {
            return;
        }
        setIsDeleteConfirmShow(true);
        // ubc 打点逻辑
        ubcCommonClkSend({
            value: 'deleteBtn',
            page: UBC_FIELD_DETAIL_PAGE
        });
    };

    const handleCancel = () => {
        setIsDeleteConfirmShow(false);
    };

    const [contentX, setContentX] = useState({
        sectionId: 1,
        planIcon: '',
        planProgressDesc: '',
        content: '',
        type: 'markdown',
        isFinish: false
    });

    const [obj, setObj] = useState({
        content: {
            data: {
                cardStyle: {
                    renderType: 2
                },
                content: {
                    list: [
                        {
                            sectionId: 1,
                            planIcon: '',
                            planProgressDesc: '',
                            content: '',
                            type: 'markdown',
                            isFinish: false
                        }
                    ]
                }
            },
            localExt: {
                dataSource: 'conversation'
            }
        }
    });

    useEffect(() => {
        obj.content.data.content.list = [contentX];
        obj.content.data = {...obj.content.data};
        setObj({...obj});
    }, [contentX]);

    // 解读操作
    const handleInterpretAction = async () => {
        if (analyzeStatus !== AnalyzeStatus.INIT) {
            return;
        }
        setAnalyzeStatus(AnalyzeStatus.PENDING);
        setUseStream(true); // 开始用流式

        // ubc 打点逻辑
        ubcCommonClkSend({
            value: 'analyzeBtn',
            page: UBC_FIELD_DETAIL_PAGE
        });
        try {
            const params = {
                patientId: patientId || '',
                medicalReportId: reportId || ''
            };

            const sseTask = await conversationSSE({params});

            const promiseTasks: Promise<SSEProcessorInstance<SSEResponseType> | unknown>[] = [
                sseTask
            ];
            const [sseTaskVal] = await allSettled(promiseTasks);

            if (sseTaskVal.status === 'fulfilled') {
                const sseInstance = sseTaskVal.value as SSEProcessorInstance<SSEResponseType>;

                for await (const chunk of sseInstance.message()) {
                    setAnalyzeStatus(AnalyzeStatus.ANALYZING);
                    const item = chunk?.data?.content?.list?.[0];
                    item &&
                        setContentX(pre => {
                            if (pre.sectionId === -1) {
                                return {
                                    ...item
                                };
                            } else {
                                const content = pre.content + (item?.content ?? '');
                                return {
                                    ...pre,
                                    ...item,
                                    content
                                };
                            }
                        });
                }
                // HACK 手动设置 action 为 end，否则会一直处于解读中。
                // bug详见：https://console.cloud.baidu-int.com/devops/icafe/issue/health-bugs-24477/show?source=copy-shortcut
                setObj({...obj, action: 'end'});
            }
        } catch (err) {
            console.error('初始化错误:=====>', err);
        } finally {
            setAnalyzeStatus(AnalyzeStatus.DONE);
        }
    };

    const handleDateConfirm = (date: string) => {
        setReportFormData(prev => ({...prev, time: date}));
        setShowDatePicker(false);
    };

    // 打开对应弹层
    const handleFieldClick = (field: FormField) => {
        if (!isEditing) return;

        setCurrentField(field);
        if (field === 'time') {
            setShowDatePicker(true);
        } else if (field === 'labelType') {
            setIsLabelPopupShow(true);
        } else {
            setIsInputPopupShow(true);
        }
    };

    const tagsForSelector: IReportTag[] = reportTagOptions.slice(1).map(item => ({
        code: item.value,
        value: item.text
    }));

    const handleLabelConfirm = (tags: IReportTag[]) => {
        const strTags = tags.map(t => t.code);
        setReportFormData(prev => ({...prev, labelType: strTags}));
        setIsLabelPopupShow(false);
    };

    const handleInputConfirm = (value: string) => {
        if (currentField) {
            setReportFormData(prev => ({
                ...prev,
                [currentField]: value || EMPTY_VALUE
            }));
        }
        setIsInputPopupShow(false);
    };

    const popupTitle = useMemo(() => {
        switch (currentField) {
            case 'hospital':
                return '就诊医院';
            default:
                return '备注';
        }
    }, [currentField]);

    const initialValue = useMemo(() => {
        switch (currentField) {
            case 'hospital':
                return reportFormData.hospital;
            default:
                return reportFormData.remark;
        }
    }, [currentField, reportFormData]);

    const inputMode = useMemo(() => {
        switch (currentField) {
            case 'hospital':
                return true;
            default:
                return false;
        }
    }, [currentField]);

    const renderFormItem = (
        label: string,
        field: FormField,
        required = false,
        isLast?: boolean
    ) => {
        // 特殊处理labelType的展示
        const displayValue =
            field === 'labelType'
                ? getLabelTypeTexts(reportFormData.labelType, reportTagOptions).join(', ') ||
                  DEFAULT_DISPLAY_TEXT
                : (reportFormData[field] as string) || DEFAULT_DISPLAY_TEXT;

        return (
            <HealthFormItem label={label} required={required} isLast={isLast}>
                <View className={styles.valueContainer} onClick={() => handleFieldClick(field)}>
                    <View className={cx(styles.value, 'wz-fs-48 wz-mr-18')}>{displayValue}</View>
                    <View className={styles.iconContainer}>
                        {isEditing && (
                            <HImage
                                src={LINK_DIRECT}
                                className={styles.arrowIcon}
                                width={45}
                                height={45}
                            />
                        )}
                    </View>
                </View>
            </HealthFormItem>
        );
    };

    useEffect(() => {
        fetchDetail();
    }, []);

    useEffect(() => {
        const newCheckedList: IReportTag[] = reportFormData.labelType
            .map(code => {
                const tag = reportTagOptions.find(t => t.value === code);
                if (!tag) {
                    console.error(`未找到标签: ${code}`);
                    return null;
                }
                return {code: tag.value, value: tag.text};
            })
            .filter(tag => tag !== null);

        setCheckedList(newCheckedList);
    }, [reportFormData.labelType, reportTagOptions]);

    return (
        <Portal.provider>
            <CPageContainer
                onBackDetain={onBackDetain}
                backDetain={backDetain}
                topBarProps={{
                    title: ' ',
                    className: 'white',
                    textColor: '#000311',
                    barBg: '#F2F6FF',
                    blank: true,
                    hideHome: true,
                    titleLeftSlot: PAGE_TITLE,
                    ...(isSwanMsgListScene ? {hideBack: true, isInTabBar: true} : {})
                }}
            >
                <View className={cx(styles.heathReportDetail, 'wz-plr-45')}>
                    <View className={styles.contentWrapper}>
                        {/* 未解读且未点击按钮：仅展示表单 */}
                        {(analyzeStatus === AnalyzeStatus.INIT || isEditing) && (
                            <View className={cx(styles.reportForm, 'wz-br-63')}>
                                {reportImage && (
                                    <View className={cx(styles.reportImgContainer, 'wz-plr-45')}>
                                        <WImage
                                            onClick={() => handleViewPic(reportImage)}
                                            src={reportImage}
                                            className={cx(styles.reportImg)}
                                            mode='aspectFill'
                                        />
                                    </View>
                                )}
                                {renderFormItem('资料类型', 'labelType')}
                                {renderFormItem('时间', 'time')}
                                {renderFormItem('就诊医院', 'hospital')}
                                {renderFormItem('备注', 'remark', false, true)}
                            </View>
                        )}
                        {/* 解读卡片 */}
                        {analyzeStatus !== AnalyzeStatus.INIT && !isEditing && (
                            <>
                                <ReportCard
                                    imageUrl={reportImage}
                                    tags={getLabelTypeTexts(
                                        reportFormData.labelType,
                                        reportTagOptions
                                    )}
                                    reportTime={reportFormData.time}
                                    hospital={reportFormData.hospital}
                                    remark={reportFormData.remark}
                                />
                                {/* 正在解读：展示解读中 */}
                                {analyzeStatus === AnalyzeStatus.PENDING && (
                                    <View className={styles.thinkingContainer}>
                                        <ImThinking />
                                    </View>
                                )}
                                {/* 已解读 或 正在解读：展示卡片 */}
                                {[AnalyzeStatus.DONE, AnalyzeStatus.ANALYZING].includes(
                                    analyzeStatus
                                ) && (
                                    <View className={styles.reportContent}>
                                        <ImFlow
                                            msgId='jkda'
                                            data={useStream ? obj : reportContent}
                                        />
                                    </View>
                                )}
                            </>
                        )}
                    </View>

                    <View className={styles.bottomBar}>
                        <View className='wz-flex wz-row-between wz-plr-51 wz-ptb-24'>
                            {!isEditing ? (
                                <>
                                    <View
                                        className={cx(styles.editBtn, 'wz-flex wz-fs-45')}
                                        onClick={handleEdit}
                                    >
                                        <HImage
                                            src={EDIT_ICON}
                                            width={54}
                                            height={54}
                                            className={cx(styles.historyPatientBarIcon, 'wz-mr-18')}
                                        />
                                        {BUTTON_TEXT.EDIT}
                                    </View>
                                    <View
                                        className={cx(styles.deleteBtn, 'wz-flex wz-fs-45')}
                                        onClick={handleDelete}
                                    >
                                        <HImage
                                            src={DELETE_ICON}
                                            width={54}
                                            height={54}
                                            className={cx(styles.historyPatientBarIcon, 'wz-mr-18')}
                                        />
                                        {BUTTON_TEXT.DELETE}
                                    </View>
                                    <HButton
                                        className={cx(
                                            styles.otherBtn,
                                            'wz-fw-500',
                                            !analyzeBtnConfig[analyzeStatus].disabled &&
                                                styles.active
                                        )}
                                        onClick={handleInterpretAction}
                                        // loading={status === 'loading'}
                                        text={analyzeBtnConfig[analyzeStatus].text}
                                        disabled={analyzeBtnConfig[analyzeStatus].disabled} // 已解读或流程完成都置灰
                                        size={54}
                                    />
                                </>
                            ) : (
                                <HButton
                                    className={cx(styles.saveBtn, 'wz-fs-54 wz-fw-500')}
                                    onClick={handleSave}
                                    text={BUTTON_TEXT.SAVE}
                                    size={54}
                                />
                            )}
                        </View>
                        <SafeArea position='bottom' />
                    </View>
                </View>
                <Portal.slot />
                <DatePicker
                    value={reportFormData.time || DEFAULT_TIME}
                    show={showDatePicker}
                    onClose={() => setShowDatePicker(false)}
                    onConfirm={handleDateConfirm}
                />
                <LabelPopup
                    checkedList={checkedList}
                    onChange={setCheckedList}
                    openStatus={isLabelPopupShow}
                    onClosePopup={() => setIsLabelPopupShow(false)}
                    title='资料类型'
                    onConfirm={handleLabelConfirm}
                    reportTags={tagsForSelector}
                    columns={2}
                />
                <InputPopup
                    initialValue={initialValue}
                    showPopup={isInputPopupShow}
                    title={popupTitle}
                    isSingleLine={inputMode}
                    onClosePopup={() => setIsInputPopupShow(false)}
                    onConfirm={handleInputConfirm}
                />
                <Dialog open={isDeleteConfirmShow} onClose={handleCancel}>
                    <Dialog.Content>{BUTTON_TEXT.DELETE_WARNING}</Dialog.Content>
                    <Dialog.Actions>
                        <Button onClick={handleCancel}>{BUTTON_TEXT.CANCEL}</Button>
                        <Button onClick={handleConfirmDelete} style={{color: '#00c8c8'}}>
                            {BUTTON_TEXT.CONFIRM_DELETE}
                        </Button>
                    </Dialog.Actions>
                </Dialog>
            </CPageContainer>
        </Portal.provider>
    );
};

HealthReportUpload.displayName = 'HealthReportUpload';

export default memo(HealthReportUpload);
