.heathReportDetail {
    background: #f2f6ff;
    padding-top: 60px;
    padding-bottom: 180px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;

    .contentWrapper {
        margin-bottom: 180px;
    }

    .thinkingContainer,
    .reportContent {
        margin-top: 60px;
    }

    .thinkingContainer {
        width: 20vw;
    }

    .reportForm {
        background: #fff;
        padding-top: 60px;
        margin-bottom: 30px;

        .valueContainer {
            flex: 1;
            display: flex;
            align-items: center;
            height: 45px;
            justify-content: flex-end;
        }

        .value {
            line-height: 72px;
            text-align: right;
        }

        .iconContainer {
            flex-shrink: 0;
            display: flex;
            align-items: center;
        }

        .placeholder {
            color: #b7b9c1;
        }
    }

    .reportImg {
        width: 100%;
        height: 618px;
        border-radius: 45px;
    }

    .bottomBar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        z-index: 9;

        .editBtn {
            color: #272933;
        }

        .deleteBtn {
            color: #272933;
        }

        .otherBtn {
            width: 585px;
            height: 132px;
            color: #fff;
        }

        .saveBtn {
            width: 100%;
            height: 132px;
            color: #fff;
            background: linear-gradient(-45deg, #00d3ea 0%, #00cfa3 100%);
        }

        .active {
            background: linear-gradient(-45deg, #00d3ea 0%, #00cfa3 100%);
        }

        .disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    }
}
