.cardContainer {
    width: 100%;
    box-sizing: border-box;
    background: #fff;
    padding-top: 60px;
    padding-bottom: 60px;
    overflow: hidden;
    align-items: flex-start;

    .cardImage {
        width: 216px;
        height: 216px;
        flex-shrink: 0;
        border-radius: 45px;
    }

    .rightContent {
        flex: 1;
        flex-direction: column;
        overflow: hidden;

        .tag {
            background: #0000000c;
            color: #272933;
        }

        .hospital {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .remark {
            color: #848691;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}
