import {FC, memo, useCallback} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';
import {View} from '@tarojs/components';
import {previewImage} from '@tarojs/taro';
import cx from 'classnames';

import styles from './index.module.less';

interface IProps {
    imageUrl: string;
    tags?: string[];
    reportTime?: string;
    hospital?: string;
    remark?: string;
}

const ReportCard: FC<IProps> = (props: IProps) => {
    const {imageUrl, tags = [], reportTime, hospital, remark} = props;

    // 预览图片
    const handleViewPic = useCallback(url => {
        previewImage({
            current: url,
            urls: [url]
        });
    }, []);

    return (
        <View className={cx(styles.cardContainer, 'wz-flex wz-br-63 wz-plr-45')}>
            <WImage
                onClick={() => handleViewPic(imageUrl)}
                className={cx(styles.cardImage, 'wz-mr-36')}
                src={imageUrl}
                mode='aspectFill'
            />
            <View className={styles.rightContent}>
                {tags && (
                    <View className={cx(styles.tagContainer, 'wz-flex')}>
                        {tags.map((tag, index) => (
                            <View
                                key={`${tag}-${index}`}
                                className={cx(
                                    styles.tag,
                                    'wz-mr-15 wz-ptb-15 wz-plr-21 wz-fw-500 wz-fs-39 wz-br-18'
                                )}
                            >
                                <View className={styles.tagText}>{tag}</View>
                            </View>
                        ))}
                    </View>
                )}
                {reportTime && (
                    <View className={cx(styles.reportTime, 'wz-mt-9')}>
                        {`就诊时间: ${reportTime}`}
                    </View>
                )}
                {hospital && <View className={cx(styles.hospital, 'wz-mt-9')}>{hospital}</View>}
                {remark && <View className={cx(styles.remark, 'wz-mt-9')}>{remark}</View>}
            </View>
        </View>
    );
};
ReportCard.displayName = 'ReportCard';

export default memo(ReportCard);
