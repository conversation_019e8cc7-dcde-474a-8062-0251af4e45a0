import {ReportTagOption} from '../../typings/portrait';

export const getLabelTypeTexts = (codes: string[], options: ReportTagOption[]): string[] => {
    return codes
        .map(code => {
            const option = options.find(option => option.value === code);
            if (!option) {
                console.error(`未找到标签: ${code}`);
                return null;
            }
            return option.text;
        })
        .filter(text => text !== null) as string[];
};
