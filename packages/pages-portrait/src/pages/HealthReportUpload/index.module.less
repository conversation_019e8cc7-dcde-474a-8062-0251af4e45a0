.heathReport {
    background: #f2f6ff;
    height: calc(100% - 60px);
    padding-top: 60px;

    .uploadTip {
        color: #272933;
        font-weight: 500;
        font-size: 45px;
        line-height: 45px;
    }

    .reportForm {
        background: #fff;

        .valueContainer {
            flex: 1;
            display: flex;
            align-items: center;
            height: 45px;
            justify-content: flex-end;
        }

        .value {
            line-height: 72px;
            text-align: right;
        }

        .iconContainer {
            flex-shrink: 0;
            display: flex;
            align-items: center;
        }

        .placeholder {
            color: #b7b9c1;
        }
    }

    .patientWrapper {
        flex-direction: column;

        .patientBasicInfo {
            color: #000311;
            margin-top: 60px;

            .patientAvatar {
                width: 96px;
                height: 96px;
            }

            .divider {
                color: #a7b4c5;
            }

            .relationship {
                font-size: 60px;
                line-height: 60px;
            }
        }
    }

    .uploadButtonWrapper {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        z-index: 9;

        .confirmButton {
            width: 100%;
            height: 132px;
            color: #fff;
        }

        .active {
            background: linear-gradient(-45deg, #00d3ea 0%, #00cfa3 100%);
        }
    }

    .skeletonInput {
        width: 100%;
        height: 46px;
        border-radius: 8px;
        background: #f2f2f2;
    }

    .imageContainer {
        width: 240px;
        height: 240px;
    }

    .imageItemSkeleton {
        width: 100%;
        height: 100%;
        border-radius: 45px;
    }
}
