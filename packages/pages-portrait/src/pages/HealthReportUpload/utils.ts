import {IReportTag} from '../../components/TagSelector/index.d';
import {IList} from '../../models/services/portrait/healthReport/index.d';
import {ReportTag} from '../../typings/upload';

export function mergeExtract(list: IList[]) {
    return list.reduce<Record<string, IList>>((acc, cur) => ({...acc, [cur.objectId]: cur}), {});
}

export const apiToInternal = (tags: ReportTag[]): IReportTag[] =>
    tags.map(t => ({code: t.code, value: t.text}));
