import {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {navigateBack, showToast, hideToast, previewImage} from '@tarojs/taro';
import {useAtom, useAtomValue} from 'jotai';
import {View, Image, Text} from '@tarojs/components';
import {HButton, HImage} from '@baidu/health-ui';
import cx from 'classnames';
import dayjs from 'dayjs';

import {SafeArea, Skeleton} from '@baidu/wz-taro-tools-core';
import {uploadFileToBos} from '../../../../pages-im/src/utils/basicAbility/upload';
import {
    ubcCommonClkSend,
    ubcCommonViewSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';
import {usePatientData, usePatientDataFetch} from '../../hooks/portrait/usePatientData';
import {Portal} from '../../../../ui-cards-common';
import HealthFormItem from '../../components/HealthFormItem';
import ReportUploadPopup from '../../components/PopupReportUpload';
import LabelPopup from '../../components/PopupLabel';
import CPageContainer from '../../../../pages-im/src/components/CPageContainer';
import {useGetSwanMsgListSceneStatus} from '../../../../pages-im/src/hooks/triageStream/useGetSwanMsgListSceneStatus';
import {useRetentionDialog} from '../../../../pages-im/src/hooks/useRetentionDialog';
import DatePicker from '../../components/PickerDate';
import type {FormField, IReportTag} from '../../components/PopupLabel/index.d';
import InputPopup from '../../components/PopupInput';
import ImageListTools from '../../components/ImageListTools';
import {useExportLabelFetch} from '../../hooks/portrait/useReportData';
import {getCreateMedicalReport} from '../../models/services/portrait/healthReport';
import {
    MedicalReportParams,
    MedicalReports
} from '../../models/services/portrait/healthReport/index.d';
import {
    imgListAtom,
    statusListAtom,
    extractMapAtom,
    selectedImgIdAtom,
    reportFormDataAtom,
    updateManualFormAtom,
    isParseResultReadyAtom,
    manualFormAtom
} from '../../store/portrait/index';
import {IPicProps, ReportTag} from '../../typings/upload';
import {API_HOST} from '../../models/apis/host';
import {BUCKET_NAME, ONLINE_HOST} from '../../constants/common';
import {useGetUrlParams} from '../../../../pages-im/src/hooks/common';
import {AVATAR_DEFAULT, LINK_DIRECT} from '../../constants/images';
import {UploadStatus} from '../../constants/upload';
import {UBC_FIELD_UPLOAD_PAGE} from '../portrait/constants';
import {DEFAULT_DISPLAY_TEXT} from '../../constants/portrait';
import {apiToInternal, mergeExtract} from './utils';

import styles from './index.module.less';

const PAGE_TITLE = '上传资料';
const DEFAULT_TIME = dayjs().format('YYYY-MM-DD');
const bucketConfName =
    API_HOST && ONLINE_HOST.indexOf(API_HOST) > -1 ? BUCKET_NAME[2] : `${BUCKET_NAME[2]}-test`;
const count = 9;

const EMPTY_VALUE = ''; // 实际存储和提交的空值

const HealthReportUpload = () => {
    const [popupOpenStatus, setPopupOpenStatus] = useState(false);
    const {status: isSwanMsgListScene} = useGetSwanMsgListSceneStatus();
    const {onBackDetain, backDetain} = useRetentionDialog();

    const {selectedPatient, reportTagOptions} = usePatientData();
    const {patientId} = useGetUrlParams();

    const [imgList, setImgList] = useAtom(imgListAtom);
    const [extractMap, setExtractMap] = useAtom(extractMapAtom);
    const [statusList, setStatusList] = useAtom(statusListAtom);
    const [isParseResultReady, setIsParseResultReady] = useAtom(isParseResultReadyAtom);

    const {fetchReportData} = useExportLabelFetch();
    const {refreshPatientData} = usePatientDataFetch();
    const [currentField, setCurrentField] = useState<FormField | null>(null);
    const [isLabelPopupShow, setIsLabelPopupShow] = useState(false);
    const [isInputPopupShow, setIsInputPopupShow] = useState(false);

    const [selectedImgId, setSelectedImgId] = useAtom(selectedImgIdAtom);
    const [showDatePicker, setShowDatePicker] = useState(false);

    const reportFormData = useAtomValue(reportFormDataAtom);
    const [manualForms, setManualForms] = useAtom(manualFormAtom);
    const [, updateForm] = useAtom(updateManualFormAtom);

    const [checkedList, setCheckedList] = useState<IReportTag[]>([]);

    const handleDateConfirm = (date: string) => {
        updateForm({time: date, imgId: selectedImgId});
        setShowDatePicker(false);
    };

    const handleFieldClick = (field: FormField) => {
        setCurrentField(field);
        if (field === 'time') {
            setShowDatePicker(true);
        } else if (field === 'labelType') {
            setIsLabelPopupShow(true);
        } else {
            setIsInputPopupShow(true);
        }
    };

    const tagsForSelector: IReportTag[] = reportTagOptions.slice(1).map(item => ({
        code: item.value,
        value: item.text
    }));

    const handleLabelConfirm = (tags: IReportTag[]) => {
        updateForm({labelType: tags, imgId: selectedImgId});
        setIsLabelPopupShow(false);
    };

    const handleInputConfirm = (value: string) => {
        if (currentField) {
            updateForm({[currentField]: value, imgId: selectedImgId});
        }
        setIsInputPopupShow(false);
    };

    const handleOpenPopup = useCallback(() => {
        setPopupOpenStatus(true);
    }, []);

    const handleClosePopup = () => {
        setPopupOpenStatus(false);
    };

    const popupTitle = useMemo(() => {
        switch (currentField) {
            case 'hospital':
                return '就诊医院';
            default:
                return '备注';
        }
    }, [currentField]);

    const inputMode = useMemo(() => {
        switch (currentField) {
            case 'hospital':
                return true;
            default:
                return false;
        }
    }, [currentField]);

    const initialValue = useMemo(() => {
        switch (currentField) {
            case 'hospital':
                return reportFormData.hospital;
            default:
                return reportFormData.remark;
        }
    }, [currentField, reportFormData]);

    const handleConfirmUpload = useCallback(async () => {
        try {
            const validImgs = imgList.filter(item => item.uploadStatus === UploadStatus.SUCCESS);

            const medicalReports: MedicalReports[] = validImgs.map(img => {
                const fileName = img.fileName || EMPTY_VALUE;
                // 合并数据，手动修改优先
                const formData = {
                    ...(extractMap[fileName] || {}),
                    ...(manualForms[fileName] || {})
                };

                return {
                    objectId: fileName,
                    reportTime: formData.time,
                    hospitalName: formData.hospital,
                    reportTags: formData.labelType?.map(t => t?.code),
                    comments: formData.remark
                };
            });

            const params: MedicalReportParams = {
                patientId: patientId || EMPTY_VALUE,
                medicalReports
            };

            const result = await getCreateMedicalReport(params);

            if (result.failureCnt > 0) {
                showToast({
                    title: `${result.failureCnt}个报告上传失败`,
                    icon: 'none',
                    duration: 3000
                });
                console.error(
                    '失败的报告:',
                    result.medicalReports.filter(r => !r.isSuccess)
                );
            }

            if (result.successCnt > 0) {
                refreshPatientData(patientId);
                navigateBack();
            }
            // ubc打点逻辑
            ubcCommonClkSend({
                value: 'uploadBtn',
                page: UBC_FIELD_UPLOAD_PAGE
            });
        } catch (err) {
            console.error('上传失败', err);
        }
    }, [extractMap, imgList, patientId, manualForms]);

    const handleImageClick = (imgId: string) => {
        if (selectedImgId === imgId) {
            if (process.env.TARO_ENV === 'swan') {
                // FIXME 百度小程序下，blob与base64的图片都无法使用taro的previewImage预览，所以这里通过获取后端返回的
                // 在线图片的url来预览。后续小程序支持bdFile数据后，可以删除这段代码，直接使用bdFile进行预览
                previewImage({
                    urls: [...imgList.map(item => item.onlineURL?.origin || '')],
                    current: imgList.find(item => item.fileName === imgId)?.onlineURL?.origin || ''
                });
            } else {
                previewImage({
                    urls: [...imgList.map(item => item.path || '')],
                    current: imgList.find(item => item.fileName === imgId)?.path || ''
                });
            }
        } else {
            setSelectedImgId(imgId);
        }
    };

    useEffect(() => {
        setExtractMap(prev => {
            prev[selectedImgId] = {...prev[selectedImgId], ...reportFormData};
            return prev;
        });
    }, [reportFormData, selectedImgId, setExtractMap, extractMap]);

    const renderFormItem = (
        label: string,
        field: FormField,
        required = false,
        isLast?: boolean
    ) => {
        // 特殊处理 labelType 的展示
        const valueText =
            field === 'labelType'
                ? reportFormData.labelType.map(t => t.value).join(', ') || DEFAULT_DISPLAY_TEXT
                : reportFormData[field] || DEFAULT_DISPLAY_TEXT;

        return (
            <HealthFormItem label={label} required={required} isLast={isLast}>
                {isParseResultReady ? (
                    <View className={styles.valueContainer} onClick={() => handleFieldClick(field)}>
                        <View className={cx(styles.value, 'wz-mr-18 wz-fs-48')}>{valueText}</View>
                        <View className={styles.iconContainer}>
                            <HImage
                                src={LINK_DIRECT}
                                className={styles.arrowIcon}
                                width={45}
                                height={45}
                            />
                        </View>
                    </View>
                ) : (
                    <Skeleton className={cx(styles.skeletonInput)} animation='wave' />
                )}
            </HealthFormItem>
        );
    };

    const handelContinueUpload = useCallback(
        async (newImages: IPicProps[]) => {
            if (!newImages?.length) return;

            // 1. 设置新图片为pending状态并合并到列表
            const pendingImages = newImages.map(img => ({
                ...img,
                uploadStatus: UploadStatus.PENDING
            }));
            setImgList(prev => [...prev, ...pendingImages]);
            setIsParseResultReady(false);

            try {
                // 2. 上传新图片
                const uploadedImages = await uploadFileToBos(newImages, {
                    count: count - imgList.length, // 计算剩余可上传数量
                    bucketConfName
                });
                setSelectedImgId(uploadedImages[0]?.fileName || '');

                // 3. 更新上传成功的图片状态
                const successImages = uploadedImages.map(img => ({
                    ...img,
                    uploadStatus: UploadStatus.SUCCESS
                }));

                const res = await fetchReportData(patientId || EMPTY_VALUE, uploadedImages);
                if (res?.list?.length) {
                    setExtractMap(prev => ({...prev, ...mergeExtract(res.list)}));
                    const newManualForms = {};
                    for (let i = 0; i < res.list.length; i++) {
                        const item = res.list[i];
                        newManualForms[item.objectId] = {
                            labelType: item.reportTags
                                ? apiToInternal(item.reportTags as ReportTag[])
                                : [],
                            time: item.reportTime ?? '',
                            hospital: item.hospitalName ?? '',
                            remark: ''
                        };
                        // FIXME 百度小程序下，blob与base64的图片都无法使用taro的previewImage预览，所以这里通过获取后端返回的
                        // 在线图片的url来预览。后续小程序支持bdFile数据后，可以删除这段代码，直接使用bdFile进行预览
                        successImages[i].onlineURL = item.image?.[0] ?? {};
                    }
                    setManualForms(prev => ({...prev, ...newManualForms}));
                }
                setImgList(prev => [
                    ...prev.slice(0, -newImages.length), // 保留旧图片
                    ...successImages // 替换为新上传的图片
                ]);
                setIsParseResultReady(true);
            } catch (error) {
                console.error('上传失败:', error);
                setIsParseResultReady(true);

                // 4. 更新上传失败的图片状态
                const failedImages = newImages.map(img => ({
                    ...img,
                    uploadStatus: UploadStatus.FAILED
                }));
                setImgList(prev => [
                    ...prev.slice(0, -newImages.length), // 保留旧图片
                    ...failedImages // 替换为失败状态的图片
                ]);
            }
        },
        [
            setImgList,
            imgList.length,
            setSelectedImgId,
            patientId,
            setExtractMap,
            setIsParseResultReady,
            setManualForms
        ]
    );

    useEffect(() => {
        setCheckedList(reportFormData.labelType);
    }, [reportFormData.hospital, reportFormData.labelType]);

    useEffect(() => {
        if (!isParseResultReady) {
            showToast({
                title: '资料解析中...',
                icon: 'loading'
            });
        } else {
            hideToast();
        }
    }, [isParseResultReady]);

    useEffect(() => {
        // ubc打点逻辑
        ubcCommonViewSend({
            value: 'page',
            page: UBC_FIELD_UPLOAD_PAGE
        });
    }, []);

    return (
        <Portal.provider>
            <CPageContainer
                onBackDetain={onBackDetain}
                backDetain={backDetain}
                topBarProps={{
                    title: ' ',
                    className: 'white',
                    textColor: '#000000',
                    barBg: '#F2F6FF',
                    blank: true,
                    hideHome: true,
                    titleLeftSlot: PAGE_TITLE,
                    // 手百中心入口进来的隐藏返回按钮 @zhangzhiyu03
                    ...(isSwanMsgListScene ? {hideBack: true, isInTabBar: true} : {})
                }}
            >
                <View className={cx(styles.heathReport, 'wz-plr-45')}>
                    <View className={cx(styles.patientWrapper)}>
                        <Text className={styles.uploadTip}>请确认资料解析信息</Text>
                        <View className={cx(styles.patientBasicInfo, 'wz-flex wz-fs-45')}>
                            <Image
                                src={selectedPatient?.icon || AVATAR_DEFAULT}
                                className={cx(styles.patientAvatar, 'wz-mr-30')}
                                mode='widthFix'
                            />
                            <View className={cx(styles.relationship, 'wz-mr-36 wz-fw-700')}>
                                {selectedPatient?.relationship || '本人'}
                            </View>
                            <View className={styles.sex}>{selectedPatient?.gender || '女'}</View>
                            <View className={cx(styles.divider, 'wz-mlr-24')}>｜</View>
                            <View className={styles.age}>{selectedPatient?.age || '35岁'}</View>
                        </View>
                    </View>
                    <View className={cx(styles.reportWrapper, 'wz-mt-45')}>
                        <View className={cx(styles.reportForm, 'wz-br-63')}>
                            <ImageListTools
                                imgList={imgList}
                                statusList={statusList}
                                setImgList={setImgList}
                                setStatusList={setStatusList}
                                onUploadClick={handleOpenPopup}
                                bucketConfName={bucketConfName}
                                selectedImgId={selectedImgId}
                                onImageClick={handleImageClick}
                            />
                            {renderFormItem('资料类型', 'labelType')}
                            {renderFormItem('时间', 'time')}
                            {renderFormItem('就诊医院', 'hospital')}
                            {renderFormItem('备注', 'remark', false, true)}
                        </View>
                    </View>
                    <View className={styles.uploadButtonWrapper}>
                        <View className='wz-flex wz-plr-51 wz-ptb-24'>
                            <HButton
                                className={cx(
                                    styles.confirmButton,
                                    'wz-fs-54 wz-fw-500',
                                    isParseResultReady && styles.active
                                )}
                                text={`确认上传 (${imgList.length})`}
                                disabled={!isParseResultReady}
                                size={45}
                                onClick={handleConfirmUpload}
                            />
                        </View>
                        <SafeArea position='bottom' />
                    </View>
                </View>
            </CPageContainer>
            <DatePicker
                value={reportFormData.time || DEFAULT_TIME}
                show={showDatePicker}
                onClose={() => setShowDatePicker(false)}
                onConfirm={handleDateConfirm}
            />
            <InputPopup
                initialValue={initialValue}
                showPopup={isInputPopupShow}
                title={popupTitle}
                isSingleLine={inputMode}
                onClosePopup={() => setIsInputPopupShow(false)}
                onConfirm={handleInputConfirm}
            />
            <LabelPopup
                openStatus={isLabelPopupShow}
                checkedList={checkedList}
                onChange={setCheckedList}
                onClosePopup={() => {
                    setIsLabelPopupShow(false);
                }}
                onConfirm={handleLabelConfirm}
                title='选择资料类型'
                reportTags={tagsForSelector}
            />
            <Portal.slot />
            <ReportUploadPopup
                open={popupOpenStatus}
                sceneType='portraitUpload'
                onSelectedPics={handelContinueUpload}
                closeUploadPopup={handleClosePopup}
            />
        </Portal.provider>
    );
};
HealthReportUpload.displayName = 'HealthReportUpload';

export default memo(HealthReportUpload);
