import {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {View, Image} from '@tarojs/components';
import cx from 'classnames';
import {SELECTED_ICON} from '../../constants/images';
import {type TagProps} from './index.d';
import styles from './index.module.less';

const TagSelector = memo((props: TagProps) => {
    const {
        columns = 4,
        reportTags = [],
        mode = 'single',
        maxSelection = mode === 'single' ? 1 : 3, // 默认单选1个，多选3个
        defaultSelected = [],
        onChange
    } = props;

    const [selectedTags, setSelectedTags] = useState<number[]>(defaultSelected);

    // 切换标签选中状态 (兼容两种数据类型)
    const toggleTag = useCallback(
        (index: number) => {
            const tag = reportTags[index];

            // 检查是否可编辑（字符串默认可编辑，对象检查editable）
            if (typeof tag !== 'string' && tag.editable === false) return;

            let newSelected: number[];

            if (mode === 'single') {
                // 单选模式
                newSelected = selectedTags.includes(index) ? [] : [index];
            } else {
                // 多选模式
                if (selectedTags.includes(index)) {
                    newSelected = selectedTags.filter(i => i !== index);
                } else if (maxSelection === -1 || selectedTags.length < maxSelection) {
                    newSelected = [...selectedTags, index];
                } else {
                    return; // 已达最大选择数量
                }
            }

            setSelectedTags(newSelected);

            onChange?.(
                newSelected,
                newSelected.map(i => reportTags[i])
            );
        },
        [mode, maxSelection, selectedTags, onChange, reportTags]
    );

    // 标签样式
    const tagStyle = useMemo(
        () => ({
            width: columns === 4 ? '23%' : '48%',
            margin: '1% 1%',
            boxSizing: 'border-box'
        }),
        [columns]
    );

    useEffect(() => {
        setSelectedTags(defaultSelected);
    }, [defaultSelected]);

    // 渲染标签 (兼容两种数据类型)
    const renderTags = useMemo(() => {
        return reportTags?.map((item, index) => {
            const displayText = typeof item === 'string' ? item : item.value;
            const itemKey = typeof item === 'string' ? item : item.code;

            const isSelected = selectedTags.includes(index);

            // 禁用逻辑：
            // 1. 对象类型且editable=false
            // 2. 多选模式且已达最大选择数（maxSelection=-1表示不限制）
            const isDisabled =
                (typeof item !== 'string' && item.editable === false) ||
                (mode === 'multiple' &&
                    maxSelection !== -1 &&
                    selectedTags.length >= maxSelection &&
                    !isSelected);

            return (
                <View
                    className={cx(
                        'wz-flex wz-col-center wz-row-center wz-fs-48 wz-text-center',
                        styles.tag,
                        isSelected ? cx(styles.selected, 'wz-fw-500') : '',
                        isDisabled ? styles.disabled : ''
                    )}
                    style={tagStyle}
                    key={itemKey}
                    onClick={() => !isDisabled && toggleTag(index)}
                >
                    <View>{displayText}</View>
                    {isSelected && (
                        <Image
                            className={styles.selectedIcon}
                            src={SELECTED_ICON}
                            mode='aspectFit'
                        />
                    )}
                </View>
            );
        });
    }, [reportTags, selectedTags, mode, maxSelection, tagStyle, toggleTag]);

    return (
        <View className={cx(styles.tagsContainer, 'wz-flex wz-flex-wrap wz-row-between wz-mt-51')}>
            {renderTags}
        </View>
    );
});

TagSelector.displayName = 'TagSelector';

export default TagSelector;
