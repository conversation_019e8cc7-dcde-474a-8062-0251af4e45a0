export interface IReportTag {
    code: string;
    value: string;
    editable?: boolean;
}

export interface TagProps {
    columns?: 2 | 4; // 每行显示数量
    maxSelection?: number; // 最大可选数量，默认3，设为-1则不限制
    reportTags: IReportTag[];
    mode?: 'single' | 'multiple'; // 单选或多选模式，默认单选
    defaultSelected?: number[]; // 默认选中的标签索引
    checkedList?: IReportTag[];
    onChange?: (
        selected: number[],
        selectedTags: Array<string | IReportTag> // 改为更宽松的类型
    ) => void;
}
