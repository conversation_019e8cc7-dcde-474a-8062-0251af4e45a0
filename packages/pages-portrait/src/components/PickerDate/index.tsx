import React, {memo} from 'react';
import {HPicker} from '@baidu/health-ui';

interface DatePickerProps {
    show: boolean;
    value?: string;
    onClose: () => void;
    onConfirm: (text: string) => void;
    title?: string;
    minDate?: string;
    maxDate?: string;
}

const DatePicker: React.FC<DatePickerProps> = ({
    show,
    value = '',
    onClose,
    onConfirm,
    title = '出生日期',
    minDate = '1900-01-01',
    maxDate = new Date().toISOString().split('T')[0]
}) => {
    // 将日期字符串转换为数组格式，并处理各种可能的输入类型
    const formatDateValue = (dateStr: string | number[] | undefined): number[] => {
        // 如果没有值，返回默认日期 1990-01-01
        if (!dateStr) {
            return [1990, 1, 1];
        }

        // 如果已经是数组，直接返回
        if (Array.isArray(dateStr)) {
            return dateStr;
        }

        // 如果是字符串，进行处理
        if (typeof dateStr === 'string' && dateStr.trim()) {
            try {
                const parts = dateStr.split('-').map(Number);
                if (parts.length === 3 && parts.every(part => !isNaN(part))) {
                    return parts;
                }
            } catch (error) {
                console.warn('日期格式解析失败:', dateStr, error);
            }
        }

        // 其他情况返回默认日期
        return [1990, 1, 1];
    };

    // 处理日期确认回调
    const handleDateConfirm = (values: number[], texts: string[]) => {
        // 将选中的值转换为 YYYY-MM-DD 格式
        if (values && values.length >= 3) {
            const [year, month, day] = values;
            const formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            onConfirm(formattedDate);
        } else if (texts && texts.length >= 3) {
            // 如果values有问题，尝试从texts中解析
            const [yearText, monthText, dayText] = texts;
            const year = parseInt(yearText.replace(/[^\d]/g, ''));
            const month = parseInt(monthText.replace(/[^\d]/g, ''));
            const day = parseInt(dayText.replace(/[^\d]/g, ''));

            if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {
                const formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                onConfirm(formattedDate);
            }
        }
    };

    return (
        <HPicker
            isCascader
            show={show}
            mode='date'
            defaultValue={formatDateValue(value)}
            onClose={onClose}
            onConfirm={handleDateConfirm}
            title={title}
            onChangeData={birthOptions => {
                return birthOptions({minDate, maxDate});
            }}
        />
    );
};

export default memo(DatePicker);
