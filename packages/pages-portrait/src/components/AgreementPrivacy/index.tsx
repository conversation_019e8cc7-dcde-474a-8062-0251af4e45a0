import React, {memo} from 'react';
import {View, Text} from '@tarojs/components';
import {HImage} from '@baidu/health-ui';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';
import {PRIVACY_AGREEMENT_URL_H5, PRIVACY_AGREEMENT_URL_SWAN} from '../../constants/links';
import {ICON_SAFE} from '../../constants/images';

import styles from './index.module.less';

const AgreementPrivacy: React.FC = () => {
    const handlePrivacyAgreementClick = () => {
        let url = '';
        if (process.env.TARO_ENV === 'swan') {
            url = PRIVACY_AGREEMENT_URL_SWAN;
        } else if (process.env.TARO_ENV === 'h5') {
            url = PRIVACY_AGREEMENT_URL_H5;
        }
        if (url) {
            navigate({
                url,
                openType: 'navigate'
            });
        }
    };
    return (
        <View className={styles.privacyAgreement}>
            <HImage
                src={ICON_SAFE}
                width={45}
                height={45}
                className={styles.privacyAgreementIcon}
            />
            <Text>我们将严格保护您的隐私安全，详见</Text>
            <Text className={styles.privacyAgreementLink} onClick={handlePrivacyAgreementClick}>
                《隐私协议》
            </Text>
        </View>
    );
};

export default memo(AgreementPrivacy);
