import React from 'react';
import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import style from './index.module.less';

interface HealthFormItemProps {
    label: string;
    required?: boolean;
    error?: boolean;
    showVerifiedTag?: boolean;
    verifiedText?: string;
    children: React.ReactNode;
    isLast?: boolean;
    onClick?: () => void;
}

const HealthFormItem: React.FC<HealthFormItemProps> = ({
    label,
    required = false,
    error = false,
    showVerifiedTag = false,
    verifiedText = '',
    children,
    isLast = false,
    onClick
}) => {
    return (
        <>
            <View className={cx(style.healthFormRow, {[style.errorRow]: error})} onClick={onClick}>
                <View className={style.labelContainer}>
                    <Text className={style.label}>{label}</Text>
                    {required && <Text className={style.requiredStar}>*</Text>}
                </View>
                <View className={style.contentContainer}>
                    {children}
                    {showVerifiedTag && (
                        <View className={style.verifiedTag}>
                            <Text className={style.tagText}>{verifiedText}</Text>
                        </View>
                    )}
                </View>
            </View>
            {!isLast && (
                <View className={cx(style.medicalReportItemDivider, 'wz-taro-hairline--top')} />
            )}
        </>
    );
};

export default HealthFormItem;
