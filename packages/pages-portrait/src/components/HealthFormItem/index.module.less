.healthFormRow {
    display: flex;
    align-items: center;
    padding: 60px 45px;
    position: relative;
    box-sizing: border-box;
    min-height: 165px;

    &:last-child {
        border-bottom: none;
    }
}

.labelContainer {
    width: 300px;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin-right: 4px;

    .label {
        font-size: 48px;
        line-height: 48px;
    }

    .requiredStar {
        color: #ff5252;
        font-size: 42px;
        line-height: 1;
    }
}

.contentContainer {
    flex: 1;
    display: flex;
    position: relative;
    justify-content: flex-end;
    flex-direction: column;
    align-items: flex-end;
    overflow: visible;
    min-width: 0;
}

.verifiedTag {
    background-color: #00c8c8;
    padding: 3px 12px;
    margin-left: 6px;
    flex-shrink: 0;
    height: 48px;
    width: 117px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9px;

    .tagText {
        color: #fff;
        font-size: 48px;
        line-height: 48px;
    }
}

.medicalReportItemDivider {
    margin: 0 45px;
}
