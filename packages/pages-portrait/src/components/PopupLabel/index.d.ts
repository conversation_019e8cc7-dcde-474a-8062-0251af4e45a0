export interface IReportTag {
    code: string;
    value: string;
}

export interface labelProps {
    openStatus?: boolean;
    columns?: 2 | 4; // 每行显示数量
    maxSelection?: number; // 最大可选数量，默认3
    title: string;
    reportTags: IReportTag[];
    checkedList?: IReportTag[];
    onConfirm?: (selectedTags: IReportTag[]) => void;
    onClosePopup: () => void;
    onChange?: (tags: IReportTag[]) => void;
}

// 定义表单字段类型
export type FormField = 'time' | 'hospital' | 'remark' | 'labelType';
