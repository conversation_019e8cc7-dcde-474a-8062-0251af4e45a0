.popContainer {
    .tag {
        position: relative;
        height: 120px;
        background: #f5f6fa;
        color: #000311;
        border-radius: 45px;
        border: 1px solid transparent;
    }

    .select {
        background: #e5f9f9;
        color: #00c8c8;
        letter-spacing: 0;
        border: 1px solid #00bcbc;
    }

    .selectedIcon {
        position: absolute;
        right: -3px;
        bottom: -3px;
        width: 45px;
        height: 45px;
    }

    .disabled {
        background: #f5f6fa;
        opacity: 0.5;
        pointer-events: none;
    }

    .title {
        color: #1f1f1f;
    }

    .uploadButtonWrapper {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        z-index: 9;

        .confirmButton {
            width: 100%;
            background: linear-gradient(-45deg, #00d3ea 0%, #00cfa3 100%);
            height: 132px;
            color: #fff;
        }
    }
}
