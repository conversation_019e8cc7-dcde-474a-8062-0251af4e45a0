import {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {View} from '@tarojs/components';
import {Popup, SafeArea} from '@baidu/wz-taro-tools-core';
import {HButton} from '@baidu/health-ui';
import cx from 'classnames';

import TagSelector from '../TagSelector';
import type {IReportTag, labelProps} from './index.d';

import styles from './index.module.less';

const LabelPopup = memo((props: labelProps) => {
    const {
        openStatus,
        onClosePopup,
        title = '选择标签',
        checkedList,
        reportTags = [],
        onChange,
        onConfirm
    } = props;

    const [isOpen, setIsOpen] = useState(openStatus);

    const initialIdx = useMemo(() => {
        return checkedList
            ?.map(tag => reportTags.findIndex(t => t.code === tag.code))
            .filter(idx => idx !== -1);
    }, [checkedList, reportTags]);

    const handleClose = useCallback(() => {
        setIsOpen(false);
        onClosePopup?.();
    }, [onClosePopup]);

    const handleTagChange = useCallback(
        (_: number[], newSelected: IReportTag[]) => {
            onChange?.(newSelected);
        },
        [onChange]
    );

    const handleConfirm = useCallback(() => {
        onConfirm?.(checkedList || []);
        handleClose();
    }, [checkedList, onConfirm, handleClose]);

    useEffect(() => {
        setIsOpen(openStatus);
    }, [openStatus]);

    return (
        <Popup
            open={isOpen}
            style={{height: '50vh'}}
            catchMove={false}
            className={styles.popup}
            placement='bottom'
            rounded
            onClose={handleClose}
        >
            <Popup.Close />
            <View className={cx(styles.popContainer, 'wz-plr-51')}>
                <View className={cx(styles.title, 'wz-text-center wz-pt-51 wz-fs-54 wz-fw-500')}>
                    {title}
                </View>
                <TagSelector
                    reportTags={reportTags}
                    columns={2}
                    mode='multiple'
                    maxSelection={3}
                    onChange={handleTagChange}
                    defaultSelected={initialIdx}
                />
                <View className={cx(styles.uploadButtonWrapper)}>
                    <View className='wz-flex wz-plr-51 wz-ptb-24'>
                        <HButton
                            className={cx(styles.confirmButton, 'wz-mlr-51 wz-mtb-24')}
                            text='保存'
                            size={45}
                            onClick={handleConfirm}
                        />
                    </View>
                    <SafeArea position='bottom' />
                </View>
            </View>
        </Popup>
    );
});

LabelPopup.displayName = 'LabelPopup';

export default LabelPopup;
