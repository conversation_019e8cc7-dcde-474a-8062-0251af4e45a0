import {useCallback} from 'react';
import {useSet<PERSON>tom} from 'jotai';
import {View} from '@tarojs/components';
import {WImage, Loading} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';

import {uploadFileToBos} from '../../../../pages-im/src/utils/basicAbility/upload';
import {usePatientData} from '../../hooks/portrait/usePatientData';
import {useExportLabelFetch} from '../../hooks/portrait/useReportData';
import {continueUploadIcon, UploadStatus} from '../../constants/upload';
import {extractMapAtom, manualFormAtom, selectedImgIdAtom} from '../../store/portrait';
import {apiToInternal, mergeExtract} from '../../pages/HealthReportUpload/utils';
import {ReportFormData, ReportTag} from '../../typings/upload';
import {CROSS_CLOSE} from '../../constants/images';
import type {ImageListProps} from './index.d';

import styles from './index.module.less';

const ImageListTools = (props: ImageListProps) => {
    const {
        imgList = [],
        statusList,
        setImgList,
        setStatusList,
        onUploadClick,
        bucketConfName,
        selectedImgId,
        onImageClick
    } = props;

    const {fetchReportData} = useExportLabelFetch();
    const {selectedPatientId} = usePatientData();

    const setExtractMap = useSetAtom(extractMapAtom);
    const setSelectedImgId = useSetAtom(selectedImgIdAtom);
    const setManualForms = useSetAtom(manualFormAtom);

    const handleRetryUpload = useCallback(
        async (item, index) => {
            // 更新状态为上传中
            let newImgList = [...imgList];
            const newStatusList = [...statusList];
            newImgList[index] = {...item, uploadStatus: UploadStatus.PENDING};
            newStatusList[index] = UploadStatus.PENDING;

            setImgList(newImgList);
            setStatusList(newStatusList);

            // 重新上传
            try {
                const picData = await uploadFileToBos([item], {
                    count: 1,
                    bucketConfName
                });
                newImgList = [...newImgList];
                const newStatus = [...newStatusList];
                newImgList[index] = {
                    ...picData[0],
                    uploadStatus: UploadStatus.SUCCESS
                };
                newStatus[index] = UploadStatus.SUCCESS;
                setImgList(newImgList);
                setStatusList(newStatus);
                setSelectedImgId(item);

                const res = await fetchReportData(selectedPatientId || '', [item]);
                if (res?.list?.length) {
                    setExtractMap(prev => ({...prev, ...mergeExtract(res.list)}));
                    // FIXME 百度小程序下，blob与base64的图片都无法使用taro的previewImage预览，所以这里通过获取后端返回的
                    // 在线图片的url来预览。后续小程序支持bdFile数据后，可以删除这段代码，直接使用bdFile进行预览
                    newImgList = [...newImgList];
                    newImgList[index] = {
                        ...newImgList[index],
                        onlineURL: res.list[0]?.image?.[0] ?? {}
                    };
                    const newManualForms = res.list.reduce(
                        (acc, item) => {
                            acc[item.objectId] = {
                                labelType: item.reportTags
                                    ? apiToInternal(item.reportTags as ReportTag[])
                                    : [],
                                time: item.reportTime ?? '',
                                hospital: item.hospitalName ?? '',
                                remark: ''
                            };
                            return acc;
                        },
                        {} as Record<string, ReportFormData>
                    );
                    setImgList(newImgList);
                    setManualForms(prev => ({...prev, ...newManualForms}));
                }
            } catch (error) {
                // 上传失败，保持失败状态
                const newList = [...newImgList];
                const newStatus = [...newStatusList];
                newList[index] = {...item, uploadStatus: UploadStatus.FAILED};
                newStatus[index] = UploadStatus.FAILED;
                setImgList(newList);
                setStatusList(newStatus);
                console.error('上传失败:', error);
            }
        },
        [
            imgList,
            statusList,
            setImgList,
            setStatusList,
            bucketConfName,
            selectedPatientId,
            setExtractMap,
            setManualForms,
            setSelectedImgId
        ]
    );

    const loadComponent = useCallback(item => {
        return (
            <View className={styles.loadingCom}>
                <WImage className={styles.loadingImg} shape='rounded' src={item?.path} />
                <View className={styles.coverView} />
                <Loading className={styles.loadingIcon} color='#cdc6c1' />
            </View>
        );
    }, []);

    const successComponent = useCallback(
        item => {
            const isSelected = item.fileName === selectedImgId;
            return (
                <View
                    className={cx(styles.successCom)}
                    onClick={() => onImageClick?.(item.fileName)}
                >
                    <View className={cx(isSelected && styles.borderSuccess)}></View>
                    <WImage className={styles.imageItem} shape='rounded' src={item?.path} />
                </View>
            );
        },
        [onImageClick, selectedImgId]
    );

    const errorComponent = useCallback(
        (item, idx) => {
            return (
                <View className={styles.errCom} onClick={() => handleRetryUpload(item, idx)}>
                    <WImage className={styles.errImg} shape='rounded' src={item?.path} />
                    <View className={styles.coverView} />
                    <View className={styles.errItext}>上传失败</View>
                </View>
            );
        },
        [handleRetryUpload]
    );

    const renderImg = useCallback(
        (item, idx) => {
            const {uploadStatus} = item;

            switch (uploadStatus) {
                case 'pending':
                    return loadComponent(item);
                case 'success':
                    return successComponent(item);
                case 'failed':
                    return errorComponent(item, idx);
                default:
                    return successComponent(item);
            }
        },
        [errorComponent, loadComponent, successComponent]
    );

    const handleUpload = useCallback(() => {
        onUploadClick?.();
    }, [onUploadClick]);

    const handelDelete = useCallback(
        (item, idx) => {
            const newList = imgList.filter(it => it?.path !== item?.path);
            const newStatus = statusList.filter((_, i) => i !== idx);

            setImgList(newList);
            setStatusList(newStatus);

            if (selectedImgId === item?.fileName) {
                // 如果删的是当前选中图，保证 idx 合法：取删除后同位置或第一张
                const nextIdx = Math.min(idx, newList.length - 1);
                const next = newList[nextIdx] || newList[0];
                setSelectedImgId(next?.fileName ?? '');
            }
        },
        [imgList, statusList, selectedImgId, setImgList, setStatusList, setSelectedImgId]
    );

    return (
        <View className={cx(styles.imageMain, 'wz-flex', 'wz-mlr-36', 'wz-ptb-45')}>
            <View className={styles.scrollContainer}>
                {imgList?.map((item, idx) => {
                    return (
                        <View key={idx} className={cx(styles.imageContainer, 'wz-mr-36')}>
                            {renderImg(item, idx)}
                            <View
                                className={styles.closeIconContainer}
                                onClick={() => handelDelete(item, idx)}
                            >
                                <WImage src={CROSS_CLOSE} className={styles.closeIcon} />
                            </View>
                        </View>
                    );
                })}
                {imgList.length < 9 && (
                    <View
                        className={cx(styles.scrollItem, styles.continueUploadMain, 'wz-flex')}
                        onClick={handleUpload}
                    >
                        <WImage className={styles.continueUpload} src={continueUploadIcon} />
                    </View>
                )}
            </View>
        </View>
    );
};

ImageListTools.displayName = 'ImageListTools';

export default ImageListTools;
