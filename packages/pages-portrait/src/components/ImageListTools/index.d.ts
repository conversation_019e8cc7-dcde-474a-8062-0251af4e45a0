import type {IPicProps, UploadStatusType} from '../../typings/upload';

export interface ImageListProps {
    imgList: IPicProps[];
    statusList: UploadStatusType[];
    setImgList: (imgList: IPicProps[]) => void;
    setStatusList: (statusList: UploadStatusType[]) => void;
    onUploadClick?: () => void;
    bucketConfName: string;
    selectedImgId?: string;
    onImageClick?: (id: string) => void;
}
