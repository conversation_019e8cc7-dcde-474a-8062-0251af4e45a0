.imageMain {
    position: relative;

    .scrollContainer {
        display: flex;
        overflow-x: auto;
        padding: 16px 0;
        -webkit-overflow-scrolling: touch;

        &::-webkit-scrollbar {
            display: none;
        }
    }

    .scrollItem {
        scroll-snap-align: start;
        flex: 0 0 auto;
    }

    .imageContainer {
        position: relative;
        flex-shrink: 0;

        .imageItem {
            width: 100%;
            height: 100%;
            border-radius: 45px;
        }

        .loadingCom,
        .successCom,
        .errCom {
            position: relative;
            width: 256px;
            height: 256px;
            border-radius: 45px;
            box-sizing: border-box;
        }

        .loadingCom {
            .coverView {
                width: 100%;
                height: 100%;
                position: absolute;
                border-radius: 45px;
                left: 0;
                top: 0;
                background-color: rgb(0 0 0 / 50%);
            }

            .loadingIcon {
                position: absolute;
                left: 30%;
                top: 30%;
            }

            .loadingImg {
                width: 100%;
                height: 100%;
                border-radius: 45px;
            }
        }

        .successCom {
            .borderSuccess {
                position: absolute;
                inset: 0;
                border: 6px solid #00c8c8;
                border-radius: 45px;
                box-sizing: border-box;
                z-index: 1;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -30px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 0;
                    height: 0;
                    border-width: 15px;
                    border-style: solid;
                    border-color: transparent;
                    border-top-color: #00c8c8;
                }
            }
        }

        .errCom {
            .coverView {
                width: 100%;
                height: 100%;
                position: absolute;
                border-radius: 45px;
                left: 0;
                top: 0;
                background-color: rgb(0 0 0 / 50%);
            }

            .errItext {
                position: absolute;
                font-size: 42px;
                color: #fff;
                left: 15%;
                top: 40%;
            }

            .errImg {
                width: 100%;
                height: 100%;
                border-radius: 45px;
            }
        }

        .closeIconContainer {
            position: absolute;
            padding: 9px;
            top: -24px;
            right: -24px;
            z-index: 2;
        }

        .closeIcon {
            width: 60px;
            height: 60px;
        }
    }

    .continueUploadMain {
        width: 256px;
        height: 256px;
        justify-content: center;
        background-color: #f5f6fa;
        border-radius: 45px;
        flex-shrink: 0;

        .continueUpload {
            width: 102px;
            height: 102px;
        }
    }
}
