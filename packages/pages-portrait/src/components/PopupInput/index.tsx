/**
 * @file 编辑资料信息组件
 * <AUTHOR>
 */
import {memo, useState, useCallback, useEffect, useMemo} from 'react';
import {View, Text} from '@tarojs/components';
import {Popup, Textarea} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';

import type {InputPopupProps} from './index.d';
import styles from './index.module.less';

const MAX_LENGTH = 250;

const InputPopup = memo((props: InputPopupProps) => {
    const {
        showPopup,
        onClosePopup,
        title,
        onConfirm,
        initialValue = '',
        cancelText = '取消',
        confirmText = '确定',
        showCancel = true,
        showConfirm = true,
        isSingleLine = false
    } = props;
    const [open, setOpen] = useState(showPopup);
    const [value, setValue] = useState('');

    const handleClose = useCallback(() => {
        setOpen(false);
        onClosePopup?.();
    }, [onClosePopup]);

    const handleConfirm = useCallback(() => {
        setOpen(showPopup);
        onConfirm?.(value);
    }, [onConfirm, showPopup, value]);

    useEffect(() => {
        setOpen(showPopup);
        // 每次打开弹窗时重置为初始值
        if (showPopup) {
            setValue(initialValue);
        }
    }, [initialValue, showPopup]);

    // 输入框内容更新
    const handleSetInput = useCallback((val: string) => {
        // 截取MAX_LENGTH个字符,修复textarea maxlength 不准确的问题
        const newVal = val?.slice(0, MAX_LENGTH);
        setValue(newVal);
    }, []);

    const genHeader = useMemo(() => {
        return (
            <View
                className={cx(styles.customHeader, 'wz-flex wz-row-between wz-col-center')}
                onClick={handleClose}
            >
                {showCancel && (
                    <View className={styles.headerButton}>
                        <Text className={cx(styles.cancelText, 'wz-fs-48')}>{cancelText}</Text>
                    </View>
                )}

                <View className={cx(styles.headerTitle, 'wz-fw-500 wz-fs-54')}>
                    <Text className={styles.titleText}>{title}</Text>
                </View>

                {showConfirm && (
                    <View
                        className={cx(styles.headerButton, styles.confirmButton)}
                        onClick={e => {
                            e.stopPropagation();
                            handleConfirm();
                        }}
                    >
                        <Text className={cx(styles.confirmText, 'wz-fs-48')}>{confirmText}</Text>
                    </View>
                )}
            </View>
        );
    }, [showCancel, showConfirm, cancelText, confirmText, title, handleClose, handleConfirm]);

    const genInputWrapper = useMemo(() => {
        return (
            <View className={cx(styles.inputWrapper, 'wz-plr-51 wz-mt-24')}>
                <View className={styles.modifyTextarea}>
                    <View
                        className={cx(
                            styles.textareaBox,
                            'wz-br-36',
                            isSingleLine ? styles.singleLineHeight : styles.multiLineHeight
                        )}
                    >
                        <Textarea
                            className={cx(styles.textareaCon, 'wz-br-36 wz-fs-42')}
                            name='content'
                            value={value}
                            placeholder='请输入'
                            placeholderClass={styles.textareaPlaceholder}
                            maxlength={MAX_LENGTH}
                            cursorSpacing={isSingleLine ? 60 : 120}
                            showConfirmBar={false}
                            adjustPosition={true}
                            onInput={e => {
                                handleSetInput(e?.detail?.value);
                            }}
                            focus={open}
                            cursor={-1}
                            holdKeyboard={true}
                            style={isSingleLine ? {height: '100%'} : undefined}
                        />
                        {!isSingleLine && (
                            <View className={cx(styles.textareaCount, 'wz-fs-42')}>
                                <Text>{value?.length || 0}</Text>/{MAX_LENGTH}
                            </View>
                        )}
                    </View>
                </View>
            </View>
        );
    }, [value, open, handleSetInput, isSingleLine]);

    return (
        <Popup
            open={open}
            catchMove={false}
            className={styles.popup}
            placement='bottom'
            rounded
            onClose={handleClose}
        >
            {genHeader}
            {genInputWrapper}
        </Popup>
    );
});

InputPopup.displayName = 'InputPopup';

export default InputPopup;
