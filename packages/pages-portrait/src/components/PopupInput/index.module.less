.popup {
    .customHeader {
        padding: 54px 51px;
        position: relative;

        .headerButton {
            flex: 0 0 auto;
        }

        .confirmButton {
            padding: 57px 51px;
            margin: -57px -51px;
        }

        .headerTitle {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            color: #272933;
        }

        .cancelText {
            color: #848691;
        }

        .confirmText {
            color: #00c8c8;
        }
    }

    .inputWrapper {
        .modifyTextarea {
            .textareaBox {
                background: #f5f6fa;
                padding: 51px 54px;
                position: relative;
                min-height: 96px;

                .textareaCon {
                    width: 100%;
                    height: 240px;
                }

                .textareaPlaceholder {
                    color: #b7b9c1;
                }

                .textareaCount {
                    text-align: right;
                    color: #b7b9c1;
                    margin-top: 12px;
                }
            }

            .singleLineHeight {
                height: 48px;
            }

            .multiLineHeight {
                min-height: 240px;
            }
        }
    }
}
