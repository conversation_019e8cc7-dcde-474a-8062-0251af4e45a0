import React, {memo} from 'react';
import {HPicker} from '@baidu/health-ui';

interface AddressPickerProps {
    show: boolean;
    value?: string[];
    onClose: () => void;
    onConfirm: (text: string, codes: string[]) => void;
    title?: string;
}

const AddressPicker: React.FC<AddressPickerProps> = ({
    show,
    value = [],
    onClose,
    onConfirm,
    title = '居住城市'
}) => {
    return (
        <HPicker
            show={show}
            mode='region'
            defaultValue={value}
            onChangeData={cityOptions => {
                return cityOptions({
                    useAdcode: false
                });
            }}
            onClose={onClose}
            onConfirm={(codes, text) => onConfirm(text, codes)}
            title={title}
        />
    );
};

export default memo(AddressPicker);
