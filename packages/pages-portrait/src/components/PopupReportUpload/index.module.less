.container {
    width: 100vw;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    max-height: calc(100vh - 320px);
    padding-bottom: 93px;
}

.containerOfH5 {
    max-height: 85vh !important;
}

.uploadSection {
    background: #fff;
    border-radius: 45px;
    font-family: PingFangSC-Regular;
    font-size: 60px;
    color: #007aff;
    letter-spacing: 0;
    text-align: center;
    line-height: 60px;
    overflow: hidden;
    width: 100%;
    flex: 0 0 auto;
}

.operationSection {
    background: #fff;
    border-radius: 45px;
    font-size: 60px;
    color: #007aff;
    height: 180px;
}

.segment {
    background: #e0e0e0;
    height: 1px;
    width: 100%;
}

.footerBtn {
    height: 180px;
    line-height: 180px;
    color: #007aff;
}
