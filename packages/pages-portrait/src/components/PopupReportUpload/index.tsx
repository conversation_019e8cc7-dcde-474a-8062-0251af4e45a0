/*
 * @Author: lijing106
 * @Description: 图片上传引导弹窗组件
 */
import cx from 'classnames';
import {View} from '@tarojs/components';
import {type FC, memo, useCallback, useEffect} from 'react';
import {pxTransform} from '@tarojs/taro';
import {Popup} from '@baidu/wz-taro-tools-core';

import Portal from '../../../../ui-cards-common/Portal'; // monorepo 内部引入暂时使用相对路径，避免与 taro 分端构建冲突；@wanghaoyu08
import {API_HOST} from '../../models/apis/host';
import {BUCKET_NAME, ONLINE_HOST} from '../../constants/common';
import {previewPic} from '../../../../pages-im/src/utils/basicAbility/upload';
import {
    ubcCommonClkSend,
    ubcCommonViewSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';

import type {ReportUploadPopupProps} from './index.d';

import styles from './index.module.less';

const UPLOADTYPEMAP = {
    album: '本地图片',
    camera: '相机拍照'
};

/**
 *
 * @description 上传就诊资料弹窗
 * @param props
 * @returns
 */

const bucketConfName =
    API_HOST && ONLINE_HOST.indexOf(API_HOST) > -1 ? BUCKET_NAME[2] : `${BUCKET_NAME[2]}-test`;
const count = 9;

const ReportUploadPopup: FC<ReportUploadPopupProps> = (props: ReportUploadPopupProps) => {
    const {
        open,
        sceneType,
        uploadOps = ['album', 'camera'],
        onSelectedPics,
        closeUploadPopup
    } = props;

    const handleUpload = useCallback(
        async type => {
            try {
                if (!sceneType) {
                    console.error('[ReportUploadPopup] sceneType is undefined');
                    return;
                }
                ubcCommonClkSend({
                    value: `reportUploadPopup_${type}`,
                    ext: {
                        product_info: {
                            scene: sceneType,
                            uploadType: uploadOps
                        }
                    }
                });

                closeUploadPopup();
                const preData = await previewPic({count: count, bucketConfName, btnType: type});
                if (!preData || !Object.keys(preData)?.length) return;
                onSelectedPics && onSelectedPics(preData, {sceneType});

                ubcCommonClkSend({
                    value: `reportUploadPopup_${type}_completed`,
                    ext: {
                        product_info: {
                            scene: sceneType,
                            uploadType: uploadOps
                        }
                    }
                });
            } catch (error) {
                console.error(error);
            }
        },
        [sceneType, closeUploadPopup, onSelectedPics, uploadOps]
    );

    useEffect(() => {
        open &&
            ubcCommonViewSend({
                value: 'reportUploadPopup',
                ext: {
                    product_info: {
                        scene: sceneType,
                        uploadType: uploadOps
                    }
                }
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open]);

    return (
        <Portal>
            <Popup
                open={open}
                rounded
                placement='bottom'
                style={{
                    maxHeight: '100%',
                    background: 'transparent',
                    zIndex: 1050,
                    borderRadius: `${pxTransform(45)} ${pxTransform(45)} 0 0`
                }}
            >
                <View
                    className={cx(
                        styles.container,
                        process.env.TARO_ENV === 'h5' ? styles.containerOfH5 : '',
                        'wz-plr-36'
                    )}
                >
                    <View className={styles.uploadSection}>
                        {uploadOps &&
                            uploadOps?.length > 0 &&
                            uploadOps.map((uploadType, index) => {
                                const lastIndex = uploadOps.length - 1;
                                return (
                                    <View key={uploadType}>
                                        <View
                                            className={cx(
                                                styles.footerBtn,
                                                'wz-flex wz-col-center wz-row-center'
                                            )}
                                            onClick={() => {
                                                handleUpload(uploadType);
                                            }}
                                        >
                                            {UPLOADTYPEMAP[uploadType]}
                                        </View>
                                        {index !== lastIndex && <View className={styles.segment} />}
                                    </View>
                                );
                            })}
                    </View>
                    <View
                        className={cx(
                            styles.operationSection,
                            'wz-flex wz-col-center wz-row-center wz-mt-30'
                        )}
                        onClick={closeUploadPopup}
                    >
                        取消
                    </View>
                </View>
            </Popup>
        </Portal>
    );
};

ReportUploadPopup.displayName = 'ReportUploadPopup';

export default memo(ReportUploadPopup);
