import type {IPicProps} from '../../typings/upload';
import type {SceneTypeOfParams} from '../../models/services/portrait/sse/index.d';
import type {ActionInfo} from '../../typings/common';

export interface TipItem {
    type: 'text' | 'title' | 'img';
    value: string[];
}

type uploadType = 'album' | 'camera';

type ContentListType<T extends string> = T extends 'img'
    ? {
          desc: string;
          small: string;
      }[]
    : string[];

type ContentItem<T extends 'text' | 'img' | 'title'> = {
    type: T;
    data: {
        value: string;
        list?: ContentListType<T>;
    };
};

export interface CapsulesToolsType {
    type: string;
    text: string;
    icon: string;
    // TODO: 通用 actionInfo 声明待收敛；@wanghaoyu08
    actionInfo: ActionInfo;
    instruction?: {
        title: string;
        content: (ContentItem<'text'> | ContentItem<'img'> | ContentItem<'title'>)[];
    };
    bubbleInfo?: {
        icon: string;
        text: string;
        showTime: number;
    };
}

export interface ReportUploadPopupProps {
    uploadOps?: uploadType[];
    onSelectedPics?: (preData: IPicProps[], {sceneType}: {sceneType: SceneTypeOfParams}) => void;
    tipsData?: CapsulesToolsType['instruction'];
    sceneType: SceneTypeOfParams | undefined;
    open?: boolean;
    closeUploadPopup: () => void;
}
