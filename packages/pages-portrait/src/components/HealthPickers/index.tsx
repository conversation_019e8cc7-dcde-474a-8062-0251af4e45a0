import React, {memo, useMemo} from 'react';
import {HPicker} from '@baidu/health-ui';
import PickerAddress from '../PickerAddress';
import DatePicker from '../PickerDate';

// 只保留性别的静态配置
const STATIC_PICKER_CONFIG = {
    gender: {
        options: [
            [
                {value: '男', text: '男'},
                {value: '女', text: '女'}
            ]
        ],
        label: '性别'
    }
};

type PickerItem = {
    type: string;
    show: boolean;
    value: any;
    onClose: () => void;
    onConfirm: (value: any, codes?: string[]) => void;
};

interface HealthPickersProps {
    pickers: PickerItem[];
    pickerOptions?: any;
    relationshipOptions?: any;
}

const HealthPickers: React.FC<HealthPickersProps> = ({
    pickers,
    pickerOptions = {},
}) => {
    const getValueByText = useMemo(
        () =>
            (type: string, valueOrText: string): any[] => {
                if (!valueOrText) return [];

                // 根据类型获取选项数据
                switch (type) {
                    case 'gender': {
                        const options = STATIC_PICKER_CONFIG.gender.options[0];
                        const genderOption = options.find(item => item.text === valueOrText);
                        return genderOption ? [genderOption.value] : [];
                    }

                    case 'relation': {
                        const options = pickerOptions.relationships || [];
                        const relationOption = options.find(item => item.value === valueOrText);
                        return relationOption ? [relationOption.value] : [];
                    }

                    case 'maritalStatus': {
                        const options = pickerOptions.marital || [];
                        const maritalOption = options.find(item => item.value === valueOrText);
                        return maritalOption ? [maritalOption.value] : [];
                    }

                    case 'educationLevel': {
                        const options = pickerOptions.education || [];
                        const educationOption = options.find(item => item.value === valueOrText);
                        return educationOption ? [educationOption.value] : [];
                    }

                    default:
                        return [];
                }
            },
        [pickerOptions]
    );

    const getPickerConfig = useMemo(
        () => (type: string) => {
            switch (type) {
                case 'gender':
                    return STATIC_PICKER_CONFIG.gender;
                case 'relation':
                    return {
                        options: [
                            (pickerOptions.relationships || []).map(item => ({
                                value: item.value,
                                text: item.text
                            }))
                        ],
                        label: '成员关系'
                    };
                case 'maritalStatus':
                    return {
                        options: [
                            (pickerOptions.marital || []).map(item => ({
                                value: item.value,
                                text: item.text
                            }))
                        ],
                        label: '婚姻状况'
                    };
                case 'educationLevel':
                    return {
                        options: [
                            (pickerOptions.education || []).map(item => ({
                                value: item.value,
                                text: item.text
                            }))
                        ],
                        label: '教育程度'
                    };

                default:
                    return null;
            }
        },
        [pickerOptions]
    );

    return (
        <>
            {pickers.map(picker => {
                const {type, show, value, onClose, onConfirm} = picker;
                const config = getPickerConfig(type);

                let pickerValue = [];
                let pickerDefaultValue = [];

                if (type === 'residenceCity') {
                    // 地址选择器使用城市名称数组进行回显
                    pickerValue = Array.isArray(value) ? value : [];
                    pickerDefaultValue = pickerValue;
                } else {
                    // 其他选择器根据文本获取对应的value值
                    pickerValue = getValueByText(type, value);
                    pickerDefaultValue = getValueByText(type, value);
                }

                switch (type) {
                    case 'birthDate':
                        return (
                            <DatePicker
                                key={type}
                                show={show}
                                value={value}
                                onClose={onClose}
                                onConfirm={text => onConfirm(text)}
                            />
                        );

                    case 'residenceCity':
                        return (
                            <PickerAddress
                                key={type}
                                show={show}
                                value={value} // 传递城市名称数组 ['省', '市', '区']
                                onClose={onClose}
                                onConfirm={(text, codes) => onConfirm(text, codes)}
                            />
                        );

                    default:
                        if (
                            !config ||
                            !config.options ||
                            !config.options[0] ||
                            config.options[0].length === 0
                        ) {
                            console.error(`${type} picker 配置错误:`, config);
                            return null;
                        }

                        return (
                            <HPicker
                                key={type}
                                show={show}
                                options={config.options}
                                value={pickerValue}
                                defaultValue={pickerDefaultValue}
                                onClose={onClose}
                                onConfirm={(selectedValues, selectedTexts) => {
                                    // 根据不同类型处理返回值
                                    let valueToSubmit = '';
                                    if (type === 'maritalStatus' || type === 'educationLevel') {
                                        // 对于婚姻状况和教育程度，提交后端的value值
                                        valueToSubmit =
                                            selectedValues && selectedValues.length > 0
                                                ? selectedValues[0]
                                                : '';
                                    } else {
                                        // 对于其他类型，提交选中的文本
                                        valueToSubmit =
                                            selectedTexts && selectedTexts.length > 0
                                                ? selectedTexts[0]
                                                : '';
                                    }
                                    onConfirm(valueToSubmit);
                                }}
                                title={config.label}
                            />
                        );
                }
            })}
        </>
    );
};

export default memo(HealthPickers);
