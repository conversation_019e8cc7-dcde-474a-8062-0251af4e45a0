import {atom} from 'jotai';
import {PopupConfig} from './popupAtom';

// Z-Index 基础值配置
export const Z_INDEX_CONFIG = {
    BASE: 1000, // 基础弹窗层级
    STEP: 10 // 每层递增步长
};

// 当前最高层级 atom
export const currentMaxZIndexAtom = atom(Z_INDEX_CONFIG.BASE);

// 分配新的 Z-Index 的 action atom
export const allocateZIndexAtom = atom(null, (get, set) => {
    const currentMax = get(currentMaxZIndexAtom);
    const newZIndex = currentMax + Z_INDEX_CONFIG.STEP;
    set(currentMaxZIndexAtom, newZIndex);
    return newZIndex;
});

// 重置 Z-Index 的 action atom（当所有弹窗关闭时）
export const resetZIndexAtom = atom(null, (get, set) => {
    set(currentMaxZIndexAtom, Z_INDEX_CONFIG.BASE);
});

// 多层弹窗状态接口
export interface MultiPopupState {
    id: string;
    config: PopupConfig;
    isVisible: boolean;
    zIndex: number;
    data?: any;
    parentId?: string; // 父弹窗ID（用于层级关系）
}

// 活跃弹窗列表 atom（支持多个同时显示）
export const activePopupsAtom = atom<MultiPopupState[]>([]);

// 显示多层弹窗的 action atom
export const showMultiPopupAtom = atom(null, (get, set, config: PopupConfig, parentId?: string) => {
    const activePopups = get(activePopupsAtom);

    // 分配新的 Z-Index
    const zIndex = config.zIndex || set(allocateZIndexAtom);

    const newPopup: MultiPopupState = {
        id: `popup_${Date.now()}_${Math.random()}`,
        config,
        isVisible: true,
        zIndex,
        data: config.props,
        parentId
    };

    if (config.allowMultiple) {
        // 支持多层弹窗，添加到活跃列表
        set(activePopupsAtom, [...activePopups, newPopup]);
    } else {
        // 不支持多层，替换当前弹窗
        set(activePopupsAtom, [newPopup]);
    }

    return newPopup.id;
});

// 关闭多层弹窗的 action atom
export const closeMultiPopupAtom = atom(null, (get, set, popupId?: string) => {
    const activePopups = get(activePopupsAtom);
    let tempID = popupId;
    if (!popupId) {
        // 关闭最顶层弹窗
        const topPopup = activePopups.reduce((top, popup) =>
            popup.zIndex > top.zIndex ? popup : top
        );
        if (topPopup) {
            tempID = topPopup.id;
        }
    }

    const updatedPopups = activePopups.filter(popup => {
        // 关闭指定弹窗及其子弹窗
        return popup.id !== tempID && popup.parentId !== tempID;
    });

    set(activePopupsAtom, updatedPopups);

    // 如果没有活跃弹窗，重置 Z-Index
    if (updatedPopups.length === 0) {
        set(resetZIndexAtom);
    }
});

// 获取顶层弹窗的派生 atom
export const topPopupAtom = atom(get => {
    const activePopups = get(activePopupsAtom);
    if (activePopups.length === 0) return null;

    return activePopups.reduce((top, popup) => (popup.zIndex > top.zIndex ? popup : top));
});
