// atoms/derivedAtoms.ts
import {atom} from 'jotai';
import dayjs from 'dayjs';
// import {MedicalReport} from '../../typings/patient';
import type {MedicalReport} from '../../typings/patient';
import {medicalReportsAtom} from './patientAtoms';
import {
    historyPatientsAtom,
    historyPatientsBarCloseCountAtom,
    historyPatientsPopupLastCloseTimeAtom,
    portraitIntroPopupHasShowAtom,
    portraitIntroPopupVisibleAtom
} from '.';

// 按时间进行归类与排序的就诊资料
export const sortedMedicalReportsAtom = atom(get => {
    const reports = get(medicalReportsAtom);
    const sortedReports: MedicalReport[][] = [];
    for (let i = 0; i < reports.length; i++) {
        if (sortedReports.length === 0) {
            sortedReports.push([]);
        }
        const lastSortedReportItem = sortedReports[sortedReports.length - 1];
        if (Array.isArray(lastSortedReportItem) && lastSortedReportItem.length === 0) {
            lastSortedReportItem.push(reports[i]);
            continue;
        }

        const item = reports[i];
        const date1 = dayjs(
            lastSortedReportItem[lastSortedReportItem.length - 1].createTime * 1000
        );
        const date2 = dayjs(item.createTime * 1000);
        if (!date1.isSame(date2, 'day')) {
            sortedReports.push([]);
        }
        sortedReports[sortedReports.length - 1].push(item);
    }
    return sortedReports;
});

// 按报告类型分组的就诊资料
export const reportsByTypeAtom = atom(() => {
    // TODO: 按报告类型分组
    // const reports = get(medicalReportsAtom);
    // return reports.reduce(
    //     (acc, report) => {
    //         if (!acc[report.reportType]) {
    //             acc[report.reportType] = [];
    //         }
    //         acc[report.reportType].push(report);
    //         return acc;
    //     },
    //     {} as Record<string, MedicalReport[]>
    // );
});

// 最新的就诊资料
export const latestMedicalReportAtom = atom(get => {
    const reports = get(sortedMedicalReportsAtom);
    return reports[0] || null;
});

// 健康史完成度统计
export const healthHistoryStatsAtom = atom(() => {
    // TODO: 健康史完成度统计
    // const healthHistory = get(healthHistoryAtom);
    // const stats = healthHistory.map(category => {
    //     const totalItems = category.items.length;
    //     const completedItems = category.items.filter(item => item.status === '已完成').length;
    //     const completionRate = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
    //     return {
    //         categoryId: category.healthCategoryId,
    //         categoryName: category.categoryName,
    //         totalItems,
    //         completedItems,
    //         completionRate,
    //     };
    // });
    // return stats;
});

// 整体健康档案完成度
export const overallHealthCompletionAtom = atom(() => {
    // TODO: 整体健康档案完成度
    // const stats = get(healthHistoryStatsAtom);
    // if (stats.length === 0) return 0;
    // const totalCompletion = stats.reduce((sum, stat) => sum + stat.completionRate, 0);
    // return Math.round(totalCompletion / stats.length);
});

// 历史就诊人条是否展示
const HISTORY_PATIENTS_BAR_CLOSE_COUNT_MAX = 3;
export const historyPatientsBarVisibleAtom = atom(get => {
    const historyPatientsBarCloseCount = get(historyPatientsBarCloseCountAtom);
    const historyPatients = get(historyPatientsAtom);
    return (
        historyPatients.length > 0 &&
        Number(historyPatientsBarCloseCount) < HISTORY_PATIENTS_BAR_CLOSE_COUNT_MAX
    );
});

// 档案介绍弹窗是否展示
export const portraitIntroPopupVisibleOnceAtom = atom(get => {
    const portraitIntroPopupHasShow = get(portraitIntroPopupHasShowAtom);
    const portraitIntroPopupVisible = get(portraitIntroPopupVisibleAtom);
    return portraitIntroPopupVisible && !portraitIntroPopupHasShow;
});

// 历史就诊人弹窗是否自动打开
// 如果最近一次关闭时间距离现在超过1天，则自动打开
export const historyPatientsPopupAutoOpenAtom = atom(get => {
    const historyPatients = get(historyPatientsAtom);
    if (historyPatients.length === 0) {
        return false;
    }
    const lastCloseTime = get(historyPatientsPopupLastCloseTimeAtom);
    const now = Date.now();
    const diff = now - Number(lastCloseTime);
    return diff > 1000 * 60 * 60 * 24;
});
