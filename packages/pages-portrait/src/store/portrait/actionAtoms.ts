import {atom} from 'jotai';
import {Patient, MedicalReport, HealthCategory, HealthItem} from '../../typings/patient';
import {RelationshipOption, ReportTagOption} from '../../typings/portrait';
import {
    patientsAtom,
    selectedPatientIdAtom,
    medicalReportsAtom,
    healthHistoryAtom,
    
} from './patientAtoms';
import {
    historyPatientsAtom,
    PATIENT_INFO_TAB_INDEX_DEFAULT,
    PATIENT_INFO_TAB_INDEX_HEALTH_HISTORY,
    patientInfoTabIndexAtom,
    relationshipOptionsAtom,
    reportTagOptionsAtom,
    portraitIntroPopupVisibleAtom,
    portraitIntroPopupHasShowAtom,
    hasSelfAtom
} from './index';

// 患者管理操作
// 添加患者
export const addPatientAtom = atom(null, (get, set, newPatient: Patient) => {
    const patients = get(patientsAtom);
    set(patientsAtom, [...patients, newPatient]);
});

// 更新患者
export const updatePatientAtom = atom(null, (get, set, updatedPatient: Patient) => {
    const patients = get(patientsAtom);
    const updatedPatients = patients.map(patient =>
        patient.patientId === updatedPatient.patientId ? updatedPatient : patient
    );
    set(patientsAtom, updatedPatients);
});

// 删除患者
export const removePatientAtom = atom(null, (get, set, patientId: string) => {
    const patients = get(patientsAtom);
    const updatedPatients = patients.filter(patient => patient.patientId !== patientId);
    set(patientsAtom, updatedPatients);

    const selectedId = get(selectedPatientIdAtom);
    if (selectedId === patientId) {
        set(selectedPatientIdAtom, undefined);
    }
});

// 就诊资料操作
// 批量添加就诊资料
export const addMedicalReportsAtom = atom(null, (get, set, newReports: MedicalReport[]) => {
    const reports = get(medicalReportsAtom);
    set(medicalReportsAtom, [...reports, ...newReports]);
});

// 添加就诊资料
export const addMedicalReportAtom = atom(null, (get, set, newReport: MedicalReport) => {
    const reports = get(medicalReportsAtom);
    set(medicalReportsAtom, [...reports, newReport]);
});

// 更新就诊资料
export const updateMedicalReportAtom = atom(
    null,
    (
        get,
        set,
        {reportId, updatedReport}: {reportId: string; updatedReport: Partial<MedicalReport>}
    ) => {
        const reports = get(medicalReportsAtom);
        const updatedReports = reports.map(report =>
            report.medicalReportId === reportId ? {...report, ...updatedReport} : report
        );
        set(medicalReportsAtom, updatedReports);
    }
);

// 删除就诊资料
export const removeMedicalReportAtom = atom(null, (get, set, reportId: string) => {
    const reports = get(medicalReportsAtom);
    const updatedReports = reports.filter(report => report.medicalReportId !== reportId);
    set(medicalReportsAtom, updatedReports);
});

// 健康史操作
// 添加健康史分类
export const addHealthCategoryAtom = atom(null, (get, set, newCategory: HealthCategory) => {
    const healthHistory = get(healthHistoryAtom);
    set(healthHistoryAtom, [...healthHistory, newCategory]);
});

// 更新健康史分类
export const updateHealthCategoryAtom = atom(
    null,
    (
        get,
        set,
        {
            categoryId,
            updatedCategory
        }: {categoryId: string; updatedCategory: Partial<HealthCategory>}
    ) => {
        const healthHistory = get(healthHistoryAtom);
        const updatedHistory = healthHistory.map(category =>
            category.healthCategoryId === categoryId ? {...category, ...updatedCategory} : category
        );
        set(healthHistoryAtom, updatedHistory);
    }
);

// 删除健康史分类
export const removeHealthCategoryAtom = atom(null, (get, set, categoryId: string) => {
    const healthHistory = get(healthHistoryAtom);
    const updatedHistory = healthHistory.filter(
        category => category.healthCategoryId !== categoryId
    );
    set(healthHistoryAtom, updatedHistory);
});

// 健康史Item操作
// 添加健康史Item
export const addHealthItemAtom = atom(
    null,
    (get, set, {categoryId, newItem}: {categoryId: string; newItem: HealthItem}) => {
        const healthHistory = get(healthHistoryAtom);
        const updatedHistory = healthHistory.map(category =>
            category.healthCategoryId === categoryId
                ? {...category, items: [...category.items, newItem]}
                : category
        );
        set(healthHistoryAtom, updatedHistory);
    }
);

// 更新健康史Item
export const updateHealthItemAtom = atom(
    null,
    (
        get,
        set,
        {
            categoryId,
            itemId,
            updatedItem
        }: {
            categoryId: string;
            itemId: string;
            updatedItem: Partial<HealthItem>;
        }
    ) => {
        const healthHistory = get(healthHistoryAtom);
        const updatedHistory = healthHistory.map(category =>
            category.healthCategoryId === categoryId
                ? {
                      ...category,
                      items: category.items.map(item =>
                          item.key === itemId ? {...item, ...updatedItem} : item
                      )
                  }
                : category
        );
        set(healthHistoryAtom, updatedHistory);
    }
);

// 删除健康史Item
export const removeHealthItemAtom = atom(
    null,
    (get, set, {categoryId, itemId}: {categoryId: string; itemId: string}) => {
        const healthHistory = get(healthHistoryAtom);
        const updatedHistory = healthHistory.map(category =>
            category.healthCategoryId === categoryId
                ? {...category, items: category.items.filter(item => item.key !== itemId)}
                : category
        );
        set(healthHistoryAtom, updatedHistory);
    }
);

// 设置历史患者数据
export const setHistoryPatientsAtom = atom(null, (get, set, newPatients: Patient[]) => {
    set(historyPatientsAtom, newPatients);
});

// 设置就诊人关系选项
export const setRelationshipOptionsAtom = atom(
    null,
    (get, set, newRelationshipOptions: RelationshipOption[]) => {
        set(relationshipOptionsAtom, newRelationshipOptions);
    }
);

// 设置是否有就诊人档案
export const setHasSelfAtom = atom(null, (get, set, newHasSelf) => {
    set(hasSelfAtom, newHasSelf);
});

// 设置就诊资料标签
export const setReportTagOptionsAtom = atom(
    null,
    (get, set, newReportTagOptions: ReportTagOption[]) => {
        set(reportTagOptionsAtom, newReportTagOptions);
    }
);

// 设置就诊人信息tabIndex为就诊资料夹
export const setPatientInfoTabToMedicalReportsAtom = atom(null, (get, set) => {
    set(patientInfoTabIndexAtom, PATIENT_INFO_TAB_INDEX_DEFAULT);
});

// 设置就诊人信息tabIndex为健康史
export const setPatientInfoTabToHealthHistoryAtom = atom(null, (get, set) => {
    set(patientInfoTabIndexAtom, PATIENT_INFO_TAB_INDEX_HEALTH_HISTORY);
});

// 隐藏档案功能介绍弹窗
export const hidePortraitIntroPopupAtom = atom(null, (get, set) => {
    set(portraitIntroPopupVisibleAtom, false);
    set(portraitIntroPopupHasShowAtom, true);
});
