import {atom} from 'jotai';
import {Patient, MedicalReport, HealthCategory} from '../../typings/patient';
import {calculateBMI} from '../../utils';

// 患者数据原子
export const patientsAtom = atom<Patient[]>([]);

// 当前选中的患者ID原子
export const selectedPatientIdAtom = atom<string | undefined>(undefined);

// 当前选中的患者 - 派生原子
export const selectedPatientAtom = atom(
    get => {
        const patients = get(patientsAtom);
        const selectedId = get(selectedPatientIdAtom);
        return patients.find(patient => patient.patientId === selectedId) || null;
    },
    (get, set, newPatient: Patient | null) => {
        if (!newPatient) return;

        const patients = get(patientsAtom);
        const updatedPatients = patients.map(patient =>
            patient.patientId === newPatient.patientId ? newPatient : patient
        );
        set(patientsAtom, updatedPatients);
    }
);

// 患者基本信息原子
export const patientBasicInfoAtom = atom(
    get => {
        const patient = get(selectedPatientAtom);
        if (!patient) return null;

        return {
            patientId: patient.patientId,
            name: patient.name,
            age: patient.age,
            gender: patient.gender,
            height: patient.height,
            weight: patient.weight,
            bmi: patient.bmi ?? calculateBMI(patient.height ?? 0, patient.weight ?? 0),
            area: patient.area,
            relationship: patient.relationship,
            isCertified: patient.isCertified,
            isSelf: patient.isSelf,
            createTime: patient.createTime,
            avatar: patient.avatar
        };
    },
    (get, set, newInfo: Partial<Patient>) => {
        const patient = get(selectedPatientAtom);
        if (!patient) return;

        // 自动计算BMI
        const updatedInfo = {...newInfo};
        const {height, weight} = newInfo;
        if (updatedInfo.bmi) {
            updatedInfo.bmi = calculateBMI(height ?? 0, weight ?? 0);
        }

        const updatedPatient = {...patient, ...updatedInfo};
        set(selectedPatientAtom, updatedPatient);
    }
);

// 就诊资料原子 - 派生原子
export const medicalReportsAtom = atom(
    get => {
        const patient = get(selectedPatientAtom);
        return patient?.medicalReports || [];
    },
    (get, set, newReports: MedicalReport[]) => {
        const patient = get(selectedPatientAtom);
        if (!patient) return;

        const updatedPatient = {...patient, medicalReports: newReports};
        set(selectedPatientAtom, updatedPatient);
    }
);

// 健康史原子
export const healthHistoryAtom = atom(
    get => {
        const patient = get(selectedPatientAtom);
        return patient?.healthHistory || [];
    },
    (get, set, newHealthHistory: HealthCategory[]) => {
        const patient = get(selectedPatientAtom);
        if (!patient) return;

        const updatedPatient = {...patient, healthHistory: newHealthHistory};
        set(selectedPatientAtom, updatedPatient);
    }
);

// 当前选中的健康史分类
export const selectedHealthCategoryAtom = atom<string | null>(null);

// 当前选中的健康史分类数据
export const currentHealthCategoryAtom = atom(get => {
    const healthHistory = get(healthHistoryAtom);
    const selectedCategoryId = get(selectedHealthCategoryAtom);
    return healthHistory.find(category => category.healthCategoryId === selectedCategoryId) || null;
});
