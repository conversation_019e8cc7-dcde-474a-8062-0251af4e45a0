import {atom, createStore} from 'jotai';
import {atomWithStorage} from 'jotai/utils';
import {Patient} from '../../typings/patient';
import {IntroPopupData, RelationshipOption, ReportTagOption} from '../../typings/portrait';
import type {IPicProps, ReportFormData, ReportTag, UploadStatusType} from '../../typings/upload';
import {createStorageAdapter} from '../../utils';
import {PAGE_TITLE} from '../../constants/common';
import {IList} from '../../models/services/portrait/healthReport/index.d';
import {apiToInternal} from '../../pages/HealthReportUpload/utils';

export const portraitAtomStore = createStore();

// 档案主页面标题原子
export const titleAtom = atom<string>(PAGE_TITLE);

// 页面渲染阶段原子
// 用于细粒度控制页面渲染
export const PAGE_RENDER_STAGE_UNRENDERED = 0; // 未渲染
export const PAGE_RENDER_STAGE_RENDERING = 1; // 请求主接口数据或渲染中
export const PAGE_RENDER_STAGE_RENDERED = 2; // 渲染完成

type PageRenderStage =
    | typeof PAGE_RENDER_STAGE_UNRENDERED
    | typeof PAGE_RENDER_STAGE_RENDERING
    | typeof PAGE_RENDER_STAGE_RENDERED;

export const pageRenderStageAtom = atom<PageRenderStage>(PAGE_RENDER_STAGE_UNRENDERED);

export const pageRenderStageSetAtom = atom(null, (get, set, stage: PageRenderStage) => {
    set(pageRenderStageAtom, stage);
});

// 是否有本人就诊人原子 
export const hasSelfAtom = atom(false);

// 档案主页就诊人信息tabIndex原子
export const PATIENT_INFO_TAB_INDEX_DEFAULT = 0;
export const PATIENT_INFO_TAB_INDEX_HEALTH_HISTORY = 1;
export const patientInfoTabIndexAtom = atom<number>(PATIENT_INFO_TAB_INDEX_DEFAULT);

// 资料上传图片列表
export const imgListAtom = atom<IPicProps[]>([]);
// 选中图片的id
export const selectedImgIdAtom = atom<string>('');
// 上传资料后的识别结果缓存
export const extractMapAtom = atom<Record<string, IList>>({});
// 上传报告单后，是否解析出标签
export const isParseResultReadyAtom = atom(false);

// 表单原子，根据图片的id更新表单展示
export const manualFormAtom = atom<Record<string, ReportFormData>>({});
// 表单派生原子
export const reportFormDataAtom = atom(get => {
    const id = get(selectedImgIdAtom);
    const map = get(extractMapAtom);
    const manualMap = get(manualFormAtom);

    const apiItem = id ? map[id] : null;

    const defaultVal: ReportFormData = {
        labelType: apiItem?.reportTags ? apiToInternal(apiItem.reportTags as ReportTag[]) : [],
        time: apiItem?.reportTime ?? '',
        hospital: apiItem?.hospitalName ?? '',
        remark: ''
    };

    // 如果有手动修改，以手动为准
    return manualMap[id] ?? defaultVal;
});

export const updateManualFormAtom = atom(
    null, // 无读逻辑
    (get, set, patch: Partial<ReportFormData> & {imgId?: string}) => {
        const id = get(selectedImgIdAtom);
        if (!id) return;

        const old = get(manualFormAtom);
        set(manualFormAtom, {
            ...old,
            [id]: {...get(reportFormDataAtom), ...patch}
        });
        // 同时更新 extractMap
        const extractMap = get(extractMapAtom);
        set(extractMapAtom, {
            ...extractMap,
            [id]: {...(extractMap[id] || {}), ...patch}
        });
    }
);

// 资料上传图片状态
export const statusListAtom = atom<UploadStatusType[]>([]);

// 历史就诊人数据原子
export const historyPatientsAtom = atom<Patient[]>([]);
// 历史就诊人Bar是否显示原子
export const historyPatientsBarCloseCountAtom = atomWithStorage<string>(
    'historyPatientsBarCloseCount',
    '0',
    createStorageAdapter()
);
export const historyPatientsBarCloseCountSetAtom = atom(null, (get, set, count: string) => {
    set(historyPatientsBarCloseCountAtom, count);
});
// 关联历史就诊人模态框是否自动打开原子
export const historyPatientsPopupAutoOpenAtom = atomWithStorage<boolean>(
    'historyPatientsPopupAutoOpen',
    false,
    createStorageAdapter()
);

// 历史就诊人弹窗最近一次关闭时间原子
export const historyPatientsPopupLastCloseTimeAtom = atomWithStorage<string>(
    'historyPatientsPopupLastCloseTime',
    String(Date.now() - 1000 * 60 * 60 * 24 * 30), // 默认时间为30天前
    createStorageAdapter()
);
export const historyPatientsPopupLastCloseTimeSetAtom = atom(null, (get, set, time: string) => {
    set(historyPatientsPopupLastCloseTimeAtom, time);
});

// 档案相关配置原子
// 就诊人关系选项
export const relationshipOptionsAtom = atom<RelationshipOption[]>([]);

// 就诊资料标签
export const reportTagOptionsAtom = atom<ReportTagOption[]>([]);

// 档案功能介绍弹窗是否展示过原子
export const portraitIntroPopupHasShowAtom = atomWithStorage<boolean>(
    'portraitIntroPopupHasShow',
    false,
    createStorageAdapter()
);
// 档案功能介绍弹窗展示
export const portraitIntroPopupHasShowSetAtom = atom(null, (get, set, hasShow: boolean) => {
    set(portraitIntroPopupHasShowAtom, hasShow);
});

// 档案功能介绍弹窗是否展示原子
export const portraitIntroPopupVisibleAtom = atom<boolean>(false);
// 档案功能介绍弹窗是否展示原子设置方法
export const portraitIntroPopupVisibleSetAtom = atom(null, (get, set, visible: boolean) => {
    set(portraitIntroPopupVisibleAtom, visible);
});

// 档案功能介绍弹窗数据原子
export const introPopupDataAtom = atom<IntroPopupData>({
    title: '',
    content: [],
    show: false
});

export const introPopupDataSetAtom = atom(null, (get, set, data: IntroPopupData) => {
    set(introPopupDataAtom, data);
});
