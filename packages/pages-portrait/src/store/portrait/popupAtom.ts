import {atom} from 'jotai';

// 弹窗类型枚举
export enum PopupType {
    CREATE_PATIENT = 'CREATE_PATIENT', // 创建成员档案
    HISTORY_SYNC = 'HISTORY_SYNC', // 历史信息同步
    SUCCESS_CREATE = 'SUCCESS_CREATE', // 成功创建健康档案
    PATIENT_FORM = 'PATIENT_FORM',
    RELATIONSHIP_SELECT = 'RELATIONSHIP_SELECT',
    HISTORY_PATIENT_SELECT = 'HISTORY_PATIENT_SELECT',
    HEALTH_MANAGEMENT_GUIDE = 'HEALTH_MANAGEMENT_GUIDE'
}

// 弹窗配置接口
export interface PopupConfig {
    type: PopupType;
    title?: string;
    props?: Record<string, any>;
    autoClose?: number; // 自动关闭时间(ms)
    onClose?: () => void;
    onSuccess?: (data?: any) => void;
    priority?: number; // 优先级，数字越大优先级越高
    placement?: 'bottom' | 'center' | 'top';
    height?: string;
    style?: Record<string, any>;
    titleStyle?: Record<string, any>;
    catchMove?: boolean;
    rounded?: boolean;
    allowMultiple?: boolean; // 是否允许多层弹窗同时存在
    zIndex?: number; // 自定义层级
}

// 弹窗状态接口
export interface PopupState {
    id: string;
    config: PopupConfig;
    isVisible: boolean;
    data?: any; // 弹窗携带的数据
}

// 弹窗队列 atom
export const popupQueueAtom = atom<PopupState[]>([]);

// 当前显示的弹窗 atom
export const currentPopupAtom = atom<PopupState | null>(null);

// 弹窗计数器，用于生成唯一ID
let popupCounter = 0;

// 显示弹窗的 action atom
export const showPopupAtom = atom(null, (get, set, config: PopupConfig) => {
    const queue = get(popupQueueAtom);
    const current = get(currentPopupAtom);

    const newPopup: PopupState = {
        id: `popup_${++popupCounter}`,
        config,
        isVisible: false,
        data: config.props
    };

    // 如果当前没有弹窗显示，直接显示
    if (!current) {
        set(currentPopupAtom, {...newPopup, isVisible: true});

        // 设置自动关闭定时器
        if (config.autoClose) {
            setTimeout(() => {
                set(closePopupAtom, newPopup.id);
            }, config.autoClose);
        }

        return newPopup.id;
    }

    // 如果有优先级设置，按优先级插入队列
    if (config.priority !== undefined) {
        const insertIndex = queue.findIndex(
            popup => (popup.config.priority || 0) < config.priority!
        );
        if (insertIndex === -1) {
            set(popupQueueAtom, [...queue, newPopup]);
        } else {
            const newQueue = [...queue];
            newQueue.splice(insertIndex, 0, newPopup);
            set(popupQueueAtom, newQueue);
        }
    } else {
        // 没有优先级，添加到队列末尾
        set(popupQueueAtom, [...queue, newPopup]);
    }

    return newPopup.id;
});

// 关闭弹窗的 action atom
export const closePopupAtom = atom(null, (get, set, popupId?: string) => {
    const current = get(currentPopupAtom);
    const queue = get(popupQueueAtom);

    if (!current) return;

    // 如果指定了 popupId，只关闭指定的弹窗
    if (popupId && current.id !== popupId) {
        // 从队列中移除指定弹窗
        const newQueue = queue.filter(popup => popup.id !== popupId);
        set(popupQueueAtom, newQueue);
        return;
    }

    // 执行当前弹窗的 onClose 回调
    current.config.onClose?.();

    // 显示队列中的下一个弹窗
    if (queue.length > 0) {
        const [nextPopup, ...restQueue] = queue;
        set(currentPopupAtom, {...nextPopup, isVisible: true});
        set(popupQueueAtom, restQueue);

        // 设置自动关闭定时器
        if (nextPopup.config.autoClose) {
            setTimeout(() => {
                set(closePopupAtom, nextPopup.id);
            }, nextPopup.config.autoClose);
        }
    } else {
        set(currentPopupAtom, null);
    }
});

// 更新当前弹窗数据的 action atom
export const updateCurrentPopupDataAtom = atom(null, (get, set, data: any) => {
    const current = get(currentPopupAtom);
    if (current) {
        set(currentPopupAtom, {
            ...current,
            data: {...current.data, ...data}
        });
    }
});

// 弹窗成功回调的 action atom
export const popupSuccessAtom = atom(null, (get, set, data?: any) => {
    const current = get(currentPopupAtom);
    if (current?.config.onSuccess) {
        current.config.onSuccess(data);
    }
    set(closePopupAtom);
});

// 清空所有弹窗的 action atom
export const clearAllPopupsAtom = atom(null, (get, set) => {
    set(currentPopupAtom, null);
    set(popupQueueAtom, []);
});

// 获取队列长度的派生 atom
export const popupQueueLengthAtom = atom(get => get(popupQueueAtom).length);

// 快捷方法：连续显示弹窗（用于成功创建档案 -> 历史信息同步的场景）
export const showSequentialPopupsAtom = atom(null, (get, set, configs: PopupConfig[]) => {
    configs.forEach((config, index) => {
        if (index === 0) {
            // 第一个弹窗立即显示
            set(showPopupAtom, config);
        } else {
            // 后续弹窗加入队列
            set(showPopupAtom, config);
        }
    });
});
