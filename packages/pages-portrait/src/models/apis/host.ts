import {getPageBaseUrl} from '../../../../pages-im/src/utils';

const isH5Prod = process.env.TARO_ENV === 'h5' && process.env.NODE_ENV === 'production';

export const H5_HOST = process.env.TARO_APP_H5_HOST;

// 如果是H5环境取当前域名
export const API_HOST = isH5Prod ? getPageBaseUrl() : process.env.TARO_APP_API_HOST;

export const MUZHI_API_HOST = 'https://muzhi-dev.baidu.com';

export const NAV_MINI_APP_VERSION = 'develop'; // 'develop' | 'trial' | 'release'

export const SOCKET_HOST = 'https://pim.baidu.com';

export const SOCFIRE_HOST = 'https://sofire.baidu.com';
