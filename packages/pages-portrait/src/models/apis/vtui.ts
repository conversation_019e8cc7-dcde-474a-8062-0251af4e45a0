/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4699485
 * @description 档案页面解读报告，SSE 流式传输
 */
export const imageAnalyze = '/vtui/ehr/imageanalyze';

/**
 *
 * @link https://yapi.baidu-int.com/project/32495/interface/api/1840666
 * @description 获取Bos Bucket的STS
 */
export const getBucketToken = '/wzcui/uiservice/common/file/getBucketToken';

/**
 *
 * @link https://yapi.baidu-int.com/project/32495/interface/api/1840671
 * @description 上传图片接口
 */
export const uploadImageApi = '/wzcui/uiservice/common/file/uploadImage';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4694362
 * @description 档案首页接口
 */
export const getPatients = '/vtui/ehr/baseinfo';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4695250
 * @description 就诊资料接口
 */
export const getMedicalReports = '/vtui/ehr/medicalreports';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4697110
 * @description 获得就诊人健康史
 */
export const getHealthHistoryApi = '/vtui/ehr/healthhistory';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4697461
 * @description 更新健康史
 */
export const updateHealthHistoryApi = '/vtui/ehr/updatehealthhistory';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4697461
 * @description 获取历史就诊人列表
 */
export const getHistoryPatientsApi = '/vtui/ehr/gethistorypatients';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4697461
 * @description 创建健康档案（已有就诊人）
 */
export const createByHistoryPatientApi = '/vtui/ehr/createbyhistorypatient';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4697461
 * @description 创建健康档案
 */
export const createHealthRecordApi = '/vtui/ehr/create';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4699521
 * @description 报告单提取接口
 */
export const extractReport = '/vtui/ehr/extractreport';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4695254
 * @description 上传就诊资料接口
 */
export const createMedicalReport = '/vtui/ehr/createmedicalreport';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4695252
 * @description 获取就诊资料详情接口
 */
export const getMedicalReportInfo = '/vtui/ehr/medicalreportinfo';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4695257
 * @description 删除就诊资料接口
 */
export const deleteMedicalReport = '/vtui/ehr/deletemedicalreport';

/**
 *
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-46952557
 * @description 修改就诊资料接口
 */
export const updateMedicalReport = '/vtui/ehr/updatemedicalreport';
