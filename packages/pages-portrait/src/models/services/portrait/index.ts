import {API_HOST} from '../../apis/host';
import {getPatients, getMedicalReports} from '../../apis/vtui';
import {utils} from '../../../../../pages-im';
import {PATIENT_ID_DEFAULT} from '../../../constants/portrait';
import {AVATAR_DEFAULT} from '../../../constants/images';
import type {
    GetPatientsResData,
    GetMedicalReportsReqData,
    GetMedicalReportsResData
} from './index.d';

const {httpRequest} = utils;

// FIXME: 临时使用，后续需要替换为正式环境
// const API_HOST = 'https://iapi.baidu-int.com/m1/356132-0-default';

export const getPatientsReq = async (params: {patientId?: string}) => {
    const [err, res] = await httpRequest<GetPatientsResData>({
        url: `${API_HOST}${getPatients}`,
        method: 'POST',
        data: params
    });

    if (err) {
        throw err;
    }
    if (!res || !res.data) {
        throw new Error(`${getPatients} 接口数据返回不符合预期，数据为空`);
    }

    const {patients, options} = res.data;
    // 如果患者列表为空，则塞一个数据用作缺省值
    if (patients.length === 0) {
        patients.push({
            patientId: PATIENT_ID_DEFAULT,
            name: '',
            icon: AVATAR_DEFAULT,
            relationship: '本人',
            age: '',
            birthDate: '',
            isCertified: false,
            isSelf: false,
            createTime: new Date().getTime()
        });
    }

    // 处理成 HFilter 组件需要的配置项格式
    options.reportTags.unshift({
        text: '全部',
        value: ''
    });
    options.reportTags = options.reportTags.map(item => ({
        text: item.text,
        value: item.value,
        key: item.value
    }));

    return res.data;
};

export const getMedicalReportsReq = async (params: GetMedicalReportsReqData) => {
    const [err, res] = await httpRequest<GetMedicalReportsResData>({
        url: `${API_HOST}${getMedicalReports}`,
        method: 'POST',
        data: params
    });

    if (err) {
        throw err;
    }
    if (!res || !res.data) {
        throw new Error(`${getMedicalReports} 接口数据返回不符合预期，数据为空`);
    }

    return res.data;
};
