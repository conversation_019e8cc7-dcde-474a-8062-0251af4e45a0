import type {Patient, MedicalReport} from '../../../typings/patient';
import {IntroPopupData, RelationshipOption, ReportTagOption} from '../../../typings/portrait';
export interface GetPatientsResData {
    historyPatients: Patient[];
    /**
     * 是否登陆
     */
    isLogin: boolean;
    /**
     * 就诊资料
     */
    medicalReports: MedicalReport[];
    options: Options;
    patients: Patient[];
    /**
     * 完善度百分比
     */
    prercentComplete: number;
    /**
     * 当前选中就诊人id
     */
    selectedPatientID: string;
    /**
     * 是否展示有历史就诊人tips
     */
    showHistoryPatientTips: boolean;
    /**
     * 档案功能介绍弹窗数据
     */
    introducePopup: IntroPopupData;
    [property: string]: any;
}

export interface Options {
    relationships: RelationshipOption[];
    reportTags: ReportTagOption[];
    [property: string]: any;
}

export interface GetMedicalReportsReqData {
    patientId: string;
    reportTags: string[];
    pn?: number;
    rn?: number;
}

export interface GetMedicalReportsResData {
    total: number;
    pn: number;
    rn: number;
    medicalReports: MedicalReport[];
}
