import {API_HOST} from '../../apis/host';
import {utils} from '../../../../../pages-im';

const {httpRequest} = utils;

export interface PatientInfoResponse {
    status: number;
    msg: string;
    data: {
        patient: {
            patientId: string;
            relationship: string;
            name: string;
            gender: string;
            age: string;
            birthDate: string;
            height: number;
            weight: number;
            address: string;
            area: string[];
            maritalStatus: string;
            educationLevel: string;
        };
    };
    options: {
        marital: Array<{text: string; value: string}>;
        education: Array<{text: string; value: string}>;
    };
}

export interface DeletePatientResponse {
    status: number;
    msg: string;
}

export interface UpdatePatientRequest {
    patientId: string;
    form: {
        relationship: string;
        name: string;
        gender: string;
        birthDate: string;
        height: number;
        weight: number;
        address: string;
        area: string; // 格式: "北京市,北京市,西城区"
        maritalStatus: string;
        educationLevel: string;
    };
}

export interface UpdatePatientResponse {
    patientId: string;
}

// 获取健康档案就诊人资料
export const getPatientInfo = async (
    patientId: string,
    hasSelf?: boolean | string
): Promise<PatientInfoResponse> => {
    // 处理 hasSelf 参数类型转换
    const normalizedHasSelf = typeof hasSelf === 'string' ? hasSelf === 'true' : hasSelf; // 防御性编程，从url上带入到接口

    const [err, res] = await httpRequest<PatientInfoResponse>({
        url: `${API_HOST}/vtui/ehr/patientinfo`,
        method: 'POST',
        data: {
            patientId,
            hasSelf: normalizedHasSelf
        }
    });

    if (err || !res || res.status !== 0) {
        throw new Error(err?.msg || '获取就诊人信息失败');
    }
    return res;
};

// 删除就诊资料
export const deletePatient = async (patientId: string): Promise<DeletePatientResponse> => {
    const [err, res] = await httpRequest<DeletePatientResponse>({
        url: `${API_HOST}/vtui/ehr/delete`,
        method: 'POST',
        data: {patientId}
    });

    if (err || !res || res.status !== 0) {
        throw new Error(err?.msg || '删除就诊人失败');
    }
    return res;
};

// 更新健康档案就诊人资料
export const updatePatientInfo = async (
    params: UpdatePatientRequest
): Promise<UpdatePatientResponse> => {
    const [err, res] = await httpRequest<UpdatePatientResponse>({
        url: `${API_HOST}/vtui/ehr/updatepatientinfo`,
        method: 'POST',
        data: params
    });

    if (err || !res || res.status !== 0) {
        throw new Error(err?.msg || '更新就诊人信息失败');
    }
    return res;
};
