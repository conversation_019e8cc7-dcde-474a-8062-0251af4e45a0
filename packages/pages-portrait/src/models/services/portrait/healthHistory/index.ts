import {utils} from '../../../../../../pages-im';
import {API_HOST} from '../../../apis/host';
import {getHealthHistoryApi, updateHealthHistoryApi} from '../../../apis/vtui';
import type {
    GetHealthHistoryRequest,
    GetHealthHistoryResponse,
    UpdateHealthHistoryRequest,
    UpdateHealthHistoryResponse
} from './type';

const {httpRequest} = utils;

/**
 * 获取健康史数据
 * @param params 请求参数
 */
export const getHealthHistory = async (params: GetHealthHistoryRequest) => {
    const [err, res] = await httpRequest<GetHealthHistoryResponse>({
        url: `${API_HOST}${getHealthHistoryApi}`,
        method: 'POST',
        data: params
    });

    if (err) {
        throw err;
    }
    if (!res || !res.data) {
        throw new Error('接口数据返回不符合预期，数据为空');
    }

    return res;
};

/**
 * 更新健康史信息
 * @param params 更新参数
 */
export const updateHealthHistory = async (params: UpdateHealthHistoryRequest) => {
    const [err, res] = await httpRequest<UpdateHealthHistoryResponse>({
        url: `${API_HOST}${updateHealthHistoryApi}`,
        method: 'POST',
        data: params
    });

    if (err) {
        throw err;
    }
    if (!res) {
        throw new Error('接口数据返回不符合预期，数据为空');
    }

    return res;
};

const healthHistoryApi = {
    getHealthHistory,
    updateHealthHistory
};

export default healthHistoryApi;
