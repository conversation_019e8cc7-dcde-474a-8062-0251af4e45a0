import {HealthHistory} from '../../../../pages/portrait/components/HealthHistory/types';

// 获取健康史请求参数
export interface GetHealthHistoryRequest {
    /**
     * 就诊人id
     */
    patientId?: string;
}

// 获取健康史响应数据
export interface GetHealthHistoryData {
    /**
     * 健康史
     */
    healthHistory: HealthHistory[];
    /**
     * 就诊人id
     */
    patientId: string;
}

// 获取健康史响应
export interface GetHealthHistoryResponse {
    applid: string;
    data: GetHealthHistoryData;
    lid: string;
    msg: string;
    status: number;
    toast: string;
}

// 更新健康史请求参数
export interface UpdateHealthHistoryRequest {
    /**
     * 过敏史
     */
    allergyHistory?: string;
    /**
     * 生日，yyyy-mm-dd
     */
    birthday?: string;
    /**
     * 家族史
     */
    familyHistory?: string;
    /**
     * 性别
     */
    gender?: string;
    /**
     * 孕产情况
     */
    gestation?: string;
    /**
     * 肾功能
     */
    kidney?: string;
    /**
     * 肝功能
     */
    liver?: string;
    /**
     * 疾病史
     */
    medicalHistory?: string;
    /**
     * 姓名
     */
    name?: string;
    /**
     * 就诊人id
     */
    patientId: string;
}

// 更新健康史响应
export interface UpdateHealthHistoryResponse {
    applid?: string;
    data?: {
        patientId: string;
    };
    lid?: string;
    msg?: string;
    status?: number;
    toast?: string;
}
