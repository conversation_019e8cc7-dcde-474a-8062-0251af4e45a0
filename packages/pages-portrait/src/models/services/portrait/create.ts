import {utils} from '../../../../../pages-im';
import {createHealthRecordApi} from '../../apis/vtui';

const {httpRequest} = utils;

const API_HOST = '';

// 创建健康档案接口请求参数
export interface CreateHealthRecordRequest {
    name: string;
    gender: string;
    birthDate: string;
    relationship: string;
    historyPatientId?: string; // 关联的历史就诊人ID
}

// 创建健康档案接口响应
export interface CreateHealthRecordResponse {
    status: number;
    msg: string;
    toast: string;
    applid: string;
    lid: string;
    data: {
        patientId: string; // 就诊人ID
        needRelation: number; // 需要关联历史就诊人：1-是 0-否
    };
}

// 创建健康档案
export const createHealthRecord = async (params: CreateHealthRecordRequest) => {
    const [err, res] = await httpRequest<CreateHealthRecordResponse>({
        url: `${API_HOST}${createHealthRecordApi}`,
        method: 'POST',
        data: params
    });

    if (err) {
        throw err;
    }

    if (!res || !res.data) {
        throw new Error('创建健康档案接口数据返回不符合预期，数据为空');
    }

    return res.data;
};
