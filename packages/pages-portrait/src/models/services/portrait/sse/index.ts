import {SSEProcessor} from 'sse-kit/lib/bundle.h5.esm';

import {API_HOST} from '../../../../models/apis/host';
import {imageAnalyze} from '../../../apis/vtui';

import editConf from '../../../../../../pages-im/src/utils/basicAbility/comonRequest/utils/editConf';

import type {ReportImageAnalyzeSSEParams, SSEResponseType} from './index.d';

export type SSEProcessorInstance<T extends object> = InstanceType<typeof SSEProcessor<T>>;

export const conversationSSE = async (
    args: ReportImageAnalyzeSSEParams
): Promise<SSEProcessorInstance<SSEResponseType>> => {
    const {params} = args;

    const conf = {
        url: `${API_HOST}${imageAnalyze}`,
        method: 'POST' as const,
        data: params,
        needTransparentWhiteListParams: true
    };
    const decoratedConf = await editConf(conf);

    return new SSEProcessor<SSEResponseType>({
        url: decoratedConf.url as `https://${string}`,
        method: 'POST',
        enableConsole: false,
        headers: decoratedConf.header as Headers,
        reqParams: decoratedConf.data
    });
};
