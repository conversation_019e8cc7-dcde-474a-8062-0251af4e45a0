export type MsgId = string; // 消息 id
export type SessionId = string;

export type SceneTypeOfParams =
    | 'wzBotConversation'
    | `wzBotCapsule_${string}`
    | 'wzBotFreeService'
    | `imWelcomeModule_${string}`
    | 'inputAudio'
    | 'inputImg'
    | 'imFlowOptions'
    | 'wzBotFreeService'
    | 'queryFirstCall'
    | 'defaultFirstCall'
    | 'unknown'
    | 'uploadImg'
    | 'submitAiForm'
    | 'portraitUpload';

export interface ReportImageAnalyzeSSEParams {
    params: {
        patientId: string;
        medicalReportId: string;
    };
}

export interface CardStyle {
    renderType?: number;
}

export type InteractionType = string;

export interface InteractionInfo {
    intent?: string;
    url?: string;
    sceneType?: string;
    method?: 'GET' | 'POST';
    contentType?: string;
    type?: string;
    fail?: () => void;
    success?: () => void;
}

export interface ActionInfo {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

export interface CardsData<T> {
    cardStyle?: CardStyle;
    actionInfo?: ActionInfo;
    content: T;
    ext?: {
        [key: string]: string | number;
    };
}

export interface Plan {
    id: string;
    name: string;
    status: 'completed' | 'pending';
    description: string;
}

export interface ImageAnalyzeList {
    sectionId: number;
    type: string;
    isFinish: boolean;
    planIcon: string;
    planProgressDesc: string;
    plan: Plan[];
}

export interface ImageAnalyzeContent {
    list: ImageAnalyzeList[];
}

export type SSEResponseType = CardsData<ImageAnalyzeContent>;
