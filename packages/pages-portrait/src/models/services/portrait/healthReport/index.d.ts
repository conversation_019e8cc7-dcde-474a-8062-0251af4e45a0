import {ReportTag} from '../../../../typings/upload';

export interface ExtractReportParams {
    patientId: string;
    objectIds: string[];
}

export interface Images {
    thumb: string; // 缩略图
    origin: string; // 原图
}

export interface IList {
    objectId: string;
    reportTime: string;
    hospitalName: string;
    reportTags: ReportTag[];
    image: Images[];
    remark?: string;
}

export interface GetExtractReportResType {
    list: IList[];
}

export interface MedicalReports {
    medicalReportId?: string;
    objectId: string;
    createTime?: string;
    reportTime?: string;
    hospitalName?: string;
    reportTags: string[];
    reportContent?: {
        cardStyle?: {
            renderType?: number;
        };
        content?: string;
        planIcon?: string;
        planProgressDesc?: string;
    };
    comments?: string;
    images?: Images[];
}

export interface MedicalReportParams {
    patientId: string;
    medicalReports: MedicalReports[];
}

export interface IMedicalReports {
    medicalReportId: string;
    isSuccess: boolean;
    objectId: string;
}

export interface CreateMedicalReportResType {
    successCnt: number;
    failureCnt: number;
    medicalReports: IMedicalReports[];
}

export interface OperateMedicalReportParams {
    patientId: string;
    medicalReportId: string;
}

export interface MedicalReportInfoResType {
    medicalReport: MedicalReports;
}

export interface IForm {
    medicalReportId: string;
    reportTime?: string;
    hospitalName?: string;
    reportTags: string[];
    comments?: string;
}
export interface UpdateMedicalReportParams {
    patientId: string;
    form: IForm;
}
