import {utils} from '../../../../../../pages-im';
import {API_HOST} from '../../../apis/host';
import {
    createMedicalReport,
    deleteMedicalReport,
    extractReport,
    getMedicalReportInfo,
    updateMedicalReport
} from '../../../apis/vtui';
import {
    MedicalReportParams,
    CreateMedicalReportResType,
    OperateMedicalReportParams,
    ExtractReportParams,
    GetExtractReportResType,
    MedicalReportInfoResType,
    UpdateMedicalReportParams
} from './index.d';

const {httpRequest} = utils;

export const getExtractReport = async (params: ExtractReportParams) => {
    const [err, res] = await httpRequest<GetExtractReportResType>({
        url: `${API_HOST}${extractReport}`,
        method: 'POST',
        data: params,
        timeout: 30000
    });

    if (err) {
        throw err;
    }
    if (!res || !res.data) {
        throw new Error(`${getExtractReport} 接口数据返回不符合预期，数据为空`);
    }

    return res.data;
};

export const getCreateMedicalReport = async (params: MedicalReportParams) => {
    const [err, res] = await httpRequest<CreateMedicalReportResType>({
        url: `${API_HOST}${createMedicalReport}`,
        method: 'POST',
        data: params
    });

    if (err) {
        throw err;
    }
    if (!res || !res.data) {
        throw new Error(`${getCreateMedicalReport} 接口数据返回不符合预期，数据为空`);
    }

    return res.data;
};

export const getDeleteMedicalReport = async (params: OperateMedicalReportParams) => {
    const [err, res] = await httpRequest<any>({
        url: `${API_HOST}${deleteMedicalReport}`,
        method: 'POST',
        data: params
    });

    if (err) {
        throw err;
    }
    if (!res) {
        throw new Error(`${getDeleteMedicalReport} 接口数据返回不符合预期，数据为空`);
    }

    return res.status;
};

export const getMedicalReportDetail = async (params: OperateMedicalReportParams) => {
    const [err, res] = await httpRequest<MedicalReportInfoResType>({
        url: `${API_HOST}${getMedicalReportInfo}`,
        method: 'POST',
        data: params
    });

    if (err) {
        throw err;
    }
    if (!res || !res.data) {
        throw new Error(`${getMedicalReportDetail} 接口数据返回不符合预期，数据为空`);
    }

    return res.data;
};

export const getUpdateMedicalReport = async (params: UpdateMedicalReportParams) => {
    const [err, res] = await httpRequest<any>({
        url: `${API_HOST}${updateMedicalReport}`,
        method: 'POST',
        data: params
    });

    if (err) {
        throw err;
    }
    if (!res) {
        throw new Error(`${updateMedicalReport} 接口数据返回不符合预期，数据为空`);
    }

    return res.status;
};
