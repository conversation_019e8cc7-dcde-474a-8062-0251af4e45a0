import {utils} from '../../../../../pages-im';
import {Patient} from '../../../typings/patient';
import {API_HOST} from '../../apis/host';
import {createByHistoryPatientApi, getHistoryPatientsApi} from '../../apis/vtui';
const {httpRequest} = utils;

// 接口类型定义
export interface GetHistoryPatientsResponse {
    status: number;
    msg: string;
    toast: string;
    applid: string;
    lid: string;
    data: {
        patients: Patient[];
    };
}

export interface CreateByHistoryPatientRequest {
    patients: {
        patientId: string;
        relationship?: string;
        name: string;
        gender: string;
        birthDate: string;
    }[];
}

export interface CreateByHistoryPatientResponse {
    status: number;
    msg: string;
    toast: string;
    applid: string;
    lid: string;
    data: {
        patients: {
            patientId: string;
            isSuccess: number; // 0-创建失败 1-创建成功
        }[];
    };
}

/**
 * 获取历史就诊人列表
 * @param params 请求参数，可包含num参数（默认值为9）
 */
export const getHistoryPatients = async (params: {num?: number} & Record<string, any>) => {
    const requestParams = {
        num: 9,
        ...params
    };

    const [err, res] = await httpRequest<GetHistoryPatientsResponse>({
        url: `${API_HOST}${getHistoryPatientsApi}`,
        method: 'POST',
        data: requestParams
    });

    if (err) {
        throw err;
    }

    if (!res || !res.data) {
        throw new Error('获取历史就诊人列表接口数据返回不符合预期，数据为空');
    }

    return res.data;
};

/**
 * 创建健康档案（已有就诊人）
 */
export const createByHistoryPatient = async (params: CreateByHistoryPatientRequest) => {
    const [err, res] = await httpRequest<CreateByHistoryPatientResponse>({
        url: `${API_HOST}${createByHistoryPatientApi}`,
        method: 'POST',
        data: params
    });

    if (err) {
        throw err;
    }

    if (!res || !res.data) {
        throw new Error('创建健康档案接口数据返回不符合预期，数据为空');
    }

    return res.data;
};
