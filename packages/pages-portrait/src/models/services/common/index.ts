import {uploadFile} from '@tarojs/taro';
import httpRequest from '../../../../../pages-im/src/utils/basicAbility/comonRequest/common';
import {API_HOST} from '../../../models/apis/host';
import {getBucketToken, uploadImageApi} from '../../apis/vtui';

export interface IBucketDataProps {
    accessKeyID?: string;
    secretAccessKey?: string;
    sessionToken?: string;
    bucket?: string;
    endPoint?: string;
    fileNames?: string[];
    snapshotURLs?: string[];
    mapFileUrls?: Record<string, Record<'icon' | 'origin' | 'small', string>>;
}

/**
 *
 * @description 获取上传图片token
 * @param params
 * @returns
 */
export const getBosToken = async (params: {bucketConfName?: string; fileNum?: number}) => {
    return await httpRequest<IBucketDataProps>({
        url: `${API_HOST}${getBucketToken}`,
        method: 'POST',
        data: params,
        isNeedLogin: false,
        isFirstScreen: false
    });
};

export const uploadPicToApiService = async (filePath: string, bucketConfName = 'default') => {
    return new Promise((resolve, reject) => {
        uploadFile({
            url: `${API_HOST}${uploadImageApi}?bucketConfName=${bucketConfName}`,
            filePath,
            name: 'file',
            success: res => {
                resolve(res?.data);
            },
            fail: err => {
                reject(err);
            }
        });
    });
};
