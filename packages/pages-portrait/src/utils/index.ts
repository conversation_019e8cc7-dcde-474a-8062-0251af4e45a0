/**
 * 获取H5当前的协议+域名
 */
export function getPageBaseUrl() {
    let baseURL = '';
    if (!window.location.origin) {
        // 兼容IE，IE11版本下location.origin为undefined
        baseURL = `${window.location.protocol}//${window.location.hostname}${
            window.location.port ? `:${window.location.port}` : ''
        }`;
    } else {
        baseURL = window.location.origin;
    }

    return baseURL;
}

// 自定义存储适配器，支持不同环境
export const createStorageAdapter = () => {
    if (process.env.TARO_ENV === 'h5') {
        // H5环境
        return {
            getItem: (key: string) => {
                try {
                    const value = localStorage.getItem(key);
                    return value ? JSON.parse(value) : null;
                } catch {
                    return null;
                }
            },
            setItem: (key: string, value: any) => {
                try {
                    localStorage.setItem(key, JSON.stringify(value));
                } catch (error) {
                    console.warn('Failed to save to localStorage:', error);
                }
            },
            removeItem: (key: string) => {
                try {
                    localStorage.removeItem(key);
                } catch (error) {
                    console.warn('Failed to remove from localStorage:', error);
                }
            }
        };
    } else if (process.env.TARO_ENV === 'swan') {
        // 小程序环境
        return {
            getItem: (key: string) => {
                return swan.getStorageSync(key);
            },
            setItem: (key, value) => {
                swan.setStorageSync(key, value);
            },
            removeItem: (key: string) => {
                swan.removeStorageSync(key);
            }
        };
    }

    return {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {}
    };
};

// 计算BMI
export const calculateBMI = (height: number, weight: number) => {
    return Math.round((weight / Math.pow(height / 100, 2)) * 100) / 100;
};
