{"status": 0, "msg": "", "toast": "", "applid": "3554303548", "lid": "", "data": {"msgIds": ["1962854939417149440", "U_bb293447-9802-4e7a-aba6-67dffcd9c02d", "1962855036615950336", "1962855039619072000", "1962855352530927616", "1962855381622620160", "1962855383690412032"], "msgData": {"1962854939417149440": {"type": "dynamic", "meta": {"sessionId": "e37b7e8fb177076c15ce44392b1a61d3_g_e03e095890c3b485", "msgId": "1962854939417149440", "ownerType": 2, "showPosterRole": 4, "createTime": "1756816060430", "sendTime": "1756816060296", "posterInfo": {"name": "", "avatar": "https://med-fe.cdn.bcebos.com/wz/assistant.png", "title": "", "disableClickOut": false}, "rounds": 0, "passthroughData": ""}, "data": {"action": "end", "content": {"cardId": 11412, "cardName": "ImFlow", "data": {"cardStyle": {"renderType": 1}, "content": {"list": [{"sectionId": 0, "content": "晚上好，我是您的AI健康管家。可以为您提供**免费健康咨询**、**医学报告解读**、**专家医生推荐**等医疗服务，请问您有什么想咨询的？", "type": "markdown", "isFinish": true, "isMockTypeWriter": false}]}, "ext": {"talkId": "e37b7e8fb177076c15ce44392b1a61d3_b704f6645c30c0a0", "contentTrack": {}}}}, "searchReferences": {"isFinish": false}, "feature": {"like": {"actionInfo": {"interaction": "popup", "interactionInfo": {"url": "/vtui/useraction/commonaction?action=attitude", "method": "post", "popupInfo": {"type": "likeFeedback", "content": {"input": {"value": "", "placeholder": "期待你的反馈和意见让我持续成长"}, "title": "😊 期待你的反馈", "content": [{"subTitle": "", "items": [{"value": "可用/不卡顿"}, {"value": "回答准确"}, {"value": "回答符合需要"}, {"value": "信息及时/新颖"}, {"value": "回答全面"}, {"value": "排版清晰"}, {"value": "回答速度快"}, {"value": "安全可信"}]}], "submitBtn": {"interaction": "request", "interactionInfo": {"url": "/vtui/useraction/commonaction?action=attitude", "method": "post"}, "value": "确认"}}}}}}, "dislike": {"actionInfo": {"interaction": "popup", "interactionInfo": {"url": "/vtui/useraction/commonaction?action=attitude", "method": "post", "popupInfo": {"type": "dislikeFeedback", "content": {"input": {"value": "", "placeholder": "期待你的反馈和意见让我持续成长"}, "title": "😊 期待你的反馈", "content": [{"subTitle": "", "items": [{"value": "繁忙/不可用"}, {"value": "回答错误/虚假"}, {"value": "回答不相关"}, {"value": "信息过时"}, {"value": "回答不全面"}, {"value": "排版不清晰"}, {"value": "回答速度慢"}, {"value": "有害/不安全"}]}], "submitBtn": {"interaction": "request", "interactionInfo": {"url": "/vtui/useraction/commonaction?action=attitude", "method": "post"}, "value": "确认"}}}}}}, "tts": {"speaker": "4148"}, "copyInfo": {"enable": true}}}}, "1962855036615950336": {"type": "static", "meta": {"sessionId": "e37b7e8fb177076c15ce44392b1a61d3_g_e03e095890c3b485", "msgId": "1962855036615950336", "ownerType": 3, "showPosterRole": 1, "createTime": "1756816083628", "sendTime": "1756816083470", "posterInfo": {"name": "", "avatar": "", "title": "", "disableClickOut": false}, "rounds": 0, "passthroughData": ""}, "data": {"action": "end", "content": {"cardId": 11402, "cardName": "ImText", "data": {"cardStyle": {"needHead": true, "renderType": 0}, "content": {"value": "我想免费问医生"}, "ext": {"talkId": "e37b7e8fb177076c15ce44392b1a61d3_b704f6645c30c0a0"}}}, "searchReferences": {"isFinish": false}, "feature": {}}}, "1962855039619072000": {"type": "dynamic", "meta": {"sessionId": "e37b7e8fb177076c15ce44392b1a61d3_g_e03e095890c3b485", "msgId": "1962855039619072000", "ownerType": 2, "showPosterRole": 4, "createTime": "1756816088332", "sendTime": "1756816084186", "posterInfo": {"name": "", "avatar": "https://med-fe.cdn.bcebos.com/wz/assistant.png", "title": "", "disableClickOut": false}, "rounds": 0, "passthroughData": ""}, "data": {"action": "end", "content": {"cardId": 11412, "cardName": "ImFlow", "data": {"cardStyle": {"renderType": 1}, "content": {"list": [{"sectionId": 1, "content": "好的，可以描述一下你的病情症状、性别、年龄、城市等信息，我为你推荐合适的医生", "type": "markdown", "isFinish": false, "isMockTypeWriter": false}]}, "ext": {"talkId": "e37b7e8fb177076c15ce44392b1a61d3_b704f6645c30c0a0", "contentTrack": {}}}}, "searchReferences": {"isFinish": false}, "feature": {"like": {"actionInfo": {"interaction": "popup", "interactionInfo": {"url": "/vtui/useraction/commonaction?action=attitude", "method": "post", "popupInfo": {"type": "likeFeedback", "content": {"input": {"value": "", "placeholder": "期待你的反馈和意见让我持续成长"}, "title": "😊 期待你的反馈", "content": [{"subTitle": "", "items": [{"value": "可用/不卡顿"}, {"value": "回答准确"}, {"value": "回答符合需要"}, {"value": "信息及时/新颖"}, {"value": "回答全面"}, {"value": "排版清晰"}, {"value": "回答速度快"}, {"value": "安全可信"}]}], "submitBtn": {"interaction": "request", "interactionInfo": {"url": "/vtui/useraction/commonaction?action=attitude", "method": "post"}, "value": "确认"}}}}}}, "dislike": {"actionInfo": {"interaction": "popup", "interactionInfo": {"url": "/vtui/useraction/commonaction?action=attitude", "method": "post", "popupInfo": {"type": "dislikeFeedback", "content": {"input": {"value": "", "placeholder": "期待你的反馈和意见让我持续成长"}, "title": "😊 期待你的反馈", "content": [{"subTitle": "", "items": [{"value": "繁忙/不可用"}, {"value": "回答错误/虚假"}, {"value": "回答不相关"}, {"value": "信息过时"}, {"value": "回答不全面"}, {"value": "排版不清晰"}, {"value": "回答速度慢"}, {"value": "有害/不安全"}]}], "submitBtn": {"interaction": "request", "interactionInfo": {"url": "/vtui/useraction/commonaction?action=attitude", "method": "post"}, "value": "确认"}}}}}}, "tts": {"speaker": "4148"}, "copyInfo": {"enable": true}}}}, "1962855352530927616": {"type": "static", "meta": {"sessionId": "e37b7e8fb177076c15ce44392b1a61d3_g_e03e095890c3b485", "msgId": "1962855352530927616", "ownerType": 3, "showPosterRole": 1, "createTime": "1756816159040", "sendTime": "1756816158789", "posterInfo": {"name": "", "avatar": "", "title": "", "disableClickOut": false}, "rounds": 0, "passthroughData": ""}, "data": {"action": "end", "content": {"cardId": 11402, "cardName": "ImText", "data": {"cardStyle": {"needHead": true, "renderType": 0}, "content": {"value": "20岁男，头疼"}, "ext": {"talkId": "e37b7e8fb177076c15ce44392b1a61d3_b704f6645c30c0a0"}}}, "searchReferences": {"isFinish": false}, "feature": {}}}, "1962855381622620160": {"type": "static", "meta": {"sessionId": "e37b7e8fb177076c15ce44392b1a61d3_g_e03e095890c3b485", "msgId": "1962855381622620160", "ownerType": 1, "showPosterRole": 4, "createTime": "1756816165968", "sendTime": "1756816165725", "posterInfo": {"name": "", "avatar": "", "title": "", "disableClickOut": false}, "rounds": 0, "passthroughData": ""}, "data": {"action": "end", "content": {"cardId": 11407, "cardName": "ImSystemMsg", "data": {"cardStyle": {"renderType": 0}, "content": {"list": [[{"type": "text", "value": "病情信息根据历史对话生成，您可进行修改"}]]}, "ext": {"talkId": "e37b7e8fb177076c15ce44392b1a61d3_b704f6645c30c0a0"}}}, "searchReferences": {"isFinish": false}, "feature": {}}}, "1962855383690412032": {"type": "static", "meta": {"sessionId": "e37b7e8fb177076c15ce44392b1a61d3_g_e03e095890c3b485", "msgId": "1962855383690412032", "ownerType": 1, "showPosterRole": 1, "createTime": "**********", "sendTime": "0", "posterInfo": {"name": "", "avatar": "", "title": "", "disableClickOut": false}, "rounds": 0, "passthroughData": ""}, "data": {"action": "end", "content": {"cardId": 11602, "cardName": "ImAIRecommendUnDirect", "data": {"cardStyle": {"renderType": 3}, "content": {"isExpired": false, "lineText": "", "collectedInfo": {"curPatient": {"contactId": "596991734952015383", "name": "", "age": "20", "gender": "男", "isCertified": 0}, "patientList": [{"contactId": "596991734952015383", "name": "", "age": "20", "gender": "男", "isCertified": 0}, {"contactId": "596991742280172211", "name": "孙海雷", "age": "39", "gender": "男", "isCertified": 0}, {"contactId": "596991718707503983", "name": "啊啊啊啊", "age": "1-7", "gender": "女", "isCertified": 0}, {"contactId": "596991717491436519", "name": "撒的发生的", "age": "25", "gender": "男", "isCertified": 0}, {"contactId": "596991717491200218", "name": "1", "age": "24", "gender": "女", "isCertified": 0}, {"contactId": "596991717398120485", "name": "1", "age": "24", "gender": "女", "isCertified": 2}, {"contactId": "596991717394500863", "name": "sad", "age": "21", "gender": "男", "isCertified": 0}, {"contactId": "596991698742271975", "name": "撒的发生的", "age": "20", "gender": "男", "isCertified": 0}, {"contactId": "596991698734277608", "name": "测试22332", "age": "25", "gender": "男", "isCertified": 0}, {"contactId": "596991698665812273", "name": "ce11111", "age": "21", "gender": "女", "isCertified": 0}, {"contactId": "596991692706784887", "name": "是是", "age": "23", "gender": "男", "isCertified": 2}, {"contactId": "596991692706665901", "name": "闪电", "age": "24", "gender": "女", "isCertified": 2}, {"contactId": "596991692706414750", "name": "说的的", "age": "23", "gender": "女", "isCertified": 0}, {"contactId": "596991691735229488", "name": "ddd", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991691134858617", "name": "的是", "age": "23", "gender": "女", "isCertified": 0}, {"contactId": "596991691134745322", "name": "但是", "age": "24", "gender": "女", "isCertified": 0}, {"contactId": "596991691060495502", "name": "高11", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991690981006919", "name": "是的是的", "age": "25", "gender": "女", "isCertified": 0}, {"contactId": "596991690979388943", "name": "是我是我", "age": "27", "gender": "男", "isCertified": 0}, {"contactId": "596991690978986601", "name": "3232233", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991690978790862", "name": "哦哦", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991690978153443", "name": "oooo", "age": "25", "gender": "男", "isCertified": 0}, {"contactId": "596991690968658441", "name": "00000", "age": "30", "gender": "男", "isCertified": 0}, {"contactId": "596991690967642719", "name": "刘雷", "age": "34", "gender": "男", "isCertified": 0}, {"contactId": "596991690963653252", "name": "哈哈1", "age": "24", "gender": "男", "isCertified": 0}, {"contactId": "596991690961528417", "name": "测试222", "age": "28", "gender": "男", "isCertified": 0}, {"contactId": "596991690961156516", "name": "刘大庆", "age": "29", "gender": "男", "isCertified": 0}, {"contactId": "596991690882092161", "name": "测试1", "age": "26", "gender": "男", "isCertified": 0}, {"contactId": "596991690808237552", "name": "12221212121", "age": "28", "gender": "男", "isCertified": 0}, {"contactId": "596991688560041462", "name": "光头强", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991688018874227", "name": "王大力", "age": "24", "gender": "女", "isCertified": 0}, {"contactId": "596991687943735582", "name": "问问", "age": "25", "gender": "女", "isCertified": 0}, {"contactId": "596991687943341883", "name": "说的", "age": "24", "gender": "男", "isCertified": 0}, {"contactId": "596991687943312443", "name": "23232323", "age": "24", "gender": "女", "isCertified": 2}, {"contactId": "596991687943261693", "name": "12122", "age": "25", "gender": "女", "isCertified": 2}, {"contactId": "596991687943158552", "name": "12212", "age": "25", "gender": "女", "isCertified": 2}, {"contactId": "596991687329080602", "name": "测试就诊人", "age": "34", "gender": "女", "isCertified": 0}, {"contactId": "596991687253392868", "name": "iiii", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991687168863855", "name": "22333", "age": "25", "gender": "女", "isCertified": 2}, {"contactId": "596991687167900016", "name": "实打实", "age": "25", "gender": "女", "isCertified": 0}, {"contactId": "596991687164322795", "name": "mm", "age": "25", "gender": "女", "isCertified": 0}, {"contactId": "596991687160774057", "name": "22333223", "age": "24", "gender": "女", "isCertified": 2}, {"contactId": "596991687160512387", "name": "2222", "age": "25", "gender": "女", "isCertified": 2}, {"contactId": "596991687158266774", "name": "888", "age": "25", "gender": "女", "isCertified": 2}, {"contactId": "596991687155984356", "name": "32322", "age": "25", "gender": "女", "isCertified": 2}, {"contactId": "596991687155894924", "name": "333", "age": "25", "gender": "女", "isCertified": 2}, {"contactId": "596991686917489018", "name": "111", "age": "25", "gender": "女", "isCertified": 2}, {"contactId": "596991686658915454", "name": "7777", "age": "35", "gender": "男", "isCertified": 2}, {"contactId": "596991686648799289", "name": "21111111111111111111", "age": "25", "gender": "女", "isCertified": 2}, {"contactId": "596991686647700322", "name": "222", "age": "25", "gender": "女", "isCertified": 2}, {"contactId": "596991686645717982", "name": "222", "age": "2-2", "gender": "男", "isCertified": 2}, {"contactId": "596991686311237153", "name": "00", "age": "35", "gender": "男", "isCertified": 2}, {"contactId": "596991686310338242", "name": "11", "age": "61", "gender": "女", "isCertified": 2}, {"contactId": "596991686307594741", "name": "ce", "age": "61", "gender": "女", "isCertified": 0}, {"contactId": "596991686225048391", "name": "dd", "age": "61", "gender": "女", "isCertified": 0}, {"contactId": "596991686223927848", "name": "阿萨德", "age": "79", "gender": "女", "isCertified": 0}, {"contactId": "596991686222852476", "name": "12212", "age": "61", "gender": "女", "isCertified": 2}, {"contactId": "596991686208711483", "name": "是是", "age": "61", "gender": "女", "isCertified": 0}, {"contactId": "596991685438674246", "name": "但颠", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991685344529636", "name": "99999999", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991685008797282", "name": "666", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991684995123444", "name": "0000", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991684910102172", "name": "16464646", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991684908414617", "name": "别人都", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991684906341804", "name": "引导测试", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991684844603111", "name": "8888", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991684743019526", "name": "测试扫码", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991684741640188", "name": "66666", "age": "22", "gender": "男", "isCertified": 0}, {"contactId": "596991684235477582", "name": "", "age": "16", "gender": "女", "isCertified": 0}, {"contactId": "596991678796946601", "name": "sdsdd", "age": "22", "gender": "男", "isCertified": 0}], "qid": "2186371660197004", "clinicalDesc": "20岁男性，主诉头疼。", "statusText": {"collectionComplete": {"text": "根据病情为您推荐相关医生"}, "patientCollecting": {"text": "正在整理您的就诊人信息..."}, "patientFail": {"text": "请选择/输入就诊人..."}, "questionCollecting": {"text": "正在整理您的病情信息..."}, "questionFail": {"text": "未识别您的病情信息..."}}}, "skuData": {"titleImg": "极速问医生·60s响应", "leftIcon": "https://med-fe.cdn.bcebos.com/wenzhen-mini-app/rectangle.png", "subText": "", "topArea": null, "btnInfo": null, "tipText": "", "toastTip": "您当前免费咨询次数已用完，可购买其他服务咨询医生", "serviceTip": "", "list": [{"card": "6", "detailInfo": {"skuId": "2107079850461184", "type": "cardDetail", "title": "服务详情", "doctorTitle": "免费咨询", "explainTitle": "服务说明", "oldPrice": "3", "finalPrice": "0", "discount": "3", "couponReductionPrice": "3", "btn": "立即咨询"}, "skuId": "2107079850461184", "skuCode": "", "title": "免费咨询", "priceText": "￥0", "sub": "真人医生，15分钟一对一服务", "couponInfo": {}, "btn": {"value": "去咨询", "interaction": "request", "interactionInfo": {"url": "/wzcui/uiservice/order/makeorderwithcheck?atn=&coupon_select=64472227629484&entrance=newbot_fdxsku&from=cbot_wz&qid=2186371660197004&sku_id=2107079850461184&version=v3"}}, "salePrice": 300, "promotionPrice": 0, "titleIcon": [{"url": "https://med-fe.cdn.bcebos.com/wenzhen-mini-app/exclusive_subsidy.png", "type": "img"}]}, {"card": "6", "detailInfo": {"skuId": "2060350606280704", "type": "cardDetail", "title": "服务详情", "doctorTitle": "三甲医生", "explainTitle": "服务说明", "oldPrice": "0.03", "finalPrice": "0.03", "btn": "立即咨询"}, "skuId": "2060350606280704", "skuCode": "", "title": "三甲医生", "priceText": "￥0.03", "sub": "公立三甲医生 平均1分钟响应", "couponInfo": {}, "btn": {"value": "去咨询", "interaction": "request", "interactionInfo": {"url": "/wzcui/uiservice/order/makeorderwithcheck?atn=&entrance=newbot_fdxsku&from=cbot_wz&qid=2186371660197004&sku_id=2060350606280704&version=v3"}}, "salePrice": 3, "promotionPrice": 3}], "buttonPrice": null, "tagArr": null}, "maxShowLen": 3}, "ext": {"scene": "wzBotConversation", "talkId": "e37b7e8fb177076c15ce44392b1a61d3_b704f6645c30c0a0"}}}, "searchReferences": {"isFinish": false}, "feature": {}}}, "U_bb293447-9802-4e7a-aba6-67dffcd9c02d": {"type": "static", "meta": {"sessionId": "e37b7e8fb177076c15ce44392b1a61d3_g_e03e095890c3b485", "msgId": "U_bb293447-9802-4e7a-aba6-67dffcd9c02d", "ownerType": 1, "showPosterRole": 5, "createTime": "1756870443320", "sendTime": "1756870443320", "posterInfo": {"name": "", "avatar": "", "title": "", "disableClickOut": false}, "rounds": 0, "passthroughData": ""}, "data": {"action": "end", "content": {"cardId": 11407, "cardName": "ImSystemMsg", "data": {"cardStyle": {"renderType": 0}, "content": {"list": [[{"type": "text", "value": "继续沟通代表您已阅读并同意"}, {"type": "highLightText", "value": "《服务声明》", "interaction": "openLink", "interactionInfo": {"url": "/wenzhen/pages/common/agreementList/index"}}]]}, "ext": {}}}, "searchReferences": {"isFinish": false}, "feature": {}}}}, "hasMore": false}}